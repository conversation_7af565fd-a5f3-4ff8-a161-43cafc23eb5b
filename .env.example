NODE_ENV=development

# Server configuration
NGINX_HOST=localhost
SERVER_PORT=3000
MONO_SESSION=true

# Database configuartion
DB_PASSWORD=propertyListing@2024
DB_USERNAME=postgres
DB_NAME=propertyListing
DB_PORT=5432
DB_HOST=localhost

# Redis configs
REDIS_URI=redis://@localhost:6379

# JWT Configuration
JWT_SECRET=e86c66879d534b5ad86050a6a32e2262e5695c229abea49c665cf784329aead5
JWT_TOKEN_AUDIENCE=localhost:3000
JWT_TOKEN_ISSUER=localhost:3000
JWT_ACCESS_TOKEN_TTL=3600

# AWS Configs
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ZHGHySdi4vIR9+Jqi0WrDF0DjdCKzqxVhIrFAbpj
AWS_BUCKET_NAME=property-development
AWS_BUCKET_REGION=ap-southeast-1

# SMTP
AWS_SES_SMTP_USER=********************
AWS_SES_SMTP_PASS=BNBTJCpmw4yZtcE4Kd8f6FeX9X4HO8VKaqAcQB7PQ1jU
AWS_SES_ENDPOINT=email-smtp.ap-southeast-1.amazonaws.com
AWS_SES_SENDER=<EMAIL>

# Router
ADMIN_LINK=https://admin.staging.project.sg
CLIENT_LINK=https://admin.staging.project.sg

BASE_DOMAIN=staging.project.sg

# Telegram bot (this is local bot)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_BOT_NAME=https://t.me/property_local_tele_bot

UMAMI_USER=admin
UMAMI_PASSWORD=umami
UMAMI_HOST=http://localhost:3000

XERO_CLIENT_ID=B693929B79BC4EF8B10FE09F45CF7580
XERO_CLIENT_SECRET=YMmPv0J-XBonv7DJ7gja4fHsHEDTzdIiXSahPQdFEix1UuFN
XERO_REDIRECT_URI=https://2588-115-73-208-22.ngrok-free.app/xero-payment/callback
XERO_WEBHOOK_KEY=+WiY/t89F0Xz0FH764L1KmgTT7DUgDW8/16QCxVyF7+w3KtwAb424fFJU1TOMBZRfEygabqu+cWa3x6qruKrNA==

CHROME_PATH=/usr/bin/google-chrome

# 32 bytes
SECRET_KEY="23dff2dnb3#$JIOmcjksokkkkdf3dhe"
# 16bytes
SECRET_KEY_IV="1234567890123456"

HCAPTCHA_SECRET="ES_e7da97dcfc9e4de6b74d224d68a586ff"

# SUFIX DOMAIN
SUFFIX_DOMAIN="staging.project.sg"