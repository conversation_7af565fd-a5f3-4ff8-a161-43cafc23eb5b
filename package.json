{"name": "property-listing-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "export node_options=--max_old_space_size=4096 && nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "typeorm": "npm run build && ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/core/configs/typeorm.config.ts", "migration:generate": "npm run typeorm -- -d ./src/core/configs/typeorm.config.ts migration:generate ./migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./src/core/configs/typeorm.config.ts migration:revert", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.583.0", "@nestjs/axios": "^3.1.3", "@nestjs/bull": "^10.2.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.1", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^10.0.2", "@types/bcrypt": "^5.0.2", "@umami/api-client": "^0.76.0", "archiver": "^7.0.1", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bull": "^4.12.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.0.0", "geoip-lite": "^1.4.10", "hcaptcha": "^0.2.0", "ioredis": "^5.4.1", "jsdom": "^24.1.0", "json2csv": "^6.0.0-alpha.2", "lodash": "^4.17.21", "mjml": "^4.15.3", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mustache": "^4.2.0", "nestjs-pino": "^4.0.0", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.9.14", "object-hash": "^3.0.0", "pg": "^8.11.5", "pino-http": "^10.1.0", "pino-pretty": "^11.0.0", "progress": "^2.0.3", "puppeteer-core": "^24.7.1", "reflect-metadata": "^0.2.0", "rss": "^1.2.2", "rxjs": "^7.8.1", "sanitize-html": "^2.17.0", "sharp": "^0.32.6", "typeorm": "^0.3.20", "ua-parser-js": "^1.0.39", "xero-node": "^9.3.0-alpha.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/geoip-lite": "^1.4.4", "@types/jest": "^29.5.2", "@types/jsdom": "^21.1.7", "@types/json2csv": "^5.0.7", "@types/lodash": "^4.17.4", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/node-telegram-bot-api": "^0.64.7", "@types/object-hash": "^3.0.6", "@types/progress": "^2.0.7", "@types/supertest": "^6.0.0", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}