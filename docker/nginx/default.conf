# Nginx configuration

server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name localhost;

    # index index.html;
    # error_log  /var/log/nginx/error.log;
    # access_log /var/log/nginx/access.log;
    # root /var/www/html;

    location / {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://node:3000;
    }
}
