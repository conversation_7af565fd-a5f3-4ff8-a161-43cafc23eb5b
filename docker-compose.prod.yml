version: "3.8"

services:
    nginx:
        build:
            context: .
            dockerfile: ./docker/nginx/Dockerfile
        container_name: nginx
        volumes:
            - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
        environment:
            - NGINX_HOST=${NGINX_HOST}
        ports:
            - 80:80
        restart: always
        networks:
            - frontend
        depends_on:
            - node

    node:
        build:
            context: .
            dockerfile: ./docker/node/Dockerfile
            target: production
        container_name: node
        ports:
            - ${SERVER_PORT}:${SERVER_PORT}
            - 9229:9229
        env_file:
            - .env
        volumes:
            - .:/usr/src/app
            - /usr/src/app/node_modules
        restart: always
        networks:
            - frontend
            - backend
        command: npm run start:dev
        depends_on:
            - redis
            - postgres

    redis:
        container_name: redis
        build: ./docker/redis
        networks:
            - backend

    postgres:
        container_name: postgres
        build: ./docker/postgres
        networks:
            - backend
        environment:
            POSTGRES_PASSWORD: ${DB_PASSWORD}
            POSTGRES_USER: ${DB_USERNAME}
            POSTGRES_DB: ${DB_NAME}
            PG_DATA: /var/lib/postgresql/data
        ports:
            - 5432:5432
        volumes:
            - pg-data:/var/lib/postgresql/data

volumes:
    pg-data:

networks:
    frontend:
        driver: bridge
    backend:
        driver: bridge