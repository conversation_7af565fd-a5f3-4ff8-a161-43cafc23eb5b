version: 2.1

jobs:
  build_dev:
    docker:
      - image: cimg/node:22.4.1
    steps:
      - run:
          shell: /bin/sh
          command: |
            sudo apt update
            sudo apt install git -y
            git --version
      - run:
          name: Install rsync
          command: |
            sudo apt-get install rsync
      - checkout
      - restore_cache:
          keys:
            - yarn-and-composer-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Checkout development branch
          command: |
            git checkout development
            git pull origin development
      - run:
          name: Install dependencies
          command: |
            yarn
      - save_cache:
          paths:
            - node_modules
          key: yarn-and-composer-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Create .env file
          command: |
            echo "DB_HOST=${DB_HOST}" >> .env
            echo "DB_NAME=${DB_NAME}" >> .env
            echo "DB_PASSWORD=${DB_PASSWORD}" >> .env
            echo "DB_PORT=${DB_PORT}" >> .env
            echo "DB_USERNAME=${DB_USERNAME}" >> .env
            echo "REDIS_URI=${REDIS_URI}" >> .env
            echo "JWT_ACCESS_TOKEN_TTL=${JWT_ACCESS_TOKEN_TTL}" >> .env
            echo "JWT_SECRET=${JWT_SECRET}" >> .env
            echo "JWT_TOKEN_AUDIENCE=${JWT_TOKEN_AUDIENCE}" >> .env
            echo "JWT_TOKEN_ISSUER=${JWT_TOKEN_ISSUER}" >> .env
            echo "SERVER_PORT=${SERVER_PORT}" >> .env
            echo "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}" >> .env
            echo "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}" >> .env
            echo "AWS_BUCKET_NAME=${AWS_BUCKET_NAME}" >> .env
            echo "AWS_BUCKET_REGION=${AWS_BUCKET_REGION}" >> .env
            echo "AWS_SES_SMTP_USER=${AWS_SES_SMTP_USER}" >> .env
            echo "AWS_SES_SMTP_PASS=${AWS_SES_SMTP_PASS}" >> .env
            echo "AWS_SES_ENDPOINT=${AWS_SES_ENDPOINT}" >> .env
            echo "AWS_SES_SENDER=${AWS_SES_SENDER}" >> .env
            echo "ADMIN_LINK=${ADMIN_LINK}" >> .env
            echo "CLIENT_LINK=${CLIENT_LINK}" >> .env
            echo "BASE_DOMAIN=${BASE_DOMAIN}" >> .env
            echo "TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN_DEV}" >> .env
            echo "TELEGRAM_SECONDARY_BOT_TOKEN=${TELEGRAM_SECONDARY_BOT_TOKEN_DEV}" >> .env
            echo "TELE_BOT_SCHEDULE=${TELE_BOT_SCHEDULE}" >> .env
            echo "CLOUDFLARE_TURNSTILE_SECRET=${CLOUDFLARE_TURNSTILE_SECRET_DEV}" >> .env
            echo "NODE_ENV=${NODE_ENV_DEV}" >> .env
            echo "CHROME_PATH=${CHROME_PATH}" >> .env
            echo "XERO_CLIENT_ID=${XERO_CLIENT_ID}" >> .env
            echo "XERO_CLIENT_SECRET=${XERO_CLIENT_SECRET}" >> .env
            echo "XERO_REDIRECT_URI=${XERO_REDIRECT_URI}" >> .env
            echo "XERO_TENANT_ID=${XERO_TENANT_ID}" >> .env
            echo "XERO_WEBHOOK_KEY=${XERO_WEBHOOK_KEY}" >> .env
            echo "SECRET_KEY=${SECRET_KEY}" >> .env
            echo "SECRET_KEY_IV=${SECRET_KEY_IV}" >> .env
            echo "HCAPTCHA_SECRET=${HCAPTCHA_SECRET}" >> .env
            echo "SUFFIX_DOMAIN=${SUFFIX_DOMAIN}" >> .env

      - run:
          name: Build
          command: |
            yarn build

      - persist_to_workspace:
          root: .
          paths:
            - ./*

  deploy_code_dev:
    docker:
      - image: cimg/node:22.4.1
    resource_class: large
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Install rsync
          command: |
            sudo apt-get update
            sudo apt-get install rsync
        # Run your command
      - run: export NODE_OPTIONS=--max-old-space-size=8192 && yarn build

      - add_ssh_keys

      # Add the server to known hosts
      - run: ssh-keyscan -H ${SERVER_DEV} >> ~/.ssh/known_hosts

      # Finally, upload your files to server.
      - run: rsync -avce ssh --delete ./. ubuntu@${SERVER_DEV}:~/property-listing-be

      - run: ssh ubuntu@${SERVER_DEV} "source ~/.nvm/nvm.sh && cd ~/property-listing-be && ls -la && source ~/.bashrc && yarn migration:run && pm2 reload ecosystem.config.js"

workflows:
  build-and-deploy-dev:
    jobs:
      - build_dev:
          filters:
            branches:
              only: development
      - deploy_code_dev:
          requires:
            - build_dev
          filters:
            branches:
              only: development
