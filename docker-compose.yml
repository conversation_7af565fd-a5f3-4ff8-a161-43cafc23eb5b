version: '3.8'

services:
  # nginx:
  #   build:
  #     context: .
  #     dockerfile: ./docker/nginx/Dockerfile
  #   container_name: nginx
  #   volumes:
  #     - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
  #   environment:
  #     - NGINX_HOST=${NGINX_HOST}
  #   ports:
  #     - 80:80
  #   restart: always
  #   networks:
  #     - frontend
  #   depends_on:
  #     - node

  # node:
  #   build:
  #     context: .
  #     dockerfile: ./docker/node/Dockerfile
  #     target: development
  #   container_name: node
  #   ports:
  #     - ${SERVER_PORT}:${SERVER_PORT}
  #     - 9229:9229
  #   env_file:
  #     - .env
  #   volumes:
  #     - .:/usr/src/app
  #     - /usr/src/app/node_modules
  #   restart: always
  #   networks:
  #     - frontend
  #     - backend
  #   command: npm run start:dev
  #   depends_on:
  #     - redis
  #     - postgres

  redis:
    container_name: redis
    build: ./docker/redis
    ports:
      - 6379:6379
    # networks:
    #   - backend

  postgres:
    container_name: postgres
    build: ./docker/postgres
    # networks:
    #   - backend
    environment:
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_DB: ${DB_NAME}
      PG_DATA: /var/lib/postgresql/data
    ports:
      - 5432:5432
    volumes:
      - pg-data:/var/lib/postgresql/data

  umami:
    image: ghcr.io/umami-software/umami:postgresql-latest
    ports:
      - '3000:3000'
    environment:
      DATABASE_URL: **************************************/umami
      DATABASE_TYPE: postgresql
      APP_SECRET: iU0nOPFIeN
    depends_on:
      umami_db:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'curl http://localhost:3000/api/heartbeat']
      interval: 5s
      timeout: 5s
      retries: 5

  umami_db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: umami
      POSTGRES_USER: umami
      POSTGRES_PASSWORD: umami
    ports:
      - '54322:5432'
    volumes:
      - umami-db-data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  umami-db-data:
  pg-data:
# networks:
#   frontend:
#     driver: bridge
#   backend:
#     driver: bridge
