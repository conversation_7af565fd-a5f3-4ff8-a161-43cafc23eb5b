import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUmamiEntities1733200847714 implements MigrationInterface {
  name = 'CreateUmamiEntities1733200847714';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "umami_websites" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, "domainId" uuid, "userProjectLandingPageId" uuid, "umamiWebsiteUuid" character varying, "umamiWebsiteId" character varying, "umamiWebsiteName" character varying, "umamiWebsiteDomain" character varying, "umamiWebsiteShareId" character varying, "umamiWebsiteTrackingCode" character varying, CONSTRAINT "REL_7ae92c5359ad5113ab897e22f2" UNIQUE ("domainId"), CONSTRAINT "REL_6f7e0471bfa32b18e58abfe08b" UNIQUE ("userProjectLandingPageId"), CONSTRAINT "PK_afdca609caf10ab224c593f0836" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "umami_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid, "umamiUserId" character varying, "umamiUserName" character varying, "umamiPassword" character varying, CONSTRAINT "REL_cf2ce7a64e1192c6d9aead73b3" UNIQUE ("userId"), CONSTRAINT "PK_2a093d4a678a2fd00535e34b589" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" ADD CONSTRAINT "FK_f65943031d38dac0c9a24613402" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" ADD CONSTRAINT "FK_7ae92c5359ad5113ab897e22f20" FOREIGN KEY ("domainId") REFERENCES "domain"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" ADD CONSTRAINT "FK_6f7e0471bfa32b18e58abfe08b3" FOREIGN KEY ("userProjectLandingPageId") REFERENCES "user_project_landing_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_mapping" ADD CONSTRAINT "FK_cf2ce7a64e1192c6d9aead73b3f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "umami_mapping" DROP CONSTRAINT "FK_cf2ce7a64e1192c6d9aead73b3f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" DROP CONSTRAINT "FK_6f7e0471bfa32b18e58abfe08b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" DROP CONSTRAINT "FK_7ae92c5359ad5113ab897e22f20"`,
    );
    await queryRunner.query(
      `ALTER TABLE "umami_websites" DROP CONSTRAINT "FK_f65943031d38dac0c9a24613402"`,
    );
    await queryRunner.query(`DROP TABLE "umami_mapping"`);
    await queryRunner.query(`DROP TABLE "umami_websites"`);
  }
}
