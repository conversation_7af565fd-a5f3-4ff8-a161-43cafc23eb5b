import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNameToUnitTable1726109323037 implements MigrationInterface {
    name = 'AddNameToUnitTable1726109323037'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unit" ADD "name" character varying`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "currency" SET DEFAULT 'SGD'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "currency" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "unit" DROP COLUMN "name"`);
    }

}
