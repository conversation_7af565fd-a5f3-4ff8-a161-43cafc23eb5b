import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCeaDetailTableContactSaleSubmission1722851083854
  implements MigrationInterface
{
  name = 'AddColumnCeaDetailTableContactSaleSubmission1722851083854';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "ceaInfo" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "ceaInfo"`,
    );
  }
}
