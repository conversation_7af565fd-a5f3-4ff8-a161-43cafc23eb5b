import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddContactStatus1745306944302 implements MigrationInterface {
  name = 'AddContactStatus1745306944302';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."contact_sale_submission_contactstatus_enum" AS ENUM('new', 'open', 'in-progress', 'unqualified', 'attempted-to-contact', 'connected')`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "contactStatus" "public"."contact_sale_submission_contactstatus_enum" NOT NULL DEFAULT 'new'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "contactStatus"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."contact_sale_submission_contactstatus_enum"`,
    );
  }
}
