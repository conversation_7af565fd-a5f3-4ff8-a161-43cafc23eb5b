import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnDeletedByIpContactSaleSubmission1732698576263
  implements MigrationInterface
{
  name = 'AddColumnDeletedByIpContactSaleSubmission1732698576263';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "deletedByIP" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "deletedByIP"`,
    );
  }
}
