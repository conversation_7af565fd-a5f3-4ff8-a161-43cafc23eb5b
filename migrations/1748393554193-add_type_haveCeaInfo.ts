import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeHaveCeaInfo1748393554193 implements MigrationInterface {
  name = 'AddTypeHaveCeaInfo1748393554193';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."contact_sale_submission_log_type_enum" RENAME TO "contact_sale_submission_log_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."contact_sale_submission_log_type_enum" AS ENUM('appointmentDate', 'projectViewCount', 'interested', 'unitType', 'projectViewDuration', 'redFlag', 'haveCeaInfo')`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission_log" ALTER COLUMN "type" TYPE "public"."contact_sale_submission_log_type_enum" USING "type"::"text"::"public"."contact_sale_submission_log_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."contact_sale_submission_log_type_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."contact_sale_submission_log_type_enum_old" AS ENUM('appointmentDate', 'projectViewCount', 'interested', 'unitType', 'projectViewDuration', 'redFlag')`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission_log" ALTER COLUMN "type" TYPE "public"."contact_sale_submission_log_type_enum_old" USING "type"::"text"::"public"."contact_sale_submission_log_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."contact_sale_submission_log_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."contact_sale_submission_log_type_enum_old" RENAME TO "contact_sale_submission_log_type_enum"`,
    );
  }
}
