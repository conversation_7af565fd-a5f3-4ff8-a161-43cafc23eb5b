import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIpTrackingAndBotTable1726027576827 implements MigrationInterface {
    name = 'AddIpTrackingAndBotTable1726027576827'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "ip_tracking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "projectName" character varying NOT NULL, "projectId" character varying NOT NULL, "projectLink" character varying NOT NULL, "ip" character varying NOT NULL, "region" character varying NOT NULL, "browser" character varying NOT NULL, "domain" character varying NOT NULL, CONSTRAINT "PK_d65f390493518f4881b9cbc1c7e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "bot" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "userId" character varying NOT NULL, "email" character varying NOT NULL, "chatId" character varying NOT NULL, CONSTRAINT "PK_bc6d59d7870eb2efd5f7f61e5ca" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "bot"`);
        await queryRunner.query(`DROP TABLE "ip_tracking"`);
    }

}
