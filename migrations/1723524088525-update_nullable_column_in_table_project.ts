import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNullableColumnInTableProject1723524088525
  implements MigrationInterface
{
  name = 'UpdateNullableColumnInTableProject1723524088525';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "marketSegment" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "area" SET DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "area" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "marketSegment" SET NOT NULL`,
    );
  }
}
