import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDeviceCountryToContactSaleSubmission1727195522571 implements MigrationInterface {
    name = 'AddDeviceCountryToContactSaleSubmission1727195522571'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" ADD "device" jsonb`);
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" ADD "country" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" DROP COLUMN "country"`);
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" DROP COLUMN "device"`);
    }

}
