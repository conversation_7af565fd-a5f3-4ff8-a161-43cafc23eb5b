import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddElevationChartToProject1720673494907
  implements MigrationInterface
{
  name = 'AddElevationChartToProject1720673494907';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "elevationChartId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_e722fc9b6e1531ec0c934e5ec64" UNIQUE ("elevationChartId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_e722fc9b6e1531ec0c934e5ec64" FOREIGN KEY ("elevationChartId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_e722fc9b6e1531ec0c934e5ec64"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_e722fc9b6e1531ec0c934e5ec64"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "elevationChartId"`,
    );
  }
}
