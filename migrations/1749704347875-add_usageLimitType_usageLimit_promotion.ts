import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUsageLimitTypeUsageLimitPromotion1749704347875
  implements MigrationInterface
{
  name = 'AddUsageLimitTypeUsageLimitPromotion1749704347875';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_usagelimittype_enum" AS ENUM('PER_USER', 'PER_PROMO_CODE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ADD "usageLimitType" "public"."promotion_usagelimittype_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "promotion" ADD "usageLimit" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "usageLimit"`);
    await queryRunner.query(
      `ALTER TABLE "promotion" DROP COLUMN "usageLimitType"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."promotion_usagelimittype_enum"`,
    );
  }
}
