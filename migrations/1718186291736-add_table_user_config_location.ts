import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableUserConfigLocation1718186291736 implements MigrationInterface {
    name = 'AddTableUserConfigLocation1718186291736'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."user_config_location_layout_enum" AS ENUM('SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix')`);
        await queryRunner.query(`CREATE TABLE "user_config_location" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "locationId" uuid NOT NULL, "configId" uuid NOT NULL, "layout" "public"."user_config_location_layout_enum" NOT NULL DEFAULT 'SectionGalleryThree', "background" character varying NOT NULL, "visible" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_f539fcee0342af4b5f7a0a3b066" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user_config_location" ADD CONSTRAINT "FK_9d5dbbe905338c326faa10433be" FOREIGN KEY ("locationId") REFERENCES "location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_config_location" ADD CONSTRAINT "FK_0ab5c3582e4200262387bf4d5ae" FOREIGN KEY ("configId") REFERENCES "user_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config_location" DROP CONSTRAINT "FK_0ab5c3582e4200262387bf4d5ae"`);
        await queryRunner.query(`ALTER TABLE "user_config_location" DROP CONSTRAINT "FK_9d5dbbe905338c326faa10433be"`);
        await queryRunner.query(`DROP TABLE "user_config_location"`);
        await queryRunner.query(`DROP TYPE "public"."user_config_location_layout_enum"`);
    }

}
