import { MigrationInterface, QueryRunner } from "typeorm";

export class  UpdateValueColumn1721812095909 implements MigrationInterface {
    name = 'UpdateValueColumn1721812095909'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "value" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "value" SET NOT NULL`);
    }

}
