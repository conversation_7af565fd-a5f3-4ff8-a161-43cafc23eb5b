import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldUseEnquiryScript1742446402006
  implements MigrationInterface
{
  name = 'AddFieldUseEnquiryScript1742446402006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "useEnquiryScript" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "useEnquiryScript"`,
    );
  }
}
