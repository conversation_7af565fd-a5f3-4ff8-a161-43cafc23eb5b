import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddXeroInvoiceIdInXeroPayment1746599131510
  implements MigrationInterface
{
  name = 'AddXeroInvoiceIdInXeroPayment1746599131510';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD "xeroInvoiceId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_6592277742879c3e93970d0e164" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_8f49b9cb57b3a1dfca4badd3bef" FOREIGN KEY ("packageId") REFERENCES "package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_8f49b9cb57b3a1dfca4badd3bef"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_6592277742879c3e93970d0e164"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP COLUMN "xeroInvoiceId"`,
    );
  }
}
