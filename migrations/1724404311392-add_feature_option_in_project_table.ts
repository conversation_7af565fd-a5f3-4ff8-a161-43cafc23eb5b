import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFeatureOptionInProjectTable1724404311392 implements MigrationInterface {
    name = 'AddFeatureOptionInProjectTable1724404311392'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "featured" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "featured"`);
    }

}
