import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEntitiesAnalytic1721381653838 implements MigrationInterface {
  name = 'AddEntitiesAnalytic1721381653838';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "unit_type_stats" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "date" TIMESTAMP WITH TIME ZONE NOT NULL, "unitTypeId" uuid NOT NULL, "entriesCount" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_9aef51079ba93b2be9f69267295" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bbb5cd28089470085d847d3aff" ON "unit_type_stats" ("date") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_a61bb2ad8ee7a8e22e5c87ca2f" ON "unit_type_stats" ("date", "unitTypeId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "project_stats" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "date" TIMESTAMP WITH TIME ZONE NOT NULL, "projectId" uuid NOT NULL, "entriesCount" integer NOT NULL DEFAULT '0', "viewsCount" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_b23c6dcedde6c266fc2af3c0bb1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_976376cc427fdfb753a0c3cece" ON "project_stats" ("date", "projectId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" ADD CONSTRAINT "FK_de58fa0f9e3e2326da155c19dc7" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" ADD CONSTRAINT "FK_4aad8ecf2257da8f01f8776ac24" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project_stats" DROP CONSTRAINT "FK_4aad8ecf2257da8f01f8776ac24"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" DROP CONSTRAINT "FK_de58fa0f9e3e2326da155c19dc7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_976376cc427fdfb753a0c3cece"`,
    );
    await queryRunner.query(`DROP TABLE "project_stats"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a61bb2ad8ee7a8e22e5c87ca2f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_bbb5cd28089470085d847d3aff"`,
    );
    await queryRunner.query(`DROP TABLE "unit_type_stats"`);
  }
}
