import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPixelID1750314668424 implements MigrationInterface {
  name = 'AddPixelID1750314668424';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "pixelID" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "pixelID"`,
    );
  }
}
