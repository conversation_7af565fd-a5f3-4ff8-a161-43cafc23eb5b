import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableUserConfig1718613717475 implements MigrationInterface {
    name = 'UpdateTableUserConfig1718613717475'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" ADD "headerLogoId" uuid`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "UQ_a7403c704f311b292e40b568dc7" UNIQUE ("headerLogoId")`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD "footerLogoId" uuid`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "UQ_c41d403b855f63500b1ee70dbf0" UNIQUE ("footerLogoId")`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD "coverImageId" uuid`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "UQ_681983b376f65b564037e1f5fe9" UNIQUE ("coverImageId")`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD "topMessage" jsonb`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "UQ_a04a247233bd86df18a93080030" UNIQUE ("siteLogoId")`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "FK_a04a247233bd86df18a93080030" FOREIGN KEY ("siteLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "FK_a7403c704f311b292e40b568dc7" FOREIGN KEY ("headerLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "FK_c41d403b855f63500b1ee70dbf0" FOREIGN KEY ("footerLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "FK_681983b376f65b564037e1f5fe9" FOREIGN KEY ("coverImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "FK_681983b376f65b564037e1f5fe9"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "FK_c41d403b855f63500b1ee70dbf0"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "FK_a7403c704f311b292e40b568dc7"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "FK_a04a247233bd86df18a93080030"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "UQ_a04a247233bd86df18a93080030"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "topMessage"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "UQ_681983b376f65b564037e1f5fe9"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "coverImageId"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "UQ_c41d403b855f63500b1ee70dbf0"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "footerLogoId"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "UQ_a7403c704f311b292e40b568dc7"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "headerLogoId"`);
    }

}
