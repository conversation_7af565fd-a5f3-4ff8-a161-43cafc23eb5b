import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableContactSaleSubmission1718782695667
  implements MigrationInterface
{
  name = 'UpdateTableContactSaleSubmission1718782695667';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "ip" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "browser" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "browser"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "ip"`,
    );
  }
}
