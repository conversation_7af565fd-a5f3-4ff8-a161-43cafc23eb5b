import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateXeroPaymentTables1736318904553
  implements MigrationInterface
{
  name = 'CreateXeroPaymentTables1736318904553';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "invoice" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "xeroInvoiceId" character varying NOT NULL, "amount" numeric(10,2) NOT NULL, "issuedAt" TIMESTAMP, "paidAt" TIMESTAMP, "transactionId" uuid NOT NULL, CONSTRAINT "PK_15d25c200d9bcd8a33f698daf18" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."package_status_enum" AS ENUM('active', 'deactive')`,
    );
    await queryRunner.query(
      `CREATE TABLE "package" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "description" character varying, "price" numeric(10,2) NOT NULL, "numberOfCredit" integer, "duration" integer, "discount" numeric(10,2), "status" "public"."package_status_enum", CONSTRAINT "PK_308364c66df656295bc4ec467c2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_status_enum" AS ENUM('pending', 'succeed', 'failed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "xero_payment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "packageId" uuid NOT NULL, "invoiceId" uuid, "paymentUrl" character varying, "status" "public"."xero_payment_status_enum" NOT NULL DEFAULT 'pending', CONSTRAINT "REL_4ef7f83319a9e1a502c244a8f1" UNIQUE ("invoiceId"), CONSTRAINT "PK_3f18eb949cabe50b25d37af2132" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "xero_payment"`);
    await queryRunner.query(`DROP TYPE "public"."xero_payment_status_enum"`);
    await queryRunner.query(`DROP TABLE "package"`);
    await queryRunner.query(`DROP TYPE "public"."package_status_enum"`);
    await queryRunner.query(`DROP TABLE "invoice"`);
  }
}
