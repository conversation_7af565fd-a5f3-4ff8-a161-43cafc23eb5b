import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSection1720430594314 implements MigrationInterface {
  name = 'UpdateSection1720430594314';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "section" ADD "weightTimestamp" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "section" ALTER COLUMN "weight" SET DEFAULT '1'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "section" ALTER COLUMN "weight" SET DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "section" DROP COLUMN "weightTimestamp"`);
  }
}
