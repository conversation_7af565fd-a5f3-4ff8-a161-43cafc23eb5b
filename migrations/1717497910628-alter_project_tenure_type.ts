import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterProjectTenureType1717497910628 implements MigrationInterface {
    name = 'AlterProjectTenureType1717497910628'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "tenure"`);
        await queryRunner.query(`CREATE TYPE "public"."project_tenure_enum" AS ENUM('freehold', '999-yr', '99-yr')`);
        await queryRunner.query(`ALTER TABLE "project" ADD "tenure" "public"."project_tenure_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "tenure"`);
        await queryRunner.query(`DROP TYPE "public"."project_tenure_enum"`);
        await queryRunner.query(`ALTER TABLE "project" ADD "tenure" integer NOT NULL`);
    }

}
