import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddSalesTeam1723431270138 implements MigrationInterface {
    name = 'AddSalesTeam1723431270138'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" ADD "salesTeamInfo" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "salesTeamInfo"`);
    }

}
