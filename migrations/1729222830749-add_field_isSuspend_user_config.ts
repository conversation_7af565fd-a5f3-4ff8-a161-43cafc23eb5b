import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldIsSuspendUserConfig1729222830749
  implements MigrationInterface
{
  name = 'AddFieldIsSuspendUserConfig1729222830749';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "isSuspend" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "isSuspend"`,
    );
  }
}
