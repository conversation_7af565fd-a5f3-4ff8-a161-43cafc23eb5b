import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableUserConfig1718681445139 implements MigrationInterface {
  name = 'UpdateTableUserConfig1718681445139';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "googleMapUrl" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "socialLinks" jsonb`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_content" ADD "title" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "site_content" DROP COLUMN "title"`);
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "socialLinks"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "googleMapUrl"`);
  }
}
