import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProjectAmenityFacility1720585862228
  implements MigrationInterface
{
  name = 'UpdateProjectAmenityFacility1720585862228';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" ADD "detail" jsonb`);
    await queryRunner.query(`ALTER TABLE "project" ADD "amenityHtml" jsonb`);
    await queryRunner.query(`ALTER TABLE "project" ADD "facilityHtml" jsonb`);
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "expectedTop"`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD "expectedTop" TIMESTAMP`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "expectedTop"`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD "expectedTop" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "facilityHtml"`);
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "amenityHtml"`);
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "detail"`);
  }
}
