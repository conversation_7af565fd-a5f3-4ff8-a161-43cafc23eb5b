import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHeldAtVoucher1747281183603 implements MigrationInterface {
  name = 'AddHeldAtVoucher1747281183603';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD "heldAt" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "voucher" DROP COLUMN "heldAt"`);
  }
}
