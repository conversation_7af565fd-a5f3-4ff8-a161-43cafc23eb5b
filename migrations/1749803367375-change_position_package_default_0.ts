import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangePositionPackageDefault01749803367375
  implements MigrationInterface
{
  name = 'ChangePositionPackageDefault01749803367375';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "package" ALTER COLUMN "position" SET DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "package" ALTER COLUMN "position" DROP DEFAULT`,
    );
  }
}
