import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLogoutTimeForLoginActivity1727146031281 implements MigrationInterface {
    name = 'AddLogoutTimeForLoginActivity1727146031281'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "action"`);
        await queryRunner.query(`DROP TYPE "public"."login_activity_action_enum"`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "sessionId" character varying`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "loggedOutAt" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "loggedOutAt"`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "sessionId"`);
        await queryRunner.query(`CREATE TYPE "public"."login_activity_action_enum" AS ENUM('login', 'logout')`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "action" "public"."login_activity_action_enum"`);
    }

}
