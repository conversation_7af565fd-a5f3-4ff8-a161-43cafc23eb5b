import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGetAllDeveloperPermission1747649703246
  implements MigrationInterface
{
  name = 'AddGetAllDeveloperPermission1747649703246';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."api_key_permissions_enum" RENAME TO "api_key_permissions_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."api_key_permissions_enum" AS ENUM('GET_ALL_PROPERTIES', 'GET_ALL_CATEGORIES', 'GET_ALL_LOCATIONS', 'GET_ALL_UNIT_TYPES', 'GET_ALL_TOP_LISTING', 'GET_ALL_PRICING', 'POST_LEAD', 'GET_ALL_DEVELOPERS')`,
    );
    await queryRunner.query(
      `ALTER TABLE "api_key" ALTER COLUMN "permissions" TYPE "public"."api_key_permissions_enum"[] USING "permissions"::"text"::"public"."api_key_permissions_enum"[]`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."api_key_permissions_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."api_key_permissions_enum_old" AS ENUM('GET_ALL_PROPERTIES', 'GET_ALL_CATEGORIES', 'GET_ALL_LOCATIONS', 'GET_ALL_UNIT_TYPES', 'GET_ALL_TOP_LISTING', 'GET_ALL_PRICING', 'POST_LEAD')`,
    );
    await queryRunner.query(
      `ALTER TABLE "api_key" ALTER COLUMN "permissions" TYPE "public"."api_key_permissions_enum_old"[] USING "permissions"::"text"::"public"."api_key_permissions_enum_old"[]`,
    );
    await queryRunner.query(`DROP TYPE "public"."api_key_permissions_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."api_key_permissions_enum_old" RENAME TO "api_key_permissions_enum"`,
    );
  }
}
