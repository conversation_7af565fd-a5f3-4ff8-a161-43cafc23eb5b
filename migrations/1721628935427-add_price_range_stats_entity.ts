import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPriceRangeStatsEntity1721628935427
  implements MigrationInterface
{
  name = 'AddPriceRangeStatsEntity1721628935427';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "price_range_stats" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "date" TIMESTAMP WITH TIME ZONE NOT NULL, "minPrice" integer NOT NULL, "maxPrice" integer NOT NULL, "entriesCount" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_c557066530c722136cf4fbdaca8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_3166620e86170db5612eae867c" ON "price_range_stats" ("date", "minPrice") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3166620e86170db5612eae867c"`,
    );
    await queryRunner.query(`DROP TABLE "price_range_stats"`);
  }
}
