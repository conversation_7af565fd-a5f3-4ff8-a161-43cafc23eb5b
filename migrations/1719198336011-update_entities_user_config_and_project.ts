import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEntitiesUserConfigAndProject1719198336011
  implements MigrationInterface
{
  name = 'UpdateEntitiesUserConfigAndProject1719198336011';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project" ADD "promotion" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."project_topstatus_enum" AS ENUM('Completed', 'TOP-soon')`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD "topStatus" "public"."project_topstatus_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "siteMetadata" jsonb`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."project_tenure_enum" RENAME TO "project_tenure_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."project_tenure_enum" AS ENUM('freehold', '999-yr', '99-yr', '101-yr')`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "tenure" TYPE "public"."project_tenure_enum" USING "tenure"::"text"::"public"."project_tenure_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."project_tenure_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."project_tenure_enum_old" AS ENUM('freehold', '999-yr', '99-yr')`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "tenure" TYPE "public"."project_tenure_enum_old" USING "tenure"::"text"::"public"."project_tenure_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."project_tenure_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."project_tenure_enum_old" RENAME TO "project_tenure_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "siteMetadata"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "topStatus"`);
    await queryRunner.query(`DROP TYPE "public"."project_topstatus_enum"`);
    await queryRunner.query(
      `ALTER TABLE "user_project" DROP COLUMN "promotion"`,
    );
  }
}
