import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSlugColumnNullable1717139267020
  implements MigrationInterface
{
  name = 'UpdateSlugColumnNullable1717139267020';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_type" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "amenity" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_plan" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "location" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "slug" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ALTER COLUMN "slug" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "category" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "location" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_plan" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "amenity" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ALTER COLUMN "slug" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ALTER COLUMN "slug" SET NOT NULL`,
    );
  }
}
