import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOnlineInvoiceUrl1747903973195 implements MigrationInterface {
  name = 'AddOnlineInvoiceUrl1747903973195';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "onlineInvoiceUrl" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD "onlineInvoiceUrl" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP COLUMN "onlineInvoiceUrl"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "onlineInvoiceUrl"`,
    );
  }
}
