import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeUpdateNotificationApp1749788048483
  implements MigrationInterface
{
  name = 'ChangeUpdateNotificationApp1749788048483';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."notification_app_type_enum" AS ENUM('PACKAGE')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."notification_app_accessrole_enum" AS ENUM('admin', 'agency', 'user')`,
    );
    await queryRunner.query(
      `CREATE TABLE "notification_app" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "titleKey" character varying NOT NULL, "descriptionKey" character varying NOT NULL, "isRead" boolean NOT NULL DEFAULT false, "data" jsonb, "type" "public"."notification_app_type_enum" NOT NULL, "userId" uuid, "accessRole" "public"."notification_app_accessrole_enum" NOT NULL, CONSTRAINT "PK_ea10ca06fd026d6a2a4f1e368d2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "notification_app" ADD CONSTRAINT "FK_43e426afafd9823f3d4278286e4" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "notification_app" DROP CONSTRAINT "FK_43e426afafd9823f3d4278286e4"`,
    );
    await queryRunner.query(`DROP TABLE "notification_app"`);
    await queryRunner.query(
      `DROP TYPE "public"."notification_app_accessrole_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."notification_app_type_enum"`);
  }
}
