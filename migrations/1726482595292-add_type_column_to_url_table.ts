import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeColumnToUrlTable1726482595292 implements MigrationInterface {
    name = 'AddTypeColumnToUrlTable1726482595292'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "url" ADD "type" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "url" DROP COLUMN "type"`);
    }

}
