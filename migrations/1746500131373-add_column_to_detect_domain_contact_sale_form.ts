import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnToDetectDomainContactSaleForm1746500131373
  implements MigrationInterface
{
  name = 'AddColumnToDetectDomainContactSaleForm1746500131373';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "submittedFromDomain" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "isSubmittedFromLdp" boolean`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "isSubmittedFromLdp"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "submittedFromDomain"`,
    );
  }
}
