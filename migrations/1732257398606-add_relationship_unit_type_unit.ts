import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationshipUnitTypeUnit1732257398606
  implements MigrationInterface
{
  name = 'AddRelationshipUnitTypeUnit1732257398606';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unit" ADD "unitTypeId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "FK_fa089475c489a50cebdc09d62af" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_fa089475c489a50cebdc09d62af"`,
    );
    await queryRunner.query(`ALTER TABLE "unit" DROP COLUMN "unitTypeId"`);
  }
}
