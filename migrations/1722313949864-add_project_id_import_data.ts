import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddProjectIdImportData1722313949864 implements MigrationInterface {
    name = 'AddProjectIdImportData1722313949864'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_data" ADD "projectId" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_data" DROP COLUMN "projectId"`);
    }

}
