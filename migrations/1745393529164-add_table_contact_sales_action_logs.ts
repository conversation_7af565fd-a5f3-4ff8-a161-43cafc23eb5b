import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableContactSalesActionLogs1745393529164
  implements MigrationInterface
{
  name = 'AddTableContactSalesActionLogs1745393529164';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."contact_sale_action_log_actiontrackingtype_enum" AS ENUM('updateContactStatus', 'viewDetail', 'clickContactWhatsappButton')`,
    );
    await queryRunner.query(
      `CREATE TABLE "contact_sale_action_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "actionTrackingType" "public"."contact_sale_action_log_actiontrackingtype_enum" NOT NULL, "userId" uuid NOT NULL, "submissionId" uuid NOT NULL, "content" character varying, CONSTRAINT "PK_a4bfdfc5306b6f2c67c549aaf5c" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP TYPE "public"."contact_sale_action_log_actiontrackingtype_enum"`,
    );
    await queryRunner.query(`DROP TABLE "contact_sale_action_log"`);
  }
}
