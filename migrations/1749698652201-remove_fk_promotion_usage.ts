import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveFkPromotionUsage1749698652201 implements MigrationInterface {
  name = 'RemoveFkPromotionUsage1749698652201';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP CONSTRAINT "FK_16c4d0b1e6090a3b4c98fc4bc9e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP CONSTRAINT "REL_16c4d0b1e6090a3b4c98fc4bc9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP COLUMN "xeroPaymentId"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD "xeroPaymentId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD CONSTRAINT "REL_16c4d0b1e6090a3b4c98fc4bc9" UNIQUE ("xeroPaymentId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD CONSTRAINT "FK_16c4d0b1e6090a3b4c98fc4bc9e" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
