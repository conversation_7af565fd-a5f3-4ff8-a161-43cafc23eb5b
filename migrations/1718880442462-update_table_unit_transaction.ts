import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableUnitTransaction1718880442462
  implements MigrationInterface
{
  name = 'UpdateTableUnitTransaction1718880442462';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "soldPrice"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "currency"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "unitTypeId" uuid NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "timestamp" TIMESTAMP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "unitName" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "status" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_banner" ADD "description" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_banner" DROP COLUMN "description"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "status"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "unitName"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "timestamp"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "unitTypeId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "currency" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "soldPrice" double precision NOT NULL`,
    );
  }
}
