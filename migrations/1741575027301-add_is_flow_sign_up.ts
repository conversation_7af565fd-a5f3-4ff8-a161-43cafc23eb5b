import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsFlowSignUp1741575027301 implements MigrationInterface {
  name = 'AddIsFlowSignUp1741575027301';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "isFlowSignUp" boolean DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isFlowSignUp"`);
  }
}
