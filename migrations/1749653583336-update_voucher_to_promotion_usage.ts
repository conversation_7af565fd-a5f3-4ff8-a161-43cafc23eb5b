import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateVoucherToPromotionUsage1749653583336
  implements MigrationInterface
{
  name = 'UpdateVoucherToPromotionUsage1749653583336';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_9fbe4c9bb33284acc0078486773"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_5e936b946b28f8e701f5c2c1bff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME COLUMN "voucherId" TO "promotionUsageId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME CONSTRAINT "UQ_9fbe4c9bb33284acc0078486773" TO "UQ_9d2ecff9a8beb354e499cb0490a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "voucherId" TO "promotionUsageId"`,
    );
    await queryRunner.query(
      `CREATE TABLE "promotion_usage" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "promotionId" uuid NOT NULL, "xeroPaymentId" uuid, "byUserId" uuid, CONSTRAINT "REL_16c4d0b1e6090a3b4c98fc4bc9" UNIQUE ("xeroPaymentId"), CONSTRAINT "PK_5d5b80f7b3c1cb5d961e69ac49c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD CONSTRAINT "FK_36308f3f36ca3e6f381c2532925" FOREIGN KEY ("promotionId") REFERENCES "promotion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD CONSTRAINT "FK_16c4d0b1e6090a3b4c98fc4bc9e" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" ADD CONSTRAINT "FK_2a8cfa0b3eb74dfb42da43398f1" FOREIGN KEY ("byUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_9d2ecff9a8beb354e499cb0490a" FOREIGN KEY ("promotionUsageId") REFERENCES "promotion_usage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_82a263b27545bedbb331cd4bb93" FOREIGN KEY ("promotionUsageId") REFERENCES "promotion_usage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    //remove table voucher
    await queryRunner.query(`DROP TABLE "voucher"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_82a263b27545bedbb331cd4bb93"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_9d2ecff9a8beb354e499cb0490a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP CONSTRAINT "FK_2a8cfa0b3eb74dfb42da43398f1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP CONSTRAINT "FK_16c4d0b1e6090a3b4c98fc4bc9e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion_usage" DROP CONSTRAINT "FK_36308f3f36ca3e6f381c2532925"`,
    );
    await queryRunner.query(`DROP TABLE "promotion_usage"`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "promotionUsageId" TO "voucherId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME CONSTRAINT "UQ_9d2ecff9a8beb354e499cb0490a" TO "UQ_9fbe4c9bb33284acc0078486773"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME COLUMN "promotionUsageId" TO "voucherId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_5e936b946b28f8e701f5c2c1bff" FOREIGN KEY ("voucherId") REFERENCES "voucher"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_9fbe4c9bb33284acc0078486773" FOREIGN KEY ("voucherId") REFERENCES "voucher"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
