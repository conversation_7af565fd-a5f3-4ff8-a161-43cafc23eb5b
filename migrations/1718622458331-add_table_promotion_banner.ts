import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTablePromotionBanner1718622458331 implements MigrationInterface {
    name = 'AddTablePromotionBanner1718622458331'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "promotion_banner" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "photoId" uuid NOT NULL, "url" character varying NOT NULL, "name" character varying NOT NULL, "configId" uuid NOT NULL, CONSTRAINT "REL_021a5e7ba049280fb0032af0a9" UNIQUE ("photoId"), CONSTRAINT "PK_4f4a57df95170e73b622d9e9ae3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TYPE "public"."asset_relation_enum" RENAME TO "asset_relation_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."asset_relation_enum" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner')`);
        await queryRunner.query(`ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum" USING "relation"::"text"::"public"."asset_relation_enum"`);
        await queryRunner.query(`DROP TYPE "public"."asset_relation_enum_old"`);
        await queryRunner.query(`ALTER TABLE "promotion_banner" ADD CONSTRAINT "FK_021a5e7ba049280fb0032af0a98" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "promotion_banner" ADD CONSTRAINT "FK_cc84c1f2d76978b410ccd8c3b9d" FOREIGN KEY ("configId") REFERENCES "user_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "promotion_banner" DROP CONSTRAINT "FK_cc84c1f2d76978b410ccd8c3b9d"`);
        await queryRunner.query(`ALTER TABLE "promotion_banner" DROP CONSTRAINT "FK_021a5e7ba049280fb0032af0a98"`);
        await queryRunner.query(`CREATE TYPE "public"."asset_relation_enum_old" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config')`);
        await queryRunner.query(`ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum_old" USING "relation"::"text"::"public"."asset_relation_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."asset_relation_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."asset_relation_enum_old" RENAME TO "asset_relation_enum"`);
        await queryRunner.query(`DROP TABLE "promotion_banner"`);
    }

}
