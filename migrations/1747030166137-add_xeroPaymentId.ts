import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddXeroPaymentId1747030166137 implements MigrationInterface {
  name = 'AddXeroPaymentId1747030166137';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "xeroPaymentID" uuid`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "xeroPaymentID"`,
    );
  }
}
