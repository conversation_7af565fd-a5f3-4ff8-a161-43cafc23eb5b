import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLoginActivityTable1726679611869 implements MigrationInterface {
    name = 'UpdateLoginActivityTable1726679611869'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "location"`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "country" character varying`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "city" character varying`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "browser" character varying`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "os" character varying`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "device"`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "device" jsonb`);
        await queryRunner.query(`ALTER TABLE "login_activity" ALTER COLUMN "ip" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "login_activity" ALTER COLUMN "referral" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_activity" ALTER COLUMN "referral" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "login_activity" ALTER COLUMN "ip" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "device"`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "device" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "os"`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "browser"`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "city"`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "country"`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "location" character varying NOT NULL`);
    }

}
