import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHeaderBodyAnalyticUrlInUserConfig1733733067960
  implements MigrationInterface
{
  name = 'AddHeaderBodyAnalyticUrlInUserConfig1733733067960';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "headerAnalyticUrl" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "bodyAnalyticUrl" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "bodyAnalyticUrl"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "headerAnalyticUrl"`,
    );
  }
}
