import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableUser1718683345021 implements MigrationInterface {
    name = 'UpdateTableUser1718683345021'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "whatsapp" character varying(15)`);
        await queryRunner.query(`ALTER TABLE "user" ADD "photoId" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "photoId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "whatsapp"`);
    }

}
