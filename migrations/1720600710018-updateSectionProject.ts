import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSectionProject1720600710018 implements MigrationInterface {
  name = 'UpdateSectionProject1720600710018';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "section_project" ADD "weightTimestamp" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "section_project" ALTER COLUMN "weight" SET DEFAULT '1'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "section_project" ALTER COLUMN "weight" SET DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "section_project" DROP COLUMN "weightTimestamp"`);
  }
}
