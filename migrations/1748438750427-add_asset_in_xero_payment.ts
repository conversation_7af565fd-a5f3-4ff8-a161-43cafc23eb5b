import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAssetInXeroPayment1748438750427 implements MigrationInterface {
  name = 'AddAssetInXeroPayment1748438750427';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "xero_payment" ADD "assetId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "assetId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_aa7b053c05ce8a42df4375a79c2" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_b7d30c8ae52bf0322e6c1efeed2" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_b7d30c8ae52bf0322e6c1efeed2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_aa7b053c05ce8a42df4375a79c2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "assetId"`,
    );
    await queryRunner.query(`ALTER TABLE "xero_payment" DROP COLUMN "assetId"`);
  }
}
