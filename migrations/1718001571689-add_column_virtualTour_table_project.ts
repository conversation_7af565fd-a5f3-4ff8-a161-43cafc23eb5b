import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnVirtualTourTableProject1718001571689
  implements MigrationInterface
{
  name = 'AddColumnVirtualTourTableProject1718001571689';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" ADD "virtualTour" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "virtualTour"`);
  }
}
