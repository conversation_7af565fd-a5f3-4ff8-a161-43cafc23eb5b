import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnForCustomDomainLdp1746159750070
  implements MigrationInterface
{
  name = 'AddColumnForCustomDomainLdp1746159750070';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "subDomain" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "cNameTargetDomain" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "customDomain" text`,
    );

    await queryRunner.query(
      `UPDATE "user_project_landing_page" SET "subDomain" = "domain"`,
    ); // Update the subDomain = domain

    await queryRunner.query(
      `UPDATE "user_project_landing_page" SET "type" = 'subdomain' WHERE "type" IS NULL`,
    ); // Update all current LDP to subdomain
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "user_project_landing_page" SET "domain" = "subDomain"`,
    ); // Revert the domain = subDomain before drop the column

    // WARNING: This migration is not fully reversible, by default, the type will be null
    await queryRunner.query(
      `UPDATE "user_project_landing_page" SET "type" = NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "customDomain"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "cNameTargetDomain"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "subDomain"`,
    );
  }
}
