import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFloorPlanPhoto1717469039597 implements MigrationInterface {
  name = 'UpdateFloorPlanPhoto1717469039597';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "floor_plan" DROP CONSTRAINT "FK_34e16f04bb93ba543526c98855f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ALTER COLUMN "photoId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD CONSTRAINT "FK_34e16f04bb93ba543526c98855f" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "floor_plan" DROP CONSTRAINT "FK_34e16f04bb93ba543526c98855f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ALTER COLUMN "photoId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD CONSTRAINT "FK_34e16f04bb93ba543526c98855f" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
