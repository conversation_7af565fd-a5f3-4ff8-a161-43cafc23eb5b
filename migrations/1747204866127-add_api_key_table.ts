import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApiKeyTable1747204866127 implements MigrationInterface {
  name = 'AddApiKeyTable1747204866127';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."api_key_permissions_enum" AS ENUM('GET_ALL_PROPERTY')`,
    );
    await queryRunner.query(
      `CREATE TABLE "api_key" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "apiKey" character varying NOT NULL, "permissions" "public"."api_key_permissions_enum" array NOT NULL, "allowedIPs" character varying array NOT NULL, CONSTRAINT "PK_b1bd840641b8acbaad89c3d8d11" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "api_key"`);
    await queryRunner.query(`DROP TYPE "public"."api_key_permissions_enum"`);
  }
}
