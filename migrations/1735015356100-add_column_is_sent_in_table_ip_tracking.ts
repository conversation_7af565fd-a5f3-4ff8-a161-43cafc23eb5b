import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsSentInTableIpTracking1735015356100
  implements MigrationInterface
{
  name = 'AddColumnIsSentInTableIpTracking1735015356100';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "ip_tracking" ADD "isSent" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "ip_tracking" DROP COLUMN "isSent"`);
  }
}
