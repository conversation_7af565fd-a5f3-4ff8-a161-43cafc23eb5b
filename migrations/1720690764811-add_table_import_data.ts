import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableImportData1720690764811 implements MigrationInterface {
    name = 'AddTableImportData1720690764811'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."import_data_type_enum" AS ENUM('project', 'category', 'project-type', 'floor-plan', 'unit-transaction')`);
        await queryRunner.query(`CREATE TYPE "public"."import_data_status_enum" AS ENUM('processing', 'success', 'failed')`);
        await queryRunner.query(`CREATE TABLE "import_data" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "type" "public"."import_data_type_enum" NOT NULL, "importedById" uuid NOT NULL, "filename" character varying NOT NULL, "status" "public"."import_data_status_enum" NOT NULL DEFAULT 'processing', "failureReason" character varying, CONSTRAINT "PK_4cf2dd325e555466fd2e181d573" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "import_data" ADD CONSTRAINT "FK_7a441587df9cafe42de1eec45ed" FOREIGN KEY ("importedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_data" DROP CONSTRAINT "FK_7a441587df9cafe42de1eec45ed"`);
        await queryRunner.query(`DROP TABLE "import_data"`);
        await queryRunner.query(`DROP TYPE "public"."import_data_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."import_data_type_enum"`);
    }

}
