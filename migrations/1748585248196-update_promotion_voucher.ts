import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePromotionVoucher1748585248196 implements MigrationInterface {
  name = 'UpdatePromotionVoucher1748585248196';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "promotion" RENAME COLUMN "name" TO "code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" RENAME COLUMN "code" TO "byUserId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ADD CONSTRAINT "UQ_969359329a22440d2b8f7d491d4" UNIQUE ("code")`,
    );
    await queryRunner.query(`ALTER TABLE "voucher" DROP COLUMN "byUserId"`);
    await queryRunner.query(`ALTER TABLE "voucher" ADD "byUserId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD CONSTRAINT "FK_d6bff5cd54ec9d8996dfefd614a" FOREIGN KEY ("byUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "voucher" DROP CONSTRAINT "FK_d6bff5cd54ec9d8996dfefd614a"`,
    );
    await queryRunner.query(`ALTER TABLE "voucher" DROP COLUMN "byUserId"`);
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD "byUserId" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" DROP CONSTRAINT "UQ_969359329a22440d2b8f7d491d4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" RENAME COLUMN "byUserId" TO "code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" RENAME COLUMN "code" TO "name"`,
    );
  }
}
