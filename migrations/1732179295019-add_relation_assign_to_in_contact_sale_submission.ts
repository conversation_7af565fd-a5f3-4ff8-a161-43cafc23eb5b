import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationAssignToInContactSaleSubmission1732179295019
  implements MigrationInterface
{
  name = 'AddRelationAssignToInContactSaleSubmission1732179295019';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "assignedToId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD CONSTRAINT "FK_0c7a5fb088923f346f3bdfe2e6e" FOREIGN KEY ("assignedToId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "assignedToId"`,
    );
  }
}
