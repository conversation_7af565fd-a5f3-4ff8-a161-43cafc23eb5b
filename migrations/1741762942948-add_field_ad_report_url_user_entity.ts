import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldAdReportUrlUserEntity1741762942948
  implements MigrationInterface
{
  name = 'AddFieldAdReportUrlUserEntity1741762942948';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "adRerportUrl" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "adRerportUrl"`);
  }
}
