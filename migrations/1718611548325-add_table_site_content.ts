import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTableSiteContent1718611548325 implements MigrationInterface {
    name = 'AddTableSiteContent1718611548325'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "site_content" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "contentType" character varying NOT NULL, "content" text NOT NULL, CONSTRAINT "PK_a1362a1a095ab41c4347aea2c2d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "site_content" ADD CONSTRAINT "FK_b250c34546bb9e56af1b9d8a460" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "site_content" DROP CONSTRAINT "FK_b250c34546bb9e56af1b9d8a460"`);
        await queryRunner.query(`DROP TABLE "site_content"`);
    }

}
