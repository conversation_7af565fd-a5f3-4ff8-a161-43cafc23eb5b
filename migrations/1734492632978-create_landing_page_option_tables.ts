import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLandingPageOptionTables1734492632978
  implements MigrationInterface
{
  name = 'CreateLandingPageOptionTables1734492632978';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "landing_page_layout_option" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" text NOT NULL, "option" jsonb, CONSTRAINT "PK_0d4734914338d5f5a90ecab8209" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "landing_page_option_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "active" boolean NOT NULL DEFAULT false, "landingPageId" uuid, "landingPageOptionId" uuid, CONSTRAINT "PK_cfd6ed281d32fe4cca83ad381fd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "landing_page_option_mapping" ADD CONSTRAINT "FK_aaae82681cc75080d213756b352" FOREIGN KEY ("landingPageId") REFERENCES "user_project_landing_page"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "landing_page_option_mapping" ADD CONSTRAINT "FK_cb67ce8278f2eb2326cb45d18ef" FOREIGN KEY ("landingPageOptionId") REFERENCES "landing_page_layout_option"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP CONSTRAINT "FK_e43b2cdebfbd0f2c4ef1f04a453"`,
    );
    await queryRunner.query(
      `ALTER TABLE "landing_page_option_mapping" DROP CONSTRAINT "FK_cb67ce8278f2eb2326cb45d18ef"`,
    );
    await queryRunner.query(
      `ALTER TABLE "landing_page_option_mapping" DROP CONSTRAINT "FK_aaae82681cc75080d213756b352"`,
    );
    await queryRunner.query(`DROP TABLE "landing_page_option_mapping"`);
    await queryRunner.query(`DROP TABLE "landing_page_layout_option"`);
  }
}
