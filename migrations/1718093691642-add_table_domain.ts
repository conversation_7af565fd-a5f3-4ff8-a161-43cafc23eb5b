import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableDomain1718093691642 implements MigrationInterface {
  name = 'AddTableDomain1718093691642';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "domain" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "privateKey" character varying, "publicKey" character varying, "configId" uuid NOT NULL, CONSTRAINT "PK_27e3ec3ea0ae02c8c5bceab3ba9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "domain"`);
    await queryRunner.query(
      `ALTER TABLE "domain" ADD CONSTRAINT "FK_f3981ae93dfdb9c6a0af322147f" FOREIGN KEY ("configId") REFERENCES "user_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "domain" DROP CONSTRAINT "FK_f3981ae93dfdb9c6a0af322147f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "domain" character varying`,
    );
    await queryRunner.query(`DROP TABLE "domain"`);
  }
}
