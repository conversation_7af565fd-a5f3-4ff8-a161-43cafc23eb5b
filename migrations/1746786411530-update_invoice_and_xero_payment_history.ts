import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoiceAndXeroPaymentHistory1746786411530
  implements MigrationInterface
{
  name = 'UpdateInvoiceAndXeroPaymentHistory1746786411530';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "originPrice" numeric(10,2)`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "discountPrice" numeric(10,2)`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "totalTax" numeric(10,2)`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "total" numeric(10,2)`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "paid" numeric(10,2)`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "totalDue" numeric(10,2)`,
    );
    await queryRunner.query(`ALTER TABLE "xero_payment" ADD "invoiceId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "UQ_4ef7f83319a9e1a502c244a8f17" UNIQUE ("invoiceId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8" UNIQUE ("xeroPaymentId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "UQ_4ef7f83319a9e1a502c244a8f17"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP COLUMN "invoiceId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "totalDue"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "paid"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "total"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "totalTax"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "discountPrice"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "originPrice"`,
    );
  }
}
