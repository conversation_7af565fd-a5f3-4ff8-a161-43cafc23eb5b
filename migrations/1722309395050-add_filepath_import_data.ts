import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddFilepathImportData1722309395050 implements MigrationInterface {
    name = 'AddFilepathImportData1722309395050'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_data" ADD "filepath" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."import_data_status_enum" RENAME TO "import_data_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."import_data_status_enum" AS ENUM('processing', 'success', 'failed', 'pending')`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" TYPE "public"."import_data_status_enum" USING "status"::"text"::"public"."import_data_status_enum"`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."import_data_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."import_data_status_enum_old" AS ENUM('processing', 'success', 'failed')`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" TYPE "public"."import_data_status_enum_old" USING "status"::"text"::"public"."import_data_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "import_data" ALTER COLUMN "status" SET DEFAULT 'processing'`);
        await queryRunner.query(`DROP TYPE "public"."import_data_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."import_data_status_enum_old" RENAME TO "import_data_status_enum"`);
        await queryRunner.query(`ALTER TABLE "import_data" DROP COLUMN "filepath"`);
    }

}
