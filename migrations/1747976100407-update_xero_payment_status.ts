import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateXeroPaymentStatus1747976100407
  implements MigrationInterface
{
  name = 'UpdateXeroPaymentStatus1747976100407';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_status_enum" RENAME TO "xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_status_enum" AS ENUM('INIT', 'PENDING', 'SUCCEED', 'FAILED', 'VOIDED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" TYPE "public"."xero_payment_status_enum" USING "status"::"text"::"public"."xero_payment_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_history_status_enum" RENAME TO "xero_payment_history_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_history_status_enum" AS ENUM('INIT', 'PENDING', 'SUCCEED', 'FAILED', 'VOIDED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" TYPE "public"."xero_payment_history_status_enum" USING "status"::"text"::"public"."xero_payment_history_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_history_status_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_history_status_enum_old" AS ENUM('PENDING', 'SUCCEED', 'FAILED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" TYPE "public"."xero_payment_history_status_enum_old" USING "status"::"text"::"public"."xero_payment_history_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_history_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_history_status_enum_old" RENAME TO "xero_payment_history_status_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_status_enum_old" AS ENUM('PENDING', 'SUCCEED', 'FAILED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" TYPE "public"."xero_payment_status_enum_old" USING "status"::"text"::"public"."xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(`DROP TYPE "public"."xero_payment_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_status_enum_old" RENAME TO "xero_payment_status_enum"`,
    );
  }
}
