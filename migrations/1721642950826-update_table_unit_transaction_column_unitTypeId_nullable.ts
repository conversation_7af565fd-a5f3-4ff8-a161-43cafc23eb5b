import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableUnitTransactionColumnUnitTypeIdNullable1721642950826
  implements MigrationInterface
{
  name = 'UpdateTableUnitTransactionColumnUnitTypeIdNullable1721642950826';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ALTER COLUMN "unitTypeId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ALTER COLUMN "unitTypeId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_4033b35143992f7dfd9df5d67d1" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
