import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1746601788882 implements MigrationInterface {
  name = 'UpdateInvoice1746601788882';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "amount"`);
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "issuedAt"`);
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "paidAt"`);
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_f803cfe7b8417f03ac350374af8" FOREIGN KEY ("transactionId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_f803cfe7b8417f03ac350374af8"`,
    );
    await queryRunner.query(`ALTER TABLE "invoice" ADD "paidAt" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "invoice" ADD "issuedAt" TIMESTAMP`);
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "amount" numeric(10,2) NOT NULL`,
    );
  }
}
