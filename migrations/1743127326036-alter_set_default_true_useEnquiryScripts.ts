import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterSetDefaultTrueUseEnquiryScripts1743127326036
  implements MigrationInterface
{
  name = 'AlterSetDefaultTrueUseEnquiryScripts1743127326036';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ALTER COLUMN "useEnquiryScript" SET DEFAULT true`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ALTER COLUMN "useEnquiryScript" SET DEFAULT false`,
    );
  }
}
