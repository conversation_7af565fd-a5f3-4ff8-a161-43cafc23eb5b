import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeDirectoryFeature1742266962976
  implements MigrationInterface
{
  name = 'AddTypeDirectoryFeature1742266962976';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum" RENAME TO "feature_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account', 'round-robin', 'directory')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum" USING "type"::"text"::"public"."feature_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum_old" AS ENUM('dynamic-layout', 'ip-access-tracker-with-telegram-alerts', 'multiple-contact', 'project-landing-page', 'round-robin', 'team-account')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum_old" USING "type"::"text"::"public"."feature_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum_old" RENAME TO "feature_type_enum"`,
    );
  }
}
