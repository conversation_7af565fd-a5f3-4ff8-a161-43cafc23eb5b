import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserConfigMultiLanguage1717481854646
  implements MigrationInterface
{
  name = 'UpdateUserConfigMultiLanguage1717481854646';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "siteTitle"`,
    );
    await queryRunner.query(`ALTER TABLE "user_config" ADD "siteTitle" jsonb`);
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "siteDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "siteDescription" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "siteDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "siteDescription" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "siteTitle"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "siteTitle" character varying`,
    );
  }
}
