import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProjectEntityWhatIsNextStatusColumns1745208877209
  implements MigrationInterface
{
  name = 'AddProjectEntityWhatIsNextStatusColumns1745208877209';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."project_whatisnextstatus_enum" AS ENUM('pre-launch', 'preview', 'post-launch')`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD "whatIsNextStatus" "public"."project_whatisnextstatus_enum" NOT NULL DEFAULT 'pre-launch'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "whatIsNextStatus"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."project_whatisnextstatus_enum"`,
    );
  }
}
