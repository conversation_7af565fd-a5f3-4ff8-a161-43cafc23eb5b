import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBrochureAssetIdInProject1745489833536
  implements MigrationInterface
{
  name = 'AddBrochureAssetIdInProject1745489833536';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" ADD "brochureAssetId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_66109077b5241aac00cdbde9c34" UNIQUE ("brochureAssetId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_66109077b5241aac00cdbde9c34" FOREIGN KEY ("brochureAssetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_66109077b5241aac00cdbde9c34"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_66109077b5241aac00cdbde9c34"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "brochureAssetId"`,
    );
  }
}
