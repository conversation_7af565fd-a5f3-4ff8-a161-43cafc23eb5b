import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateColumnLayoutOptionLandingPage1733372308553
  implements MigrationInterface
{
  name = 'CreateColumnLayoutOptionLandingPage1733372308553';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "layoutOption" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "layoutOption"`,
    );
  }
}
