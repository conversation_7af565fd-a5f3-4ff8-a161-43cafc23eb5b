import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProjectDeveloperRelation1717042596973
  implements MigrationInterface
{
  name = 'AddProjectDeveloperRelation1717042596973';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_d0c555ba854452ca05fd57a0cc2" FOREIGN KEY ("developerId") REFERENCES "developer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_d0c555ba854452ca05fd57a0cc2"`,
    );
  }
}
