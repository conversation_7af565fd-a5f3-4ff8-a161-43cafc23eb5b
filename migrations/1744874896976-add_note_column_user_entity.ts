import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNoteColumnUserEntity1744874896976
  implements MigrationInterface
{
  name = 'AddNoteColumnUserEntity1744874896976';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "note" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "note"`);
  }
}
