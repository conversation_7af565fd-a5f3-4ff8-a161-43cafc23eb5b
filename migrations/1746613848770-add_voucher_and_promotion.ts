import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVoucherAndPromotion1746613848770 implements MigrationInterface {
  name = 'AddVoucherAndPromotion1746613848770';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_discounttype_enum" AS ENUM('PERCENT', 'PRICE')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_status_enum" AS ENUM('ACTIVE', 'DE_ACTIVE')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_type_enum" AS ENUM('PACKAGE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "promotion" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "description" character varying, "discount" numeric(10,2), "discountType" "public"."promotion_discounttype_enum", "status" "public"."promotion_status_enum" NOT NULL DEFAULT 'ACTIVE', "startDate" TIMESTAMP WITH TIME ZONE, "endDate" TIMESTAMP WITH TIME ZONE, "type" "public"."promotion_type_enum", CONSTRAINT "PK_fab3630e0789a2002f1cadb7d38" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "voucher" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "code" character varying NOT NULL, "used" boolean NOT NULL DEFAULT false, "promotionId" uuid NOT NULL, CONSTRAINT "PK_677ae75f380e81c2f103a57ffaf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD CONSTRAINT "FK_60e3cfa27ef8f3e35d9582f249f" FOREIGN KEY ("promotionId") REFERENCES "promotion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "voucher" DROP CONSTRAINT "FK_60e3cfa27ef8f3e35d9582f249f"`,
    );
    await queryRunner.query(`DROP TABLE "voucher"`);
    await queryRunner.query(`DROP TABLE "promotion"`);
    await queryRunner.query(`DROP TYPE "public"."promotion_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."promotion_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."promotion_discounttype_enum"`);
  }
}
