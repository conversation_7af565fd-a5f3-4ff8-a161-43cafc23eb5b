import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUpcommingLaunchTableUserProject1720758500406
  implements MigrationInterface
{
  name = 'AddColumnUpcommingLaunchTableUserProject1720758500406';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project" ADD "upcomingLaunch" boolean`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project" DROP COLUMN "upcomingLaunch"`,
    );
  }
}
