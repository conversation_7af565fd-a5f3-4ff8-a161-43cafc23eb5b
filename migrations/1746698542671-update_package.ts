import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePackage1746698542671 implements MigrationInterface {
  name = 'UpdatePackage1746698542671';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "package" ADD "isPopular" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "package" DROP COLUMN "isPopular"`);
  }
}
