import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNolimitipInApiKey1747298166485 implements MigrationInterface {
  name = 'AddNolimitipInApiKey1747298166485';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "api_key" ADD "noLimitIp" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "api_key" DROP COLUMN "noLimitIp"`);
  }
}
