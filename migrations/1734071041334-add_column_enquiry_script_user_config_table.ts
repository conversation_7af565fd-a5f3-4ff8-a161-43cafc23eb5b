import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnEnquiryScriptUserConfigTable1734071041334
  implements MigrationInterface
{
  name = 'AddColumnEnquiryScriptUserConfigTable1734071041334';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "enquiryScript" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "enquiryScript"`,
    );
  }
}
