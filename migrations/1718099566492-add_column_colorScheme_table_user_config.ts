import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnColorSchemeTableUserConfig1718099566492
  implements MigrationInterface
{
  name = 'AddColumnColorSchemeTableUserConfig1718099566492';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "domain" ADD "primary" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "colorScheme" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "colorScheme"`,
    );
    await queryRunner.query(`ALTER TABLE "domain" DROP COLUMN "primary"`);
  }
}
