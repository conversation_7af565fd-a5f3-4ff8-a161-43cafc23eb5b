import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateXeroPaymentHistory1746783961222
  implements MigrationInterface
{
  name = 'UpdateXeroPaymentHistory1746783961222';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "type" TO "xeroPaymentType"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_history_type_enum" RENAME TO "xero_payment_history_xeropaymenttype_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_history_xeropaymenttype_enum" RENAME TO "xero_payment_history_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "xeroPaymentType" TO "type"`,
    );
  }
}
