import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1746695205698 implements MigrationInterface {
  name = 'UpdateInvoice1746695205698';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "REL_4ef7f83319a9e1a502c244a8f1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP COLUMN "invoiceId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "totalDue" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8" UNIQUE ("xeroPaymentId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "totalDue"`);
    await queryRunner.query(`ALTER TABLE "xero_payment" ADD "invoiceId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "REL_4ef7f83319a9e1a502c244a8f1" UNIQUE ("invoiceId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_4ef7f83319a9e1a502c244a8f17" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
