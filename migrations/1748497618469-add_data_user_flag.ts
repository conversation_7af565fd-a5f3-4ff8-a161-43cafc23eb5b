import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDataUserFlag1748497618469 implements MigrationInterface {
  name = 'AddDataUserFlag1748497618469';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_flag" ADD "data" json`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_flag" DROP COLUMN "data"`);
  }
}
