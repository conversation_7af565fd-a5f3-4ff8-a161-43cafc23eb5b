import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemplate1720774131940 implements MigrationInterface {
  name = 'CreateTemplate1720774131940';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."template_type_enum" AS ENUM('whats_app')`);
    await queryRunner.query(`CREATE TABLE "template" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "subject" character varying NOT NULL, "content" text NOT NULL, "type" "public"."template_type_enum" NOT NULL DEFAULT 'whats_app', "isAdmin" boolean NOT NULL DEFAULT false, "userId" uuid, CONSTRAINT "PK_fbae2ac36bd9b5e1e793b957b7f" PRIMARY KEY ("id"))`);
    await queryRunner.query(`ALTER TABLE "template" ADD CONSTRAINT "FK_5e718539594d02a4c75ddc1ca56" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "template" DROP CONSTRAINT "FK_5e718539594d02a4c75ddc1ca56"`);
    await queryRunner.query(`DROP TABLE "template"`);
    await queryRunner.query(`DROP TYPE "public"."template_type_enum"`);
  }
}
