import { MigrationInterface, QueryRunner } from "typeorm";

export class AddContactSaleSubmissionLog1729062635203 implements MigrationInterface {
    name = 'AddContactSaleSubmissionLog1729062635203'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."contact_sale_submission_log_type_enum" AS ENUM('appointmentDate', 'projectViewCount', 'interested', 'unitType', 'projectViewDuration', 'redFlag')`);
        await queryRunner.query(`CREATE TABLE "contact_sale_submission_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "type" "public"."contact_sale_submission_log_type_enum" NOT NULL, "point" integer NOT NULL, "reason" character varying NOT NULL, "submissionId" uuid, CONSTRAINT "PK_3cd047e9ca347075da542ec04e3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "contact_sale_submission_log" ADD CONSTRAINT "FK_25a6becb701eb1c0272103e1d22" FOREIGN KEY ("submissionId") REFERENCES "contact_sale_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact_sale_submission_log" DROP CONSTRAINT "FK_25a6becb701eb1c0272103e1d22"`);
        await queryRunner.query(`DROP TABLE "contact_sale_submission_log"`);
        await queryRunner.query(`DROP TYPE "public"."contact_sale_submission_log_type_enum"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "PK_3715b9223c0b9043c75edad50b1" ON "contact_sale_submission" ("id") `);
    }
}
