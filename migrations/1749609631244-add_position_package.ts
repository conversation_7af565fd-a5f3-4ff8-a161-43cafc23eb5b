import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPositionPackage1749609631244 implements MigrationInterface {
  name = 'AddPositionPackage1749609631244';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "package" ADD "position" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "package" DROP COLUMN "position"`);
  }
}
