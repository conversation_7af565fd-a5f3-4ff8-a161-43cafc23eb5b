import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveTableUnit1718277929068 implements MigrationInterface {
  name = 'RemoveTableUnit1718277929068';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_bf8512a1eeeb24b8885c2e09b67"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_f0e99f20131e3f58d8f2a2333db"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_081a1021523202d85962a6ef10c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902"`,
    );

    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD "availableUnits" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD "totalUnits" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD "minPrice" double precision NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD "maxPrice" double precision NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "projectId" uuid NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "REL_bf8512a1eeeb24b8885c2e09b6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "REL_57c9c6b8c1a4d4a1f5b0623490"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_e7b5e15cd61eb77e67dba92f76e" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "unitId"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD "unitId" uuid NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_e7b5e15cd61eb77e67dba92f76e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "REL_57c9c6b8c1a4d4a1f5b0623490" UNIQUE ("transactionId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "REL_bf8512a1eeeb24b8885c2e09b6" UNIQUE ("unitId")`,
    );

    await queryRunner.query(
      `ALTER TABLE "unit_transaction" DROP COLUMN "projectId"`,
    );
    await queryRunner.query(`ALTER TABLE "floor_plan" DROP COLUMN "maxPrice"`);
    await queryRunner.query(`ALTER TABLE "floor_plan" DROP COLUMN "minPrice"`);
    await queryRunner.query(
      `ALTER TABLE "floor_plan" DROP COLUMN "totalUnits"`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" DROP COLUMN "availableUnits"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902" FOREIGN KEY ("transactionId") REFERENCES "unit_transaction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "FK_081a1021523202d85962a6ef10c" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "FK_f0e99f20131e3f58d8f2a2333db" FOREIGN KEY ("floorPlanId") REFERENCES "floor_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_bf8512a1eeeb24b8885c2e09b67" FOREIGN KEY ("unitId") REFERENCES "unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
