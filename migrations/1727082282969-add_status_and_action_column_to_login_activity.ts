import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusAndActionColumnToLoginActivity1727082282969 implements MigrationInterface {
    name = 'AddStatusAndActionColumnToLoginActivity1727082282969'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."login_activity_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "status" "public"."login_activity_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."login_activity_action_enum" AS ENUM('login', 'logout')`);
        await queryRunner.query(`ALTER TABLE "login_activity" ADD "action" "public"."login_activity_action_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "action"`);
        await queryRunner.query(`DROP TYPE "public"."login_activity_action_enum"`);
        await queryRunner.query(`ALTER TABLE "login_activity" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."login_activity_status_enum"`);
    }

}
