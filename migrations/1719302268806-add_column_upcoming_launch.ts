import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUpcomingLaunch1719302268806
  implements MigrationInterface
{
  name = 'AddColumnUpcomingLaunch1719302268806';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "upcomingLaunch" boolean`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "upcomingLaunch"`,
    );
  }
}
