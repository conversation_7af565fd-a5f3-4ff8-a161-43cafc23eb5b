import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserCompanyColumn1722565598413 implements MigrationInterface {
  name = 'AddUserCompanyColumn1722565598413';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "company" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "company"`);
  }
}
