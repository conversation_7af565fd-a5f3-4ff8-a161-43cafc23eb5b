import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameColumnHeaderBodyScript1733740784906
  implements MigrationInterface
{
  name = 'RenameColumnHeaderBodyScript1733740784906';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "headerAnalyticUrl"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "bodyAnalyticUrl"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "headerScript" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "bodyScript" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "bodyScript"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "headerScript"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "bodyAnalyticUrl" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "headerAnalyticUrl" character varying`,
    );
  }
}
