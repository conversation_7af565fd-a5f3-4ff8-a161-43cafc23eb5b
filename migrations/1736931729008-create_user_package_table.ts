import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserPackageTable1736931729008 implements MigrationInterface {
  name = 'CreateUserPackageTable1736931729008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_package" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "packageId" uuid NOT NULL, "userId" uuid NOT NULL, "isExpired" boolean NOT NULL DEFAULT false, "expiredAt" TIMESTAMP, "enabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_252c8e6dd1cf58954b8cdcd1984" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_package" ADD CONSTRAINT "FK_9ea3f4f7dfa5680bcb1d5bf5016" FOREIGN KEY ("packageId") REFERENCES "package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_package" ADD CONSTRAINT "FK_ffe858da52734142aa7591499a8" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_package" DROP CONSTRAINT "FK_ffe858da52734142aa7591499a8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_package" DROP CONSTRAINT "FK_9ea3f4f7dfa5680bcb1d5bf5016"`,
    );
    await queryRunner.query(`DROP TABLE "user_package"`);
  }
}
