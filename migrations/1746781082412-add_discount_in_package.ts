import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiscountInPackage1746781082412 implements MigrationInterface {
  name = 'AddDiscountInPackage1746781082412';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."package_discounttype_enum" AS ENUM('PERCENT', 'PRICE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "package" ADD "discountType" "public"."package_discounttype_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "package" DROP COLUMN "discountType"`);
    await queryRunner.query(`DROP TYPE "public"."package_discounttype_enum"`);
  }
}
