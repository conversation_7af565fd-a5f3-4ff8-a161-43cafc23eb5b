import { MigrationInterface, QueryRunner } from "typeorm";

export class  UpdateValueNotification1721897276529 implements MigrationInterface {
    name = 'UpdateValueNotification1721897276529'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "value"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "value" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notification" DROP COLUMN "value"`);
        await queryRunner.query(`ALTER TABLE "notification" ADD "value" character varying`);
    }

}
