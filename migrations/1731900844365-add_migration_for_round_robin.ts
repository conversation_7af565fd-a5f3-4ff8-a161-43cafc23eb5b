import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMigrationForRoundRobin1731900844365
  implements MigrationInterface
{
  name = 'AddMigrationForRoundRobin1731900844365';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "time_slot" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "deletedAt" TIMESTAMP,
          "agencyId" uuid NOT NULL,
          "startTime" character varying NOT NULL,
          "endTime" character varying NOT NULL,
          CONSTRAINT "PK_03f782f8c4af029253f6ad5bacf" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`
      CREATE TABLE "round_robin_config" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "deletedAt" TIMESTAMP,
          "enabled" boolean NOT NULL DEFAULT true,
          "agencyId" uuid NOT NULL,
          CONSTRAINT "REL_5dbb89883240c149b954e5405d" UNIQUE ("agencyId"),
          CONSTRAINT "PK_d4d704589e16e2e013cc5262310" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`
      CREATE TABLE "lead_history" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
          "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "deletedAt" TIMESTAMP,
          "contactSaleSubmissionId" uuid NOT NULL,
          "assignedToId" uuid NOT NULL,
          "distributedAt" TIMESTAMP NOT NULL DEFAULT now(),
          "agencyId" uuid NOT NULL,
          CONSTRAINT "PK_480752a3c0f39333d322658dcab" PRIMARY KEY ("id")
      )
    `);
    await queryRunner.query(`
      CREATE TABLE "users_timeslots" (
          "time_slot_id" uuid NOT NULL,
          "user_id" uuid NOT NULL,
          CONSTRAINT "PK_c315b174ba20d2a10a00dd8559d" PRIMARY KEY ("time_slot_id", "user_id")
      )
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_4141a42abb4da8ff95d8f966ba" ON "users_timeslots" ("time_slot_id")
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_29f982e149e184ab722dcc7d8c" ON "users_timeslots" ("user_id")
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."contact_sale_submission_status_enum" AS ENUM('Unassigned', 'Assigned')
    `);
    await queryRunner.query(`
      ALTER TABLE "contact_sale_submission"
      ADD "status" "public"."contact_sale_submission_status_enum" NOT NULL DEFAULT 'Unassigned'
    `);
    await queryRunner.query(`
      ALTER TABLE "time_slot"
      ADD CONSTRAINT "FK_81a8354b4ecde882903214a6d56" FOREIGN KEY ("agencyId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "round_robin_config"
      ADD CONSTRAINT "FK_5dbb89883240c149b954e5405df" FOREIGN KEY ("agencyId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history"
      ADD CONSTRAINT "FK_d7288eaa41ed1686bd325ccaf79" FOREIGN KEY ("contactSaleSubmissionId") REFERENCES "contact_sale_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history"
      ADD CONSTRAINT "FK_e3e6b9fc641b6a56009e4146649" FOREIGN KEY ("assignedToId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history"
      ADD CONSTRAINT "FK_292b70c863b82cec18f6e77da92" FOREIGN KEY ("agencyId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(`
      ALTER TABLE "users_timeslots"
      ADD CONSTRAINT "FK_4141a42abb4da8ff95d8f966ba5" FOREIGN KEY ("time_slot_id") REFERENCES "time_slot"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);
    await queryRunner.query(`
      ALTER TABLE "users_timeslots"
      ADD CONSTRAINT "FK_29f982e149e184ab722dcc7d8ce" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum" RENAME TO "feature_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account', 'round-robin')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum" USING "type"::"text"::"public"."feature_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum_old" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum_old" USING "type"::"text"::"public"."feature_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum_old" RENAME TO "feature_type_enum"`,
    );
    await queryRunner.query(`
      ALTER TABLE "users_timeslots" DROP CONSTRAINT "FK_29f982e149e184ab722dcc7d8ce"
    `);
    await queryRunner.query(`
      ALTER TABLE "users_timeslots" DROP CONSTRAINT "FK_4141a42abb4da8ff95d8f966ba5"
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history" DROP CONSTRAINT "FK_292b70c863b82cec18f6e77da92"
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history" DROP CONSTRAINT "FK_e3e6b9fc641b6a56009e4146649"
    `);
    await queryRunner.query(`
      ALTER TABLE "lead_history" DROP CONSTRAINT "FK_d7288eaa41ed1686bd325ccaf79"
    `);
    await queryRunner.query(`
      ALTER TABLE "round_robin_config" DROP CONSTRAINT "FK_5dbb89883240c149b954e5405df"
    `);
    await queryRunner.query(`
      ALTER TABLE "time_slot" DROP CONSTRAINT "FK_81a8354b4ecde882903214a6d56"
    `);
    await queryRunner.query(`
      DROP INDEX "public"."IDX_29f982e149e184ab722dcc7d8c"
    `);
    await queryRunner.query(`
      DROP INDEX "public"."IDX_4141a42abb4da8ff95d8f966ba"
    `);
    await queryRunner.query(`DROP TABLE "users_timeslots"`);
    await queryRunner.query(`DROP TABLE "lead_history"`);
    await queryRunner.query(`DROP TABLE "round_robin_config"`);
    await queryRunner.query(`DROP TABLE "time_slot"`);
    await queryRunner.query(`
      ALTER TABLE "contact_sale_submission" DROP COLUMN "status"
    `);
    await queryRunner.query(`
      DROP TYPE "public"."contact_sale_submission_status_enum"
    `);
  }
}
