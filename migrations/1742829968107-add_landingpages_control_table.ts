import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLandingpagesControlTable1742829968107
  implements MigrationInterface
{
  name = 'AddLandingpagesControlTable1742829968107';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "landingpages_control" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "useProjectSgTrackingId" boolean NOT NULL DEFAULT false, "userId" character varying NOT NULL, CONSTRAINT "PK_68d429764512f5752d25c5accc1" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "landingpages_control"`);
  }
}
