import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminNoteContactSaleSubmission1750215343100
  implements MigrationInterface
{
  name = 'AddAdminNoteContactSaleSubmission1750215343100';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "adminNote" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "adminNote"`,
    );
  }
}
