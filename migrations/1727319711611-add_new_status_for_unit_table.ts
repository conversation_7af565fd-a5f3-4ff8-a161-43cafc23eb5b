import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewStatusForUnitTable1727319711611 implements MigrationInterface {
    name = 'AddNewStatusForUnitTable1727319711611'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "price" DROP NOT NULL`);
        await queryRunner.query(`ALTER TYPE "public"."unit_status_enum" RENAME TO "unit_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."unit_status_enum" AS ENUM('available', 'sold', 'not released', 'reserved')`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" TYPE "public"."unit_status_enum" USING "status"::"text"::"public"."unit_status_enum"`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" SET DEFAULT 'available'`);
        await queryRunner.query(`DROP TYPE "public"."unit_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."unit_status_enum_old" AS ENUM('available', 'sold')`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" TYPE "public"."unit_status_enum_old" USING "status"::"text"::"public"."unit_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "status" SET DEFAULT 'available'`);
        await queryRunner.query(`DROP TYPE "public"."unit_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."unit_status_enum_old" RENAME TO "unit_status_enum"`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "price" SET NOT NULL`);
    }

}
