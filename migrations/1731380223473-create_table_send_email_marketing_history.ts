import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableSendEmailMarketingHistory1731380223473
  implements MigrationInterface
{
  name = 'CreateTableSendEmailMarketingHistory1731380223473';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "send_email_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "email" character varying NOT NULL, "subject" character varying NOT NULL, "body" character varying NOT NULL, CONSTRAINT "PK_8a73ede2157553a4d0df7444a92" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "send_email_history"`);
  }
}
