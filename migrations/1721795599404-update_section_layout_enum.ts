import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSectionLayoutEnum1721795599404
  implements MigrationInterface
{
  name = 'UpdateSectionLayoutEnum1721795599404';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."section_layout_enum" RENAME TO "section_layout_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."section_layout_enum" AS ENUM('SectionFeaturedLaunches', 'SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix', 'SectionHTMLContent', 'SectionPromotionBanner', 'SectionCTA')`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" TYPE "public"."section_layout_enum" USING "layout"::"text"::"public"."section_layout_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" SET DEFAULT 'SectionGalleryThree'`,
    );
    await queryRunner.query(`DROP TYPE "public"."section_layout_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."section_layout_enum_old" AS ENUM('SectionFeaturedLaunches', 'SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix', 'SectionHTMLContent', 'SectionPromotionBanner', 'SectionBottomCTA')`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" TYPE "public"."section_layout_enum_old" USING "layout"::"text"::"public"."section_layout_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" SET DEFAULT 'SectionGalleryThree'`,
    );
    await queryRunner.query(`DROP TYPE "public"."section_layout_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."section_layout_enum_old" RENAME TO "section_layout_enum"`,
    );
  }
}
