import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProjectStatsUnitsSoldCount1721794665672
  implements MigrationInterface
{
  name = 'UpdateProjectStatsUnitsSoldCount1721794665672';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project_stats" ADD "unitsSoldCount" integer NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project_stats" DROP COLUMN "unitsSoldCount"`,
    );
  }
}
