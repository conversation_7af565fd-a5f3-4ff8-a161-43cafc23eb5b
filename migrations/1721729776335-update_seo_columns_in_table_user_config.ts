import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSeoColumnsInTableUserConfig1721729776335
  implements MigrationInterface
{
  name = 'UpdateSeoColumnsInTableUserConfig1721729776335';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "promotionSeoImageId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_081b694bf1c818a0bb5209924ae" UNIQUE ("promotionSeoImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "promotionSeoDescription" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "promotionSeoTitle" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "upcomingSeoImageId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_bd19b6c934fc25149e78416f669" UNIQUE ("upcomingSeoImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "upcomingSeoDescription" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "upcomingSeoTitle" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "topSeoImageId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_03d6260f93597a2c2bc8b9d3633" UNIQUE ("topSeoImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "topSeoDescription" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "topSeoTitle" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_081b694bf1c818a0bb5209924ae" FOREIGN KEY ("promotionSeoImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_bd19b6c934fc25149e78416f669" FOREIGN KEY ("upcomingSeoImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_03d6260f93597a2c2bc8b9d3633" FOREIGN KEY ("topSeoImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_03d6260f93597a2c2bc8b9d3633"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_bd19b6c934fc25149e78416f669"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_081b694bf1c818a0bb5209924ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "topSeoTitle"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "topSeoDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_03d6260f93597a2c2bc8b9d3633"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "topSeoImageId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "upcomingSeoTitle"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "upcomingSeoDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_bd19b6c934fc25149e78416f669"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "upcomingSeoImageId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "promotionSeoTitle"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "promotionSeoDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_081b694bf1c818a0bb5209924ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "promotionSeoImageId"`,
    );
  }
}
