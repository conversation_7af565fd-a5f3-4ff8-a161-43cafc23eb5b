import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldPricingPlansUserEntity1741661929208
  implements MigrationInterface
{
  name = 'AddFieldPricingPlansUserEntity1741661929208';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "pricingPlans" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "pricingPlans"`);
  }
}
