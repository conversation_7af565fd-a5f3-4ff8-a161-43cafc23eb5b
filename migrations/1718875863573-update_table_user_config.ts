import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableUserConfig1718875863573 implements MigrationInterface {
  name = 'UpdateTableUserConfig1718875863573';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "agencyEmail" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "agencyName" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "agencyPhotoId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_b597e3087088e665c4157ba4774" UNIQUE ("agencyPhotoId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_b597e3087088e665c4157ba4774" FOREIGN KEY ("agencyPhotoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_b597e3087088e665c4157ba4774"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_b597e3087088e665c4157ba4774"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "agencyPhotoId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "agencyName"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "agencyEmail"`,
    );
  }
}
