import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsShowCollumnInUserProjectTable1724210431669 implements MigrationInterface {
    name = 'AddIsShowCollumnInUserProjectTable1724210431669'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_project" ADD "isShow" boolean DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_project" DROP COLUMN "isShow"`);
    }

}
