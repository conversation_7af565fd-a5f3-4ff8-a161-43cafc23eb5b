import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMigrationForFeatureEnum1731895095990
  implements MigrationInterface
{
  name = 'AddMigrationForFeatureEnum1731895095990';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum" RENAME TO "feature_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum" USING "type"::"text"::"public"."feature_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum_old" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum_old" USING "type"::"text"::"public"."feature_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum_old" RENAME TO "feature_type_enum"`,
    );
  }
}
