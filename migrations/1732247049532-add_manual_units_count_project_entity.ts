import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddManualUnitsCountProjectEntity1732247049532
  implements MigrationInterface
{
  name = 'AddManualUnitsCountProjectEntity1732247049532';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "manualTotalUnitsCount" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD "manualAvailableUnitsCount" integer NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "manualAvailableUnitsCount"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "manualTotalUnitsCount"`,
    );
  }
}
