import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDeveloper1747895064245 implements MigrationInterface {
  name = 'UpdateDeveloper1747895064245';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "developer" ADD "address" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "developer" ADD "isShowAddress" boolean`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "developer" DROP COLUMN "isShowAddress"`,
    );
    await queryRunner.query(`ALTER TABLE "developer" DROP COLUMN "address"`);
  }
}
