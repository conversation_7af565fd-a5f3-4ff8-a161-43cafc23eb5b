import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1746679197161 implements MigrationInterface {
  name = 'UpdateInvoice1746679197161';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_f803cfe7b8417f03ac350374af8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP COLUMN "transactionId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "originPrice" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "discountPrice" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "totalTax" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "total" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "paid" numeric(10,2) NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "invoice" ADD "xeroPaymentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8" UNIQUE ("xeroPaymentId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ALTER COLUMN "xeroInvoiceId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ALTER COLUMN "xeroInvoiceId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "UQ_2477f43bf198dae5ef29d8d7bc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP COLUMN "xeroPaymentId"`,
    );
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "paid"`);
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "total"`);
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "totalTax"`);
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP COLUMN "discountPrice"`,
    );
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "originPrice"`);
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD "transactionId" uuid NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_f803cfe7b8417f03ac350374af8" FOREIGN KEY ("transactionId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
