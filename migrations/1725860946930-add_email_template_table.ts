import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEmailTemplateTable1725860946930 implements MigrationInterface {
  name = 'AddEmailTemplateTable1725860946930';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "email_template" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "subject" character varying NOT NULL, "body" character varying NOT NULL, "params" character varying array, CONSTRAINT "PK_c90815fd4ca9119f19462207710" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "email_template"`);
  }
}
