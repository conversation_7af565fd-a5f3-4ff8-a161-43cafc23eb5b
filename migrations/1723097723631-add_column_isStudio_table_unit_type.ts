import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsStudioTableUnitType1723097723631
  implements MigrationInterface
{
  name = 'AddColumnIsStudioTableUnitType1723097723631';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isStudio" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "isStudio"`);
  }
}
