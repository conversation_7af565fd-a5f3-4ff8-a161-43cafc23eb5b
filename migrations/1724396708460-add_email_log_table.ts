import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailLogTable1724396708460 implements MigrationInterface {
    name = 'AddEmailLogTable1724396708460'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "email_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "sender" character varying NOT NULL, "receiver" character varying NOT NULL, "subject" character varying NOT NULL, "template" character varying NOT NULL, "params" jsonb NOT NULL, "failedReason" character varying, "text" character varying, CONSTRAINT "PK_edfd3f7225051fc07bdd63a22dc" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "email_log"`);
    }

}
