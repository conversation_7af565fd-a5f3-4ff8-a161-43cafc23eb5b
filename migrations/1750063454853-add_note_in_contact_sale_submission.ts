import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNoteInContactSaleSubmission1750063454853
  implements MigrationInterface
{
  name = 'AddNoteInContactSaleSubmission1750063454853';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "note" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "note"`,
    );
  }
}
