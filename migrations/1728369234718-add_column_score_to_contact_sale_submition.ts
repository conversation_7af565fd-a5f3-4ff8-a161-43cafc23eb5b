import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnScoreToContactSaleSubmition1728369234718
  implements MigrationInterface
{
  name = 'AddColumnScoreToContactSaleSubmition1728369234718';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "score" integer NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "score"`,
    );
  }
}
