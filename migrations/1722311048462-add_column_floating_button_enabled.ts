import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnFloatingButtonEnabled1722311048462
  implements MigrationInterface
{
  name = 'AddColumnFloatingButtonEnabled1722311048462';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "floatingButtonEnabled" boolean NOT NULL DEFAULT true`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "floatingButtonEnabled"`,
    );
  }
}
