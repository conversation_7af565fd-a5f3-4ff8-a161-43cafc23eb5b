import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserFlag1748489233530 implements MigrationInterface {
  name = 'AddUserFlag1748489233530';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_flag_type_enum" AS ENUM('payment_confirmed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_flag" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "type" "public"."user_flag_type_enum" NOT NULL, CONSTRAINT "PK_5e2839d501e541edabc79448cab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_flag" ADD CONSTRAINT "FK_69c007a2a09f45620dc674067d2" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_flag" DROP CONSTRAINT "FK_69c007a2a09f45620dc674067d2"`,
    );
    await queryRunner.query(`DROP TABLE "user_flag"`);
    await queryRunner.query(`DROP TYPE "public"."user_flag_type_enum"`);
  }
}
