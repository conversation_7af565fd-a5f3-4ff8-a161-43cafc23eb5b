import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSectionCtaAndPromotionBanner1721720310908
  implements MigrationInterface
{
  name = 'UpdateSectionCtaAndPromotionBanner1721720310908';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "section_promotion_banner" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "photoId" uuid NOT NULL, "url" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "sectionId" uuid NOT NULL, "weight" integer NOT NULL DEFAULT '1', "weightTimestamp" TIMESTAMP WITH TIME ZONE DEFAULT now(), CONSTRAINT "REL_893536e77610736427a452bf95" UNIQUE ("photoId"), CONSTRAINT "PK_bed1038a5b381747b1283fb596d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "section" ADD "photoId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "UQ_d6ffd4dd58808c7767727504935" UNIQUE ("photoId")`,
    );
    await queryRunner.query(`ALTER TABLE "section" ADD "cta" jsonb`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_relation_enum" RENAME TO "asset_relation_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_relation_enum" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner', 'virtual_tour', 'mortgage_bank', 'section')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum" USING "relation"::"text"::"public"."asset_relation_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."section_layout_enum" RENAME TO "section_layout_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."section_layout_enum" AS ENUM('SectionFeaturedLaunches', 'SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix', 'SectionHTMLContent', 'SectionPromotionBanner', 'SectionBottomCTA')`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" TYPE "public"."section_layout_enum" USING "layout"::"text"::"public"."section_layout_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" SET DEFAULT 'SectionGalleryThree'`,
    );
    await queryRunner.query(`DROP TYPE "public"."section_layout_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "FK_d6ffd4dd58808c7767727504935" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_promotion_banner" ADD CONSTRAINT "FK_893536e77610736427a452bf958" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_promotion_banner" ADD CONSTRAINT "FK_0e6f795cbb228ff700f283c4103" FOREIGN KEY ("sectionId") REFERENCES "section"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "section_promotion_banner" DROP CONSTRAINT "FK_0e6f795cbb228ff700f283c4103"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_promotion_banner" DROP CONSTRAINT "FK_893536e77610736427a452bf958"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "FK_d6ffd4dd58808c7767727504935"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."section_layout_enum_old" AS ENUM('SectionFeaturedLaunches', 'SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix', 'SectionHTMLContent')`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" TYPE "public"."section_layout_enum_old" USING "layout"::"text"::"public"."section_layout_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ALTER COLUMN "layout" SET DEFAULT 'SectionGalleryThree'`,
    );
    await queryRunner.query(`DROP TYPE "public"."section_layout_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."section_layout_enum_old" RENAME TO "section_layout_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_relation_enum_old" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner', 'virtual_tour', 'mortgage_bank')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum_old" USING "relation"::"text"::"public"."asset_relation_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_relation_enum_old" RENAME TO "asset_relation_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "section" DROP COLUMN "cta"`);
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "UQ_d6ffd4dd58808c7767727504935"`,
    );
    await queryRunner.query(`ALTER TABLE "section" DROP COLUMN "photoId"`);
    await queryRunner.query(`DROP TABLE "section_promotion_banner"`);
  }
}
