import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLoginActivityTable1726652572390
  implements MigrationInterface
{
  name = 'CreateLoginActivityTable1726652572390';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "login_activity" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "device" character varying NOT NULL, "ip" character varying NOT NULL, "location" character varying NOT NULL, "referral" character varying NOT NULL, CONSTRAINT "PK_e1111e34d9a5759ca81646516a9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "login_activity" ADD CONSTRAINT "FK_28aab163e727fb7ddbb9f07c485" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "login_activity" DROP CONSTRAINT "FK_28aab163e727fb7ddbb9f07c485"`,
    );
    await queryRunner.query(`DROP TABLE "login_activity"`);
  }
}
