import { MigrationInterface, QueryRunner } from "typeorm";

export class NewUserFeatureTable1726721974285 implements MigrationInterface {
    name = 'NewUserFeatureTable1726721974285'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "user_feature" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "featureId" uuid NOT NULL, "enabled" boolean NOT NULL DEFAULT false, "expiredAt" TIMESTAMP, "activationKey" character varying, CONSTRAINT "PK_7e15bb53d5a314f79c784a2bc27" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user_feature" ADD CONSTRAINT "FK_67b9af97dad40aa55e6ed09db24" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_feature" ADD CONSTRAINT "FK_ad1793551e2629af7fd496e1659" FOREIGN KEY ("featureId") REFERENCES "feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_feature" DROP CONSTRAINT "FK_ad1793551e2629af7fd496e1659"`);
        await queryRunner.query(`ALTER TABLE "user_feature" DROP CONSTRAINT "FK_67b9af97dad40aa55e6ed09db24"`);
        await queryRunner.query(`DROP TABLE "user_feature"`);
    }

}
