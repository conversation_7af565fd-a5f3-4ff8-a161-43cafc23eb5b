import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateXeroInvoiceIdToInvoiceID1747122218897
  implements MigrationInterface
{
  name = 'UpdateXeroInvoiceIdToInvoiceID1747122218897';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice" RENAME COLUMN "xeroInvoiceId" TO "xeroInvoiceID"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME COLUMN "xeroInvoiceId" TO "xeroInvoiceID"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "xeroInvoiceId" TO "xeroInvoiceID"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" RENAME COLUMN "xeroInvoiceID" TO "xeroInvoiceId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" RENAME COLUMN "xeroInvoiceID" TO "xeroInvoiceId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" RENAME COLUMN "xeroInvoiceID" TO "xeroInvoiceId"`,
    );
  }
}
