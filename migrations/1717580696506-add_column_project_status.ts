import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnProjectStatus1717580696506 implements MigrationInterface {
    name = 'AddColumnProjectStatus1717580696506'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."project_status_enum" AS ENUM('by-buc', 'top-soon', 'completed')`);
        await queryRunner.query(`ALTER TABLE "project" ADD "status" "public"."project_status_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."project_status_enum"`);
    }

}
