import { MigrationInterface, QueryRunner } from "typeorm";

export class  RenameSecondYYearRate1721667514542 implements MigrationInterface {
    name = 'RenameSecondYYearRate1721667514542'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" RENAME COLUMN "SecondYearRate" TO "secondYearRate"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" RENAME COLUMN "secondYearRate" TO "SecondYearRate"`);
    }

}
