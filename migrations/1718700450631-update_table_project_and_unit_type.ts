import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableProjectAndUnitType1718700450631
  implements MigrationInterface
{
  name = 'UpdateTableProjectAndUnitType1718700450631';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."category_type_enum" AS ENUM('residential', 'commercial')`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD "type" "public"."category_type_enum" NOT NULL DEFAULT 'residential'`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isPrivateLift" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isCompact" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isCompactPlus" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isDuplex" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isPenthouse" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isUtility" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "hasGuest" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "project" ADD "keyPoint" jsonb`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD "showflatLocation" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "project" ADD "sitePlanImageId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_993ef9098e6d8a5b0c2fb43589f" UNIQUE ("sitePlanImageId")`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."asset_type_enum" RENAME TO "asset_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_type_enum" AS ENUM('image', 'video', 'youtube', 'binary')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" TYPE "public"."asset_type_enum" USING "type"::"text"::"public"."asset_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" SET DEFAULT 'binary'`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_type_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_75e2be4ce11d447ef43be0e374f" UNIQUE ("photoId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_993ef9098e6d8a5b0c2fb43589f" FOREIGN KEY ("sitePlanImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_75e2be4ce11d447ef43be0e374f" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_75e2be4ce11d447ef43be0e374f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_993ef9098e6d8a5b0c2fb43589f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "UQ_75e2be4ce11d447ef43be0e374f"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_type_enum_old" AS ENUM('image', 'video', 'binary')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" TYPE "public"."asset_type_enum_old" USING "type"::"text"::"public"."asset_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" SET DEFAULT 'binary'`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_type_enum_old" RENAME TO "asset_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_993ef9098e6d8a5b0c2fb43589f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "sitePlanImageId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "showflatLocation"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "keyPoint"`);
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "hasGuest"`);
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "isUtility"`);
    await queryRunner.query(
      `ALTER TABLE "unit_type" DROP COLUMN "isPenthouse"`,
    );
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "isDuplex"`);
    await queryRunner.query(
      `ALTER TABLE "unit_type" DROP COLUMN "isCompactPlus"`,
    );
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "isCompact"`);
    await queryRunner.query(
      `ALTER TABLE "unit_type" DROP COLUMN "isPrivateLift"`,
    );
    await queryRunner.query(`ALTER TABLE "category" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."category_type_enum"`);
  }
}
