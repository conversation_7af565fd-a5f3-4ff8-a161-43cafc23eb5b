import { MigrationInterface, QueryRunner } from "typeorm";

export class  UpdateDirectCommission1723895065982 implements MigrationInterface {
    name = 'UpdateDirectCommission1723895065982'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Set NULL values to default value
        await queryRunner.query(`UPDATE "project" SET "directCommission" = '0' WHERE "directCommission" IS NULL`);
    
        // Alter column to NOT NULL and set default value
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "directCommission" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "directCommission" SET DEFAULT '0'`);
    }
    

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "directCommission" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "project" ALTER COLUMN "directCommission" DROP NOT NULL`);
    }

}
