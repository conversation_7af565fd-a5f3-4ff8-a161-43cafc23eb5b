import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateXeroPayment1746692659554 implements MigrationInterface {
  name = 'UpdateXeroPayment1746692659554';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "xero_payment" ADD "voucherId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "UQ_9fbe4c9bb33284acc0078486773" UNIQUE ("voucherId")`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_type_enum" AS ENUM('PACKAGE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD "type" "public"."xero_payment_type_enum" NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "voucher" ADD "xeroPaymentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD CONSTRAINT "UQ_89dea10ec5142fd783a6cbe0ad3" UNIQUE ("xeroPaymentId")`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_status_enum" RENAME TO "xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_status_enum" AS ENUM('PENDING', 'SUCCEED', 'FAILED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" TYPE "public"."xero_payment_status_enum" USING "status"::"text"::"public"."xero_payment_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD CONSTRAINT "FK_9fbe4c9bb33284acc0078486773" FOREIGN KEY ("voucherId") REFERENCES "voucher"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" ADD CONSTRAINT "FK_89dea10ec5142fd783a6cbe0ad3" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "voucher" DROP CONSTRAINT "FK_89dea10ec5142fd783a6cbe0ad3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "FK_9fbe4c9bb33284acc0078486773"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_status_enum_old" AS ENUM('pending', 'succeed', 'failed')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" TYPE "public"."xero_payment_status_enum_old" USING "status"::"text"::"public"."xero_payment_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(`DROP TYPE "public"."xero_payment_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."xero_payment_status_enum_old" RENAME TO "xero_payment_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" DROP CONSTRAINT "UQ_89dea10ec5142fd783a6cbe0ad3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "voucher" DROP COLUMN "xeroPaymentId"`,
    );
    await queryRunner.query(`ALTER TABLE "xero_payment" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."xero_payment_type_enum"`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP CONSTRAINT "UQ_9fbe4c9bb33284acc0078486773"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" DROP COLUMN "voucherId"`,
    );
  }
}
