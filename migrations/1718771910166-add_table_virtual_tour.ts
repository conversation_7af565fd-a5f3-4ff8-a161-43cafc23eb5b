import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableVirtualTour1718771910166 implements MigrationInterface {
  name = 'AddTableVirtualTour1718771910166';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "virtual_tour" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "url" character varying, "thumbnailId" uuid, "projectId" uuid NOT NULL, "unitTypeId" uuid, "minSize" double precision DEFAULT '0', "maxSize" double precision DEFAULT '0', CONSTRAINT "REL_caf1b45e4532eec3be4cf34c93" UNIQUE ("thumbnailId"), CONSTRAINT "PK_3674c07a781debc3e77a0ac7daf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "virtualTour"`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_relation_enum" RENAME TO "asset_relation_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_relation_enum" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner', 'virtual_tour')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum" USING "relation"::"text"::"public"."asset_relation_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" ADD CONSTRAINT "FK_caf1b45e4532eec3be4cf34c93f" FOREIGN KEY ("thumbnailId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" ADD CONSTRAINT "FK_fbe24c57a87c84c2c2ae001d9cf" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" ADD CONSTRAINT "FK_8dae0ab4a5f989f84a083020862" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" DROP CONSTRAINT "FK_8dae0ab4a5f989f84a083020862"`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" DROP CONSTRAINT "FK_fbe24c57a87c84c2c2ae001d9cf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_tour" DROP CONSTRAINT "FK_caf1b45e4532eec3be4cf34c93f"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_relation_enum_old" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum_old" USING "relation"::"text"::"public"."asset_relation_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_relation_enum_old" RENAME TO "asset_relation_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "project" ADD "virtualTour" jsonb`);
    await queryRunner.query(`DROP TABLE "virtual_tour"`);
  }
}
