import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddIsCommissionUpto1721892524840 implements MigrationInterface {
    name = 'AddIsCommissionUpto1721892524840'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "isCommissionUpTo" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "isCommissionUpTo"`);
    }

}
