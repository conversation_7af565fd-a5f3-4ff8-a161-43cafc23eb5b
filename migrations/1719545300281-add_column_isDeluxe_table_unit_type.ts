import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsDeluxeTableUnitType1719545300281
  implements MigrationInterface
{
  name = 'AddColumnIsDeluxeTableUnitType1719545300281';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "isDeluxe" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "isDeluxe"`);
  }
}
