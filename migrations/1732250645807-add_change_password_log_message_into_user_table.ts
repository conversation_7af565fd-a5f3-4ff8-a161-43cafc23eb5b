import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddChangePasswordLogMessageIntoUserTable1732250645807
  implements MigrationInterface
{
  name = 'AddChangePasswordLogMessageIntoUserTable1732250645807';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "changePasswordLogMessage" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "changePasswordLogMessage"`,
    );
  }
}
