import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTablesSectionAndSectionProject1719834123740
  implements MigrationInterface
{
  name = 'AddTablesSectionAndSectionProject1719834123740';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."section_layout_enum" AS ENUM('SectionProjectLeftWithGallery', 'SectionGalleryThree', 'SectionProjectTopWithGallery', 'SectionSwiftCards', 'SectionProjectWithScrollGallery', 'SectionSlideCards', 'SectionGallerySix')`,
    );
    await queryRunner.query(
      `CREATE TABLE "section" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "configId" uuid NOT NULL, "weight" smallint NOT NULL DEFAULT '0', "name" jsonb NOT NULL, "layout" "public"."section_layout_enum" NOT NULL DEFAULT 'SectionGalleryThree', "background" character varying NOT NULL, "visible" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_3c41d2d699384cc5e8eac54777d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "section_project" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "sectionId" uuid NOT NULL, "projectId" uuid NOT NULL, "weight" smallint NOT NULL DEFAULT '0', CONSTRAINT "PK_fa327c3f0c0ac84b8306be7a60b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "FK_d0fcae2a921ecf75378db212c47" FOREIGN KEY ("configId") REFERENCES "user_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_project" ADD CONSTRAINT "FK_a81c5025d74477c5410acd33d6c" FOREIGN KEY ("sectionId") REFERENCES "section"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_project" ADD CONSTRAINT "FK_1df4ae2ecf1ae16d6f42af98afa" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "section_project" DROP CONSTRAINT "FK_1df4ae2ecf1ae16d6f42af98afa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section_project" DROP CONSTRAINT "FK_a81c5025d74477c5410acd33d6c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "FK_d0fcae2a921ecf75378db212c47"`,
    );
    await queryRunner.query(`DROP TABLE "section_project"`);
    await queryRunner.query(`DROP TABLE "section"`);
    await queryRunner.query(`DROP TYPE "public"."section_layout_enum"`);
  }
}
