import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEnumDynamicLayoutMode1728533265400
  implements MigrationInterface
{
  name = 'AddEnumDynamicLayoutMode1728533265400';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_feature_dynamiclayoutmode_enum" AS ENUM('normal', 'advance')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_feature" ADD "dynamicLayoutMode" "public"."user_feature_dynamiclayoutmode_enum" NOT NULL DEFAULT 'normal'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_feature" DROP COLUMN "dynamicLayoutMode"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_feature_dynamiclayoutmode_enum"`,
    );
  }
}
