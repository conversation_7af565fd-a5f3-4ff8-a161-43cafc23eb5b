import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTypeFeature1726733759106 implements MigrationInterface {
    name = 'AddTypeFeature1726733759106'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."feature_type_enum" AS ENUM('ip-access-tracker-with-telegram-alerts')`);
        await queryRunner.query(`ALTER TABLE "feature" ADD "type" "public"."feature_type_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "feature" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."feature_type_enum"`);
    }

}
