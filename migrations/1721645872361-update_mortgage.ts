import { MigrationInterface, QueryRunner } from "typeorm";

export class  UpdateMortgage1721645872361 implements MigrationInterface {
    name = 'UpdateMortgage1721645872361'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "content"`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "benchMarkRate" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "firstYearRate" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "SecondYearRate" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "soraType" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "applyDate" TIMESTAMP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "applyDate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "soraType"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "SecondYearRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "firstYearRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "benchMarkRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "content" jsonb NOT NULL`);
    }

}
