import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUser1719906998449 implements MigrationInterface {
  name = 'UpdateUser1719906998449';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "resetPasswordToken" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "resetPasswordToken"`);
  }

}
