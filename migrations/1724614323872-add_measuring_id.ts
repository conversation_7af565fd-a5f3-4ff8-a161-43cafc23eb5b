import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMeasuringId1724614323872 implements MigrationInterface {
    name = 'AddMeasuringId1724614323872'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "measuringId" character varying`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD "measuringId" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "measuringId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "measuringId"`);
    }

}
