import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReasonXeroPayment1749094465650 implements MigrationInterface {
  name = 'AddReasonXeroPayment1749094465650';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "reason" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD "reason" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "xero_payment" DROP COLUMN "reason"`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "reason"`,
    );
  }
}
