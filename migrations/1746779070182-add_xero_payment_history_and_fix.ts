import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddXeroPaymentHistoryAndFix1746779070182
  implements MigrationInterface
{
  name = 'AddXeroPaymentHistoryAndFix1746779070182';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_history_status_enum" AS ENUM('PENDING', 'SUCCEED', 'FAILED')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_history_type_enum" AS ENUM('PACKAGE')`,
    );
    await queryRunner.query(
      `CREATE TABLE "xero_payment_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "description" character varying, "xeroPaymentId" uuid NOT NULL, "userId" uuid NOT NULL, "packageId" uuid NOT NULL, "paymentUrl" character varying, "xeroInvoiceId" character varying, "status" "public"."xero_payment_history_status_enum" NOT NULL DEFAULT 'PENDING', "voucherId" uuid, "type" "public"."xero_payment_history_type_enum" NOT NULL, CONSTRAINT "PK_bcdfc63f7463774242ef78be9c8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."promotion_status_enum" RENAME TO "promotion_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_status_enum" AS ENUM('ACTIVE', 'DEACTIVE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" TYPE "public"."promotion_status_enum" USING "status"::"text"::"public"."promotion_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`,
    );
    await queryRunner.query(`DROP TYPE "public"."promotion_status_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."package_status_enum" RENAME TO "package_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."package_status_enum" AS ENUM('ACTIVE', 'DEACTIVE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "package" ALTER COLUMN "status" TYPE "public"."package_status_enum" USING "status"::"text"::"public"."package_status_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."package_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_2b97331643640192b012859dc3b" FOREIGN KEY ("xeroPaymentId") REFERENCES "xero_payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_34839411b6fe3ee9ba6805e5fe0" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_d1db6e18cd5080606894897bec0" FOREIGN KEY ("packageId") REFERENCES "package"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD CONSTRAINT "FK_5e936b946b28f8e701f5c2c1bff" FOREIGN KEY ("voucherId") REFERENCES "voucher"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_5e936b946b28f8e701f5c2c1bff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_d1db6e18cd5080606894897bec0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_34839411b6fe3ee9ba6805e5fe0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP CONSTRAINT "FK_2b97331643640192b012859dc3b"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."package_status_enum_old" AS ENUM('active', 'deactive')`,
    );
    await queryRunner.query(
      `ALTER TABLE "package" ALTER COLUMN "status" TYPE "public"."package_status_enum_old" USING "status"::"text"::"public"."package_status_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."package_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."package_status_enum_old" RENAME TO "package_status_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."promotion_status_enum_old" AS ENUM('ACTIVE', 'DE_ACTIVE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" TYPE "public"."promotion_status_enum_old" USING "status"::"text"::"public"."promotion_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "promotion" ALTER COLUMN "status" SET DEFAULT 'ACTIVE'`,
    );
    await queryRunner.query(`DROP TYPE "public"."promotion_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."promotion_status_enum_old" RENAME TO "promotion_status_enum"`,
    );
    await queryRunner.query(`DROP TABLE "xero_payment_history"`);
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_history_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_history_status_enum"`,
    );
  }
}
