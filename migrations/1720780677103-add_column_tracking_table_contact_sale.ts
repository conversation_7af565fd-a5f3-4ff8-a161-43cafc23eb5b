import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnTrackingTableContactSale1720780677103 implements MigrationInterface {
    name = 'AddColumnTrackingTableContactSale1720780677103'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" ADD "tracking" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contact_sale_submission" DROP COLUMN "tracking"`);
    }

}
