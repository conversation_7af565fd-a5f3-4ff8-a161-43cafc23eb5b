import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSiteContentAdminTable1727072032929 implements MigrationInterface {
    name = 'AddSiteContentAdminTable1727072032929'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "site_content_admin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "contentType" character varying NOT NULL, "title" character varying NOT NULL, "content" text NOT NULL, CONSTRAINT "PK_a72a6625b7439fa20da495eabc5" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "site_content_admin"`);
    }

}
