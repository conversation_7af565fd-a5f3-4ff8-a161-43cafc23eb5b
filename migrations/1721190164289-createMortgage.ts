import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMortgage1721190164289 implements MigrationInterface {
  name = 'CreateMortgage1721190164289';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."mortgage_type_enum" AS ENUM('HDB', 'Condo', 'BUC')`);
    await queryRunner.query(`CREATE TABLE "mortgage" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "type" "public"."mortgage_type_enum" NOT NULL DEFAULT 'HDB', "bankName" character varying NOT NULL, "bankLogoId" uuid, "content" jsonb NOT NULL, CONSTRAINT "REL_a036a1562f9767562c13461cf9" UNIQUE ("bankLogoId"), CONSTRAINT "PK_083a27028d698c1cc2f611f7944" PRIMARY KEY ("id"))`);
    await queryRunner.query(`ALTER TYPE "public"."asset_relation_enum" RENAME TO "asset_relation_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."asset_relation_enum" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner', 'virtual_tour', 'mortgage_bank')`);
    await queryRunner.query(`ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum" USING "relation"::"text"::"public"."asset_relation_enum"`);
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum_old"`);
    await queryRunner.query(`ALTER TABLE "mortgage" ADD CONSTRAINT "FK_a036a1562f9767562c13461cf9f" FOREIGN KEY ("bankLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "mortgage" DROP CONSTRAINT "FK_a036a1562f9767562c13461cf9f"`);
    await queryRunner.query(`CREATE TYPE "public"."asset_relation_enum_old" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config', 'promotion_banner', 'virtual_tour')`);
    await queryRunner.query(`ALTER TABLE "asset" ALTER COLUMN "relation" TYPE "public"."asset_relation_enum_old" USING "relation"::"text"::"public"."asset_relation_enum_old"`);
    await queryRunner.query(`DROP TYPE "public"."asset_relation_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."asset_relation_enum_old" RENAME TO "asset_relation_enum"`);
    await queryRunner.query(`DROP TABLE "mortgage"`);
    await queryRunner.query(`DROP TYPE "public"."mortgage_type_enum"`);
  }
}
