import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProjectAmenity1718700770490 implements MigrationInterface {
  name = 'UpdateProjectAmenity1718700770490';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP CONSTRAINT "FK_5711163d9dc030295ed039201df"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "amenityId"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "name" jsonb NOT NULL`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "photoId" character varying`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "type" jsonb NOT NULL`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "coordinates" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "coordinates"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "photoId"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "amenityId" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD CONSTRAINT "FK_5711163d9dc030295ed039201df" FOREIGN KEY ("amenityId") REFERENCES "amenity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

}
