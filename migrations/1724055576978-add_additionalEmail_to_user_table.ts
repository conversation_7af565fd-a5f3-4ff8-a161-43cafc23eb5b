import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAdditionalEmailToUserTable1724055576978 implements MigrationInterface {
    name = 'AddAdditionalEmailToUserTable1724055576978'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "additionalEmail" character varying array`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "additionalEmail"`);
    }

}
