import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableLocation1717138473952 implements MigrationInterface {
  name = 'AddTableLocation1717138473952';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "location" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "slug" character varying NOT NULL, CONSTRAINT "UQ_ff73a7032e673d18bacf8d06c9a" UNIQUE ("slug"), CONSTRAINT "PK_876d7bdba03c72251ec4c2dc827" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "project" ADD "locationId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD CONSTRAINT "UQ_75c71f4eab109b16c73d3b512cf" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD CONSTRAINT "UQ_ddd184792a65499a05439c104eb" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "amenity" ADD CONSTRAINT "UQ_8986f548e443edb88ac9f2ef714" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_plan" ADD CONSTRAINT "UQ_04b32da2028b6b7a112abef0da2" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_6fce32ddd71197807027be6ad38" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "UQ_cb73208f151aa71cdd78f662d70" UNIQUE ("slug")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_d26eab468db424d2070581373cb" FOREIGN KEY ("locationId") REFERENCES "location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_d26eab468db424d2070581373cb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "UQ_cb73208f151aa71cdd78f662d70"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_6fce32ddd71197807027be6ad38"`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_plan" DROP CONSTRAINT "UQ_04b32da2028b6b7a112abef0da2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "amenity" DROP CONSTRAINT "UQ_8986f548e443edb88ac9f2ef714"`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" DROP CONSTRAINT "UQ_ddd184792a65499a05439c104eb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type" DROP CONSTRAINT "UQ_75c71f4eab109b16c73d3b512cf"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "locationId"`);
    await queryRunner.query(`ALTER TABLE "project" ADD "location" jsonb`);
    await queryRunner.query(`DROP TABLE "location"`);
  }
}
