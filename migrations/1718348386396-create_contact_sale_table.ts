import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateContactSaleTable1718348386396 implements MigrationInterface {
  name = 'CreateContactSaleTable1718348386396';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "interestedIn" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "phone" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "name" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "email" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "appointment" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "unitTypes" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "projectId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD "userId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD CONSTRAINT "FK_e4cdb794da6688e6b90f73fc150" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" ADD CONSTRAINT "FK_9cd6f2f48c1d837f35d72d1b5d1" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP CONSTRAINT "FK_9cd6f2f48c1d837f35d72d1b5d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP CONSTRAINT "FK_e4cdb794da6688e6b90f73fc150"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "userId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "projectId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "unitTypes"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "appointment"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "email"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "name"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "phone"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contact_sale_submission" DROP COLUMN "interestedIn"`,
    );
  }
}
