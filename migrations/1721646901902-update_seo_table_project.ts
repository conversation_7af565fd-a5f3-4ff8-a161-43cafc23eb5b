import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSeoTableProject1721646901902 implements MigrationInterface {
  name = 'UpdateSeoTableProject1721646901902';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "seoTitle" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "project" ADD "keywords" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "keywords"`);
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "seoTitle"`);
  }
}
