import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMobileMastheadImage1722309407498 implements MigrationInterface {
  name = 'AddMobileMastheadImage1722309407498';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "mobileMastheadImageId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_c3a70a0f204de0feac82e0184ae" UNIQUE ("mobileMastheadImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_c3a70a0f204de0feac82e0184ae" FOREIGN KEY ("mobileMastheadImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_c3a70a0f204de0feac82e0184ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_c3a70a0f204de0feac82e0184ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "mobileMastheadImageId"`,
    );
  }
}
