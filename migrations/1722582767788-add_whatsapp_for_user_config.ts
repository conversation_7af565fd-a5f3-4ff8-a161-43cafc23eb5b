import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddWhatsappForUserConfig1722582767788 implements MigrationInterface {
    name = 'AddWhatsappForUserConfig1722582767788'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" ADD "whatsapp" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "whatsapp"`);
    }

}
