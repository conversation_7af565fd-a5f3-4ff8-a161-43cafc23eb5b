import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsHiddenInUserConfig1748920334975
  implements MigrationInterface
{
  name = 'AddIsHiddenInUserConfig1748920334975';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "isHiddenWhatsapp" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "isHiddenShowFlatNumber" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "isHiddenRegister" boolean DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "isHiddenRegister"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "isHiddenShowFlatNumber"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "isHiddenWhatsapp"`,
    );
  }
}
