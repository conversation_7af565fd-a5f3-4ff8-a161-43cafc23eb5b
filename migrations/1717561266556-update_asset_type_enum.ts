import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAssetTypeEnum1717561266556 implements MigrationInterface {
  name = 'UpdateAssetTypeEnum1717561266556';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."asset_type_enum" RENAME TO "asset_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_type_enum" AS ENUM('image', 'video', 'binary')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" TYPE "public"."asset_type_enum" USING "type"::"text"::"public"."asset_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" SET DEFAULT 'binary'`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."asset_type_enum_old" AS ENUM('binary', 'image')`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" TYPE "public"."asset_type_enum_old" USING "type"::"text"::"public"."asset_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ALTER COLUMN "type" SET DEFAULT 'binary'`,
    );
    await queryRunner.query(`DROP TYPE "public"."asset_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."asset_type_enum_old" RENAME TO "asset_type_enum"`,
    );
  }
}
