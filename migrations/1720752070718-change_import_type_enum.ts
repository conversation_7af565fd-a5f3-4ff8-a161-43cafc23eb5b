import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeImportTypeEnum1720752070718 implements MigrationInterface {
  name = 'ChangeImportTypeEnum1720752070718';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."import_data_type_enum" RENAME TO "import_data_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."import_data_type_enum" AS ENUM('project', 'category', 'unit-type', 'floor-plan', 'unit-transaction')`,
    );
    await queryRunner.query(
      `ALTER TABLE "import_data" ALTER COLUMN "type" TYPE "public"."import_data_type_enum" USING "type"::"text"::"public"."import_data_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."import_data_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."import_data_type_enum_old" AS ENUM('project', 'category', 'project-type', 'floor-plan', 'unit-transaction')`,
    );
    await queryRunner.query(
      `ALTER TABLE "import_data" ALTER COLUMN "type" TYPE "public"."import_data_type_enum_old" USING "type"::"text"::"public"."import_data_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."import_data_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."import_data_type_enum_old" RENAME TO "import_data_type_enum"`,
    );
  }
}
