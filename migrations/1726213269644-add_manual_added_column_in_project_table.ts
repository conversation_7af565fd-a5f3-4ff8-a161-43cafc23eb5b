import { MigrationInterface, QueryRunner } from "typeorm";

export class AddManualAddedColumnInProjectTable1726213269644 implements MigrationInterface {
    name = 'AddManualAddedColumnInProjectTable1726213269644'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "manualAdded" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "manualAdded"`);
    }

}
