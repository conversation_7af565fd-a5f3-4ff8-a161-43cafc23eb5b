import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePackageEntity1748577146564 implements MigrationInterface {
  name = 'UpdatePackageEntity1748577146564';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "package" ADD "isHidden" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "package" ADD "code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "package" ADD CONSTRAINT "UQ_2c101f681a8229b198c816a3190" UNIQUE ("code")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "package" DROP CONSTRAINT "UQ_2c101f681a8229b198c816a3190"`,
    );
    await queryRunner.query(`ALTER TABLE "package" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "package" DROP COLUMN "isHidden"`);
  }
}
