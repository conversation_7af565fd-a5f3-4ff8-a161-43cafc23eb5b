import { MigrationInterface, QueryRunner } from 'typeorm';

export class S1741690195415 implements MigrationInterface {
  name = 'S1741690195415';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "statusAccount" character varying NOT NULL DEFAULT 'inactive'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "statusAccount"`);
  }
}
