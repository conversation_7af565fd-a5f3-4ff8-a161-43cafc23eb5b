import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldNameLandingPage1729482597161
  implements MigrationInterface
{
  name = 'AddFieldNameLandingPage1729482597161';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "name" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "name"`,
    );
  }
}
