import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSlugColumnToTables1717056705331 implements MigrationInterface {
  name = 'AddSlugColumnToTables1717056705331';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_type" ADD "slug" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "floor_plan" ADD "slug" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "amenity" ADD "slug" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "site_plan" ADD "slug" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD "slug" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD "slug" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "category" DROP COLUMN "slug"`);
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "slug"`);
    await queryRunner.query(`ALTER TABLE "site_plan" DROP COLUMN "slug"`);
    await queryRunner.query(`ALTER TABLE "amenity" DROP COLUMN "slug"`);
    await queryRunner.query(`ALTER TABLE "floor_plan" DROP COLUMN "slug"`);
    await queryRunner.query(`ALTER TABLE "unit_type" DROP COLUMN "slug"`);
  }
}
