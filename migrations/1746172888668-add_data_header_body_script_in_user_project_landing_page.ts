import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDataHeaderBodyScriptInUserProjectLandingPage1746172888668
  implements MigrationInterface
{
  name = 'AddDataHeaderBodyScriptInUserProjectLandingPage1746172888668';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "dataScript" json`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "dataScript"`,
    );
  }
}
