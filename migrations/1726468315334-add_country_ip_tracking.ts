import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCountryIpTracking1726468315334 implements MigrationInterface {
    name = 'AddCountryIpTracking1726468315334'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ip_tracking" ADD "country" character varying`);
        await queryRunner.query(`ALTER TABLE "ip_tracking" ADD "countryCode" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "ip_tracking" DROP COLUMN "countryCode"`);
        await queryRunner.query(`ALTER TABLE "ip_tracking" DROP COLUMN "country"`);
    }

}
