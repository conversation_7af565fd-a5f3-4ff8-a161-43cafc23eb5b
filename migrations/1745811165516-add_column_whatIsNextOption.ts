import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnWhatIsNextOption1745811165516
  implements MigrationInterface
{
  name = 'AddColumnWhatIsNextOption1745811165516';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" ADD "whatIsNextStatusOptions" jsonb DEFAULT '["pre-launch","preview","post-launch"]'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "whatIsNextStatusOptions"`,
    );
  }
}
