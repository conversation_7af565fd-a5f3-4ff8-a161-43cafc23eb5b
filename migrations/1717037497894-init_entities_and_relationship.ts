import { MigrationInterface, QueryRunner } from "typeorm";

export class InitEntitiesAndRelationship1717037497894 implements MigrationInterface {
    name = 'InitEntitiesAndRelationship1717037497894'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."asset_type_enum" AS ENUM('image', 'binary')`);
        await queryRunner.query(`CREATE TYPE "public"."asset_relation_enum" AS ENUM('user', 'developer', 'amenity', 'project', 'site_plan', 'floor_plan', 'user_config')`);
        await queryRunner.query(`CREATE TABLE "asset" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "type" "public"."asset_type_enum" NOT NULL DEFAULT 'binary', "relation" "public"."asset_relation_enum", "relationId" uuid, "urls" jsonb, CONSTRAINT "PK_1209d107fe21482beaea51b745e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "shortname" jsonb NOT NULL, CONSTRAINT "PK_9c4e4a89e3674fc9f382d733f03" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "unit_type" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "title" jsonb NOT NULL, "bedRoomCount" smallint NOT NULL DEFAULT '0', "hasStudyRoom" boolean NOT NULL DEFAULT false, "isPremium" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_e883871bf594e4890e06e7ee27e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "floor_plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "area" double precision NOT NULL, "projectId" uuid NOT NULL, "photoId" uuid NOT NULL, "unitTypeId" uuid NOT NULL, CONSTRAINT "REL_34e16f04bb93ba543526c98855" UNIQUE ("photoId"), CONSTRAINT "PK_d16cdd3e52f897258d1587ef65c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."amenity_type_enum" AS ENUM('recreation', 'shopping', 'supermarket', 'medical', 'education', 'retail', 'connectivity')`);
        await queryRunner.query(`CREATE TABLE "amenity" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "photoId" character varying, "type" "public"."amenity_type_enum", "coordinates" character varying, CONSTRAINT "PK_f981de7b1a822823e5f31da10dc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "project_amenity" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "projectId" uuid NOT NULL, "amenityId" uuid NOT NULL, "distance" double precision NOT NULL, "duration" integer NOT NULL, CONSTRAINT "PK_ce4d6537de1cbe8db3f74b6b8f6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "site_plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "title" jsonb NOT NULL, "projectId" uuid NOT NULL, "photoId" uuid, CONSTRAINT "PK_5e8cdd2f49eba06c113391a71a0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."project_marketsegment_enum" AS ENUM('RCR', 'CCR', 'OCR')`);
        await queryRunner.query(`CREATE TABLE "project" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "description" jsonb NOT NULL, "photoId" uuid, "marketSegment" "public"."project_marketsegment_enum" NOT NULL, "developerId" uuid, "categoryId" uuid, "address" jsonb, "location" jsonb, "coordinates" character varying, "area" double precision NOT NULL, "expectedTop" character varying, "tenure" integer NOT NULL, "tenureEffectFrom" TIMESTAMP, CONSTRAINT "REL_34910848a43ff58c8e31d8335c" UNIQUE ("photoId"), CONSTRAINT "PK_4d68b1358bb5b766d3e78f32f57" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "unit_transaction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "unitId" uuid NOT NULL, "soldPrice" double precision NOT NULL, "currency" character varying NOT NULL, CONSTRAINT "REL_bf8512a1eeeb24b8885c2e09b6" UNIQUE ("unitId"), CONSTRAINT "PK_48b99bad109558a4dced7bc18ce" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."unit_status_enum" AS ENUM('available', 'sold')`);
        await queryRunner.query(`CREATE TABLE "unit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "floor" integer, "block" character varying, "price" integer NOT NULL, "currency" character varying NOT NULL, "remarks" character varying, "status" "public"."unit_status_enum" NOT NULL DEFAULT 'available', "projectId" uuid NOT NULL, "floorPlanId" uuid NOT NULL, "transactionId" uuid NOT NULL, CONSTRAINT "REL_57c9c6b8c1a4d4a1f5b0623490" UNIQUE ("transactionId"), CONSTRAINT "PK_4252c4be609041e559f0c80f58a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "category_config" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "categoryId" uuid NOT NULL, "configId" uuid NOT NULL, "cardStyle" character varying NOT NULL DEFAULT 'default', CONSTRAINT "PK_235ca385124704d7088ebb091ff" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_config" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "domain" character varying, "siteTitle" character varying, "siteDescription" character varying, "siteLogoId" uuid, "phoneNumber" character varying, "facebook" character varying, "instagram" character varying, "tiktok" character varying, CONSTRAINT "REL_50aa50cd542e360ea75bf4eaa7" UNIQUE ("userId"), CONSTRAINT "PK_5cb73472aaca5c0cf69e74d870e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`CREATE TYPE "public"."user_role_enum" AS ENUM('admin', 'user')`);
        await queryRunner.query(`CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "email" character varying NOT NULL, "password" character varying NOT NULL, "status" "public"."user_status_enum" NOT NULL DEFAULT 'active', "role" "public"."user_role_enum" NOT NULL DEFAULT 'user', "firstName" character varying(30) NOT NULL, "lastName" character varying(30) NOT NULL, "phone" character varying(15), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "featured_project" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "projectId" uuid NOT NULL, "userId" uuid NOT NULL, "weight" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_73b62e87165767b6add774f950f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "developer" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" jsonb NOT NULL, "description" jsonb, "logoId" uuid, CONSTRAINT "REL_7501adbb170751acf95aab42c9" UNIQUE ("logoId"), CONSTRAINT "PK_71b846918f80786eed6bfb68b77" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "contact_sale_submission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, CONSTRAINT "PK_3715b9223c0b9043c75edad50b1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "session" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, CONSTRAINT "PK_f55da76ac1c3ac420f444d2ff11" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "floor_plan" ADD CONSTRAINT "FK_9d83b71c1b5dfa65bfaa5055a48" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "floor_plan" ADD CONSTRAINT "FK_34e16f04bb93ba543526c98855f" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "floor_plan" ADD CONSTRAINT "FK_f11ef846e795914e6bb0fcfd6f8" FOREIGN KEY ("unitTypeId") REFERENCES "unit_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project_amenity" ADD CONSTRAINT "FK_20b6390a1699876e2e6dc6159dc" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project_amenity" ADD CONSTRAINT "FK_5711163d9dc030295ed039201df" FOREIGN KEY ("amenityId") REFERENCES "amenity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_plan" ADD CONSTRAINT "FK_39b1b81be9fca91734e00cda311" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "site_plan" ADD CONSTRAINT "FK_f9891a4f1791b0ed65a242fb998" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project" ADD CONSTRAINT "FK_34910848a43ff58c8e31d8335c1" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project" ADD CONSTRAINT "FK_3caef906211aad45559039f11f9" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "unit_transaction" ADD CONSTRAINT "FK_bf8512a1eeeb24b8885c2e09b67" FOREIGN KEY ("unitId") REFERENCES "unit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "FK_081a1021523202d85962a6ef10c" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "FK_f0e99f20131e3f58d8f2a2333db" FOREIGN KEY ("floorPlanId") REFERENCES "floor_plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902" FOREIGN KEY ("transactionId") REFERENCES "unit_transaction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "category_config" ADD CONSTRAINT "FK_de4926b83a1450620303ef7a8a3" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "category_config" ADD CONSTRAINT "FK_8c92d8eaef6806b0d717f043ddd" FOREIGN KEY ("configId") REFERENCES "user_config"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_config" ADD CONSTRAINT "FK_50aa50cd542e360ea75bf4eaa74" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "featured_project" ADD CONSTRAINT "FK_93e03edbd2e1b159e84425a8542" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "featured_project" ADD CONSTRAINT "FK_0d70ef069387f325efe87e15877" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "developer" ADD CONSTRAINT "FK_7501adbb170751acf95aab42c9b" FOREIGN KEY ("logoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "developer" DROP CONSTRAINT "FK_7501adbb170751acf95aab42c9b"`);
        await queryRunner.query(`ALTER TABLE "featured_project" DROP CONSTRAINT "FK_0d70ef069387f325efe87e15877"`);
        await queryRunner.query(`ALTER TABLE "featured_project" DROP CONSTRAINT "FK_93e03edbd2e1b159e84425a8542"`);
        await queryRunner.query(`ALTER TABLE "user_config" DROP CONSTRAINT "FK_50aa50cd542e360ea75bf4eaa74"`);
        await queryRunner.query(`ALTER TABLE "category_config" DROP CONSTRAINT "FK_8c92d8eaef6806b0d717f043ddd"`);
        await queryRunner.query(`ALTER TABLE "category_config" DROP CONSTRAINT "FK_de4926b83a1450620303ef7a8a3"`);
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902"`);
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "FK_f0e99f20131e3f58d8f2a2333db"`);
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "FK_081a1021523202d85962a6ef10c"`);
        await queryRunner.query(`ALTER TABLE "unit_transaction" DROP CONSTRAINT "FK_bf8512a1eeeb24b8885c2e09b67"`);
        await queryRunner.query(`ALTER TABLE "project" DROP CONSTRAINT "FK_3caef906211aad45559039f11f9"`);
        await queryRunner.query(`ALTER TABLE "project" DROP CONSTRAINT "FK_34910848a43ff58c8e31d8335c1"`);
        await queryRunner.query(`ALTER TABLE "site_plan" DROP CONSTRAINT "FK_f9891a4f1791b0ed65a242fb998"`);
        await queryRunner.query(`ALTER TABLE "site_plan" DROP CONSTRAINT "FK_39b1b81be9fca91734e00cda311"`);
        await queryRunner.query(`ALTER TABLE "project_amenity" DROP CONSTRAINT "FK_5711163d9dc030295ed039201df"`);
        await queryRunner.query(`ALTER TABLE "project_amenity" DROP CONSTRAINT "FK_20b6390a1699876e2e6dc6159dc"`);
        await queryRunner.query(`ALTER TABLE "floor_plan" DROP CONSTRAINT "FK_f11ef846e795914e6bb0fcfd6f8"`);
        await queryRunner.query(`ALTER TABLE "floor_plan" DROP CONSTRAINT "FK_34e16f04bb93ba543526c98855f"`);
        await queryRunner.query(`ALTER TABLE "floor_plan" DROP CONSTRAINT "FK_9d83b71c1b5dfa65bfaa5055a48"`);
        await queryRunner.query(`DROP TABLE "session"`);
        await queryRunner.query(`DROP TABLE "contact_sale_submission"`);
        await queryRunner.query(`DROP TABLE "developer"`);
        await queryRunner.query(`DROP TABLE "featured_project"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
        await queryRunner.query(`DROP TABLE "user_config"`);
        await queryRunner.query(`DROP TABLE "category_config"`);
        await queryRunner.query(`DROP TABLE "unit"`);
        await queryRunner.query(`DROP TYPE "public"."unit_status_enum"`);
        await queryRunner.query(`DROP TABLE "unit_transaction"`);
        await queryRunner.query(`DROP TABLE "project"`);
        await queryRunner.query(`DROP TYPE "public"."project_marketsegment_enum"`);
        await queryRunner.query(`DROP TABLE "site_plan"`);
        await queryRunner.query(`DROP TABLE "project_amenity"`);
        await queryRunner.query(`DROP TABLE "amenity"`);
        await queryRunner.query(`DROP TYPE "public"."amenity_type_enum"`);
        await queryRunner.query(`DROP TABLE "floor_plan"`);
        await queryRunner.query(`DROP TABLE "unit_type"`);
        await queryRunner.query(`DROP TABLE "category"`);
        await queryRunner.query(`DROP TABLE "asset"`);
        await queryRunner.query(`DROP TYPE "public"."asset_relation_enum"`);
        await queryRunner.query(`DROP TYPE "public"."asset_type_enum"`);
    }

}
