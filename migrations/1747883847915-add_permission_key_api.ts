import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPermissionKeyApi1747883847915 implements MigrationInterface {
  name = 'AddPermissionKeyApi1747883847915';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."api_key_permissions_enum" RENAME TO "api_key_permissions_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."api_key_permissions_enum" AS ENUM('GET_ALL_PROPERTIES', 'GET_ALL_CATEGORIES', 'GET_ALL_LOCATIONS', 'GET_ALL_UNIT_TYPES', 'GET_ALL_TOP_LISTING', 'GET_ALL_PRICING', 'POST_LEAD', 'GET_ALL_DEVELOPERS', 'GET_PROPERTY_BY_SLUG', 'GET_DEVELOPER_BY_ID')`,
    );
    await queryRunner.query(
      `ALTER TABLE "api_key" ALTER COLUMN "permissions" TYPE "public"."api_key_permissions_enum"[] USING "permissions"::"text"::"public"."api_key_permissions_enum"[]`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."api_key_permissions_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users_timeslots" DROP CONSTRAINT "FK_29f982e149e184ab722dcc7d8ce"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users_timeslots" DROP CONSTRAINT "FK_4141a42abb4da8ff95d8f966ba5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_f0e99f20131e3f58d8f2a2333db"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_081a1021523202d85962a6ef10c"`,
    );

    await queryRunner.query(
      `CREATE TYPE "public"."api_key_permissions_enum_old" AS ENUM('GET_ALL_PROPERTIES', 'GET_ALL_CATEGORIES', 'GET_ALL_LOCATIONS', 'GET_ALL_UNIT_TYPES', 'GET_ALL_TOP_LISTING', 'GET_ALL_PRICING', 'POST_LEAD', 'GET_ALL_DEVELOPERS')`,
    );
    await queryRunner.query(
      `ALTER TABLE "api_key" ALTER COLUMN "permissions" TYPE "public"."api_key_permissions_enum_old"[] USING "permissions"::"text"::"public"."api_key_permissions_enum_old"[]`,
    );
    await queryRunner.query(`DROP TYPE "public"."api_key_permissions_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."api_key_permissions_enum_old" RENAME TO "api_key_permissions_enum"`,
    );
  }
}
