import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateImageLogoFields1740985132518 implements MigrationInterface {
  name = 'UpdateImageLogoFields1740985132518';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns
    await queryRunner.query(`ALTER TABLE "project" ADD "mobileLogoId" uuid`);
    await queryRunner.query(`ALTER TABLE "project" ADD "desktopLogoId" uuid`);
    await queryRunner.query(`ALTER TABLE "asset" ADD "projectId" uuid`);
    await queryRunner.query(`ALTER TABLE "user" ADD "agencyLogoId" uuid`);

    // Add unique constraints
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_f1d847720a81152fb3c7de7acdb" UNIQUE ("mobileLogoId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_55042bda4d99641e8b43fd2d5c5" UNIQUE ("desktopLogoId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_921834e993da2adf815e90a6d46" UNIQUE ("agencyLogoId")`,
    );

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_f1d847720a81152fb3c7de7acdb" FOREIGN KEY ("mobileLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_55042bda4d99641e8b43fd2d5c5" FOREIGN KEY ("desktopLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ADD CONSTRAINT "FK_e8d6f1f931bbeea918b07aa6ace" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_921834e993da2adf815e90a6d46" FOREIGN KEY ("agencyLogoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_921834e993da2adf815e90a6d46"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" DROP CONSTRAINT "FK_e8d6f1f931bbeea918b07aa6ace"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_55042bda4d99641e8b43fd2d5c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_f1d847720a81152fb3c7de7acdb"`,
    );

    // Remove unique constraints
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "UQ_921834e993da2adf815e90a6d46"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_55042bda4d99641e8b43fd2d5c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_f1d847720a81152fb3c7de7acdb"`,
    );

    // Remove columns
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "agencyLogoId"`);
    await queryRunner.query(`ALTER TABLE "asset" DROP COLUMN "projectId"`);
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "desktopLogoId"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "mobileLogoId"`);
  }
}
