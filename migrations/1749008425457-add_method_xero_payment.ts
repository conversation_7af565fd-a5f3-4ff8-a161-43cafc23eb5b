import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMethodXeroPayment1749008425457 implements MigrationInterface {
  name = 'AddMethodXeroPayment1749008425457';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_history_method_enum" AS ENUM('PAY_NOW', 'CREDIT_CARD')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" ADD "method" "public"."xero_payment_history_method_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."xero_payment_method_enum" AS ENUM('PAY_NOW', 'CREDIT_CARD')`,
    );
    await queryRunner.query(
      `ALTER TABLE "xero_payment" ADD "method" "public"."xero_payment_method_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "xero_payment" DROP COLUMN "method"`);
    await queryRunner.query(`DROP TYPE "public"."xero_payment_method_enum"`);
    await queryRunner.query(
      `ALTER TABLE "xero_payment_history" DROP COLUMN "method"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."xero_payment_history_method_enum"`,
    );
  }
}
