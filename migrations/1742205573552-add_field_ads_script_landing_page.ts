import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldAdsScriptLandingPage1742205573552
  implements MigrationInterface
{
  name = 'AddFieldAdsScriptLandingPage1742205573552';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "headerScript" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "bodyScript" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "enquiryScript" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "enquiryScript"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "bodyScript"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "headerScript"`,
    );
  }
}
