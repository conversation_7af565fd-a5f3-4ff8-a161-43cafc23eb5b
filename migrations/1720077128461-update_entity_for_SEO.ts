import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEntityForSEO1720077128461 implements MigrationInterface {
  name = 'UpdateEntityForSEO1720077128461';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "asset" ADD "forSEO" boolean`);
    await queryRunner.query(`ALTER TABLE "project" ADD "seoImageId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "UQ_740c2fda8a80bdce626cef2e23d" UNIQUE ("seoImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD "seoDescription" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "twitterCreator" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "twitterSite" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "user_config" ADD "seoImageId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "UQ_f3a1dfb433665f8ea93ea445d16" UNIQUE ("seoImageId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD "seoDescription" character varying(200)`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" ADD CONSTRAINT "FK_740c2fda8a80bdce626cef2e23d" FOREIGN KEY ("seoImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" ADD CONSTRAINT "FK_f3a1dfb433665f8ea93ea445d16" FOREIGN KEY ("seoImageId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "FK_f3a1dfb433665f8ea93ea445d16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "FK_740c2fda8a80bdce626cef2e23d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "seoDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP CONSTRAINT "UQ_f3a1dfb433665f8ea93ea445d16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "seoImageId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "twitterSite"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "twitterCreator"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP COLUMN "seoDescription"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project" DROP CONSTRAINT "UQ_740c2fda8a80bdce626cef2e23d"`,
    );
    await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "seoImageId"`);
    await queryRunner.query(`ALTER TABLE "asset" DROP COLUMN "forSEO"`);
  }
}
