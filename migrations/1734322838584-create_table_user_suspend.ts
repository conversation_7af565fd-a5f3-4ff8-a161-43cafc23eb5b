import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableUserSuspend1734322838584 implements MigrationInterface {
  name = 'CreateTableUserSuspend1734322838584';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_suspend_status_enum" AS ENUM('pending', 'active')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_suspend" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "reason" text, "date" TIMESTAMP WITH TIME ZONE, "status" "public"."user_suspend_status_enum", CONSTRAINT "PK_fb9b2a82d10e4966072072b8399" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fe9509599d7d18c355dca5d0b0" ON "user_suspend" ("userId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_60c58e9bd8c3ba11854f73a78e" ON "user_suspend" ("date") `,
    );
    await queryRunner.query(
      `ALTER TABLE "user_suspend" ADD CONSTRAINT "FK_fe9509599d7d18c355dca5d0b07" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_suspend" DROP CONSTRAINT "FK_fe9509599d7d18c355dca5d0b07"`,
    );
    await queryRunner.query(`DROP TABLE "user_suspend"`);
    await queryRunner.query(`DROP TYPE "public"."user_suspend_status_enum"`);
  }
}
