import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnTopButtonToTableUserConfig1719473452155
  implements MigrationInterface
{
  name = 'AddColumnTopButtonToTableUserConfig1719473452155';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_config" ADD "topButton" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_config" DROP COLUMN "topButton"`,
    );
  }
}
