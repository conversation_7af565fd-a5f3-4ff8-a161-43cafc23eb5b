import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnLayoutToUserLandingPage1731396229414
  implements MigrationInterface
{
  name = 'AddColumnLayoutToUserLandingPage1731396229414';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "layout" jsonb`,
    );

    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD "type" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "type"`,
    );

    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP COLUMN "layout"`,
    );
  }
}
