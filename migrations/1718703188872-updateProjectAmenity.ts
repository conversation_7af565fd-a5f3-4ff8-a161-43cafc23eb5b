import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProjectAmenity1718703188872 implements MigrationInterface {
  name = 'UpdateProjectAmenity1718703188872';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "photoId"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "photoId" uuid`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD CONSTRAINT "UQ_c433d3d8746d43e225e8029e597" UNIQUE ("photoId")`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD CONSTRAINT "FK_c433d3d8746d43e225e8029e597" FOREIGN KEY ("photoId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP CONSTRAINT "FK_c433d3d8746d43e225e8029e597"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP CONSTRAINT "UQ_c433d3d8746d43e225e8029e597"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" DROP COLUMN "photoId"`);
    await queryRunner.query(`ALTER TABLE "project_amenity" ADD "photoId" character varying`);
  }

}
