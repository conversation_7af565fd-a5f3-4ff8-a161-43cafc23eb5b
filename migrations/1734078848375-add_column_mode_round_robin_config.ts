import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnModeRoundRobinConfig1734078848375
  implements MigrationInterface
{
  name = 'AddColumnModeRoundRobinConfig1734078848375';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."round_robin_config_mode_enum" AS ENUM('round_robin', 'default', 'off')`,
    );
    await queryRunner.query(
      `ALTER TABLE "round_robin_config" ADD "mode" "public"."round_robin_config_mode_enum" NOT NULL DEFAULT 'round_robin'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "round_robin_config" DROP COLUMN "mode"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."round_robin_config_mode_enum"`,
    );
  }
}
