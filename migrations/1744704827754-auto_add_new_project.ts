import { MigrationInterface, QueryRunner } from 'typeorm';

export class AutoAddNewProject1744704827754 implements MigrationInterface {
  name = 'AutoAddNewProject1744704827754';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum" RENAME TO "feature_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account', 'round-robin', 'directory', 'auto-add-new-project')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum" USING "type"::"text"::"public"."feature_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."feature_type_enum_old" AS ENUM('ip-access-tracker-with-telegram-alerts', 'dynamic-layout', 'project-landing-page', 'multiple-contact', 'team-account', 'round-robin', 'directory')`,
    );
    await queryRunner.query(
      `ALTER TABLE "feature" ALTER COLUMN "type" TYPE "public"."feature_type_enum_old" USING "type"::"text"::"public"."feature_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."feature_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."feature_type_enum_old" RENAME TO "feature_type_enum"`,
    );
  }
}
