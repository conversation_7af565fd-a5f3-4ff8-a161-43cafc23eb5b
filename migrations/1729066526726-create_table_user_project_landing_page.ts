import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableUserProjectLandingPage1729066526726
  implements MigrationInterface
{
  name = 'CreateTableUserProjectLandingPage1729066526726';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user_project_landing_page" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "projectId" uuid NOT NULL, "userId" uuid NOT NULL, "domain" text NOT NULL, CONSTRAINT "PK_871d381435e3de049146e5ca8d4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD CONSTRAINT "FK_3767c115a5d53229d8284110a9d" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" ADD CONSTRAINT "FK_e43b2cdebfbd0f2c4ef1f04a453" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP CONSTRAINT "FK_e43b2cdebfbd0f2c4ef1f04a453"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_project_landing_page" DROP CONSTRAINT "FK_3767c115a5d53229d8284110a9d"`,
    );
    await queryRunner.query(`DROP TABLE "user_project_landing_page"`);
  }
}
