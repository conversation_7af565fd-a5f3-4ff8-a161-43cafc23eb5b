import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddPromotionForProjectTable1722499221297 implements MigrationInterface {
    name = 'AddPromotionForProjectTable1722499221297'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "promotion" boolean DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "promotion"`);
    }

}
