import { MigrationInterface, QueryRunner } from 'typeorm';

export class ContactSaleSubmissionRelations1745394257350
  implements MigrationInterface
{
  name = 'ContactSaleSubmissionRelations1745394257350';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_976376cc427fdfb753a0c3cece"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a61bb2ad8ee7a8e22e5c87ca2f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" ADD "contactSaleSubmissionId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" ADD CONSTRAINT "UQ_012da0d06a6854e462e7a0bf721" UNIQUE ("contactSaleSubmissionId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" ADD "contactSaleSubmissionId" uuid`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_fc426fc54c219abb4ba2521a85" ON "project_stats" ("date", "projectId", "contactSaleSubmissionId") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_7fecef32813cd1b59b986b1dbb" ON "unit_type_stats" ("date", "unitTypeId", "contactSaleSubmissionId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" ADD CONSTRAINT "FK_012da0d06a6854e462e7a0bf721" FOREIGN KEY ("contactSaleSubmissionId") REFERENCES "contact_sale_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" ADD CONSTRAINT "FK_8a2db83ab7183b49c66b27c2fcb" FOREIGN KEY ("contactSaleSubmissionId") REFERENCES "contact_sale_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" DROP CONSTRAINT "FK_8a2db83ab7183b49c66b27c2fcb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" DROP CONSTRAINT "FK_012da0d06a6854e462e7a0bf721"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7fecef32813cd1b59b986b1dbb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_fc426fc54c219abb4ba2521a85"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unit_type_stats" DROP COLUMN "contactSaleSubmissionId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" DROP CONSTRAINT "UQ_012da0d06a6854e462e7a0bf721"`,
    );
    await queryRunner.query(
      `ALTER TABLE "project_stats" DROP COLUMN "contactSaleSubmissionId"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_a61bb2ad8ee7a8e22e5c87ca2f" ON "unit_type_stats" ("date", "unitTypeId") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_976376cc427fdfb753a0c3cece" ON "project_stats" ("date", "projectId") `,
    );
  }
}
