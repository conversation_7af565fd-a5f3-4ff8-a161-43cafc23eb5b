import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveNotNullUnitTransactionId1717469478994 implements MigrationInterface {
    name = 'RemoveNotNullUnitTransactionId1717469478994'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902"`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "transactionId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902" FOREIGN KEY ("transactionId") REFERENCES "unit_transaction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unit" DROP CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902"`);
        await queryRunner.query(`ALTER TABLE "unit" ALTER COLUMN "transactionId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "unit" ADD CONSTRAINT "FK_57c9c6b8c1a4d4a1f5b06234902" FOREIGN KEY ("transactionId") REFERENCES "unit_transaction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
