import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserConfig1721024532251 implements MigrationInterface {
  name = 'UpdateUserConfig1721024532251';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_config" ADD "promotionBgColor" character varying`);
    await queryRunner.query(`ALTER TABLE "user_config" ADD "promotionHeading" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "promotionHeading"`);
    await queryRunner.query(`ALTER TABLE "user_config" DROP COLUMN "promotionBgColor"`);
  }
}
