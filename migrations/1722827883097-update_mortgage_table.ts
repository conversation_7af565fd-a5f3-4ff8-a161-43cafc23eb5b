import { MigrationInterface, QueryRunner } from "typeorm";

export class  UpdateMortGageTable1722827883097 implements MigrationInterface {
    name = 'UpdateMortGageTable1722827883097'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "benchMarkRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "firstYearRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "secondYearRate"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "soraType"`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "firstYear" jsonb`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "secondYear" jsonb`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "threeYear" jsonb`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "thereAfter" jsonb`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "package" character varying`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "remarks" text`);
        await queryRunner.query(`ALTER TABLE "mortgage" ALTER COLUMN "applyDate" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mortgage" ALTER COLUMN "applyDate" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "package"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "thereAfter"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "threeYear"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "secondYear"`);
        await queryRunner.query(`ALTER TABLE "mortgage" DROP COLUMN "firstYear"`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "soraType" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "secondYearRate" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "firstYearRate" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "mortgage" ADD "benchMarkRate" character varying NOT NULL`);
    }

}
