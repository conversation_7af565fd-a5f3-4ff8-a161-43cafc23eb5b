import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRelationSectionLocation1723606487178
  implements MigrationInterface
{
  name = 'CreateRelationSectionLocation1723606487178';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "section" ADD "locationId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "FK_cafb90ae06c57a58c0467c810c6" FOREIGN KEY ("locationId") REFERENCES "location"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "FK_cafb90ae06c57a58c0467c810c6"`,
    );
    await queryRunner.query(`ALTER TABLE "section" DROP COLUMN "locationId"`);
  }
}
