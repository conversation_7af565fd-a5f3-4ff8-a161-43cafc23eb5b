import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLimitCreationLdpUserFeature1747721794594
  implements MigrationInterface
{
  name = 'AddLimitCreationLdpUserFeature1747721794594';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "landingpages_control" ADD "limitCreation" integer`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "landingpages_control" DROP COLUMN "limitCreation"`,
    );
  }
}
