<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Email Template</title>
    <style>
      body {
        font-family: 'Lato', sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;
      }

      .text-content {
        font-size: 16px;
        line-height: 1.5;
        color: #000000;
        margin-bottom: 24px;
      }

      .section-title {
        font-size: 14px;
        font-weight: 700;
        text-transform: uppercase;
        margin-bottom: 12px;
        color: #000000;
      }

      .divider {
        border-bottom: 1px solid rgba(0, 0, 0, 0.25);
        margin: 12px 0 20px 0;
      }

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
      }

      .info-label {
        font-size: 14px;
        line-height: 19px;
        color: rgba(0, 0, 0, 0.5);
        flex: 0 0 40%;
        max-width: 40%;
        white-space: nowrap;
        vertical-align: baseline;
        padding-right: 10px;
      }

      .info-value {
        font-size: 16px;
        line-height: 19px;
        color: #000000;
        flex: 1;
        word-wrap: break-word;
      }

      .info-value a {
        color: #007bff;
        text-decoration: none;
      }

      .info-value a:hover {
        text-decoration: underline;
      }

      @media only screen and (max-width: 600px) {
        .email-container {
          padding: 16px;
        }

        .info-row {
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .info-label {
          flex: none;
          max-width: 100%;
          margin-bottom: 4px;
        }

        .info-value {
          flex: none;
          max-width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="text-content">
        Hi there,<br />
        Application information was sent at {{submissionDate}}.
      </div>

      <div class="section-title">Customer’s personal information</div>
      <div class="divider"></div>

      <div class="info-row">
        <span class="info-label">Name</span>
        <span class="info-value">{{fullName}}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Phone</span>
        <span class="info-value">{{phone}}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Email</span>
        <span class="info-value">{{email}}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Appointment</span>
        <span class="info-value">{{appointment}}</span>
      </div>

      <div class="info-row">
        <span class="info-label">Unit type</span>
        <span class="info-value"> {{unitType}} </span>
      </div>
    </div>
  </body>
</html>
