<!doctype html>
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <title></title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
      rel="stylesheet"
    />
    <style type="text/css">
      #outlook a {
        padding: 0;
      }

      body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        font-family: 'Roboto', sans-serif;
      }

      table,
      td {
        border-collapse: collapse;
        vertical-align: top;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }

      img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        -ms-interpolation-mode: bicubic;
      }

      p {
        display: block;
        margin: 13px 0;
      }

      #redirect_button:hover {
        background: #444444 !important;
      }
    </style>
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG></o:AllowPNG>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
      <style type="text/css">
        .mj-outlook-group-fix {
          width: 100% !important;
        }
      </style>
    <![endif]-->
    <!--[if !mso]><!-->
    <!--<![endif]-->
    <style type="text/css">
      @media only screen and (min-width: 480px) {
        .mj-column-per-100 {
          width: 100% !important;
          max-width: 100%;
        }
      }
    </style>
    <style media="screen and (min-width:480px)">
      .moz-text-html .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }
    </style>
    <style type="text/css"></style>
  </head>

  <body style="word-spacing: normal" data-new-gr-c-s-loaded="14.1098.0">
    <div
      style="
        width: 800px;
        margin: 0 auto;
        border: solid 0px #e1e1e1;
        border-radius: 15px;
      "
    >
      <!--[if mso | IE]>
  <table align='center' border='0' cellpadding='0' cellspacing='0' class='' style='width:600px;' width='600'>
    <tr>
      <td style='line-height:0px;font-size:0px;mso-line-height-rule:exactly;'><![endif]-->
      <div style="margin: 0px auto">
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width: 100%"
        >
          <tbody>
            <tr>
              <td
                style="
                  direction: ltr;
                  font-size: 0px;
                  padding: 40px 60px 16px 60px;
                  text-align: center;
                "
              >
                <!--[if mso | IE]>
          <table role='presentation' border='0' cellpadding='0' cellspacing='0'>
            <tr>
              <td class='' style='vertical-align:top;width:580px;'><![endif]-->
                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="
                    font-size: 0px;
                    text-align: left;
                    direction: ltr;
                    display: inline-block;
                    vertical-align: top;
                    width: 100%;
                  "
                >
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="vertical-align: top"
                    width="100%"
                  >
                    <tbody>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Hi there,</span
                            >
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                            >
                              Application information was sent at
                              {{submissionDate}}.</span
                            >
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!--[if mso | IE]></td></tr></table><![endif]-->
      <!--[if mso | IE]>
  <table align='center' border='0' cellpadding='0' cellspacing='0' class='' style='width:600px;' width='600'>
    <tr>
      <td style='line-height:0px;font-size:0px;mso-line-height-rule:exactly;'><![endif]-->
      <div
        style="margin: 20px auto; margin-top: 0px; margin-bottom: 0px"
        class="esd-text"
      >
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width: 100%"
          class="cke_show_border"
        >
          <tbody>
            <tr>
              <td
                style="
                  direction: ltr;
                  font-size: 0px;
                  padding: 0px 60px 20px 60px;
                  text-align: center;
                "
              >
                <!--[if mso | IE]>
          <table role='presentation' border='0' cellpadding='0' cellspacing='0'>
            <tr>
              <td class='' style='vertical-align:top;width:580px;'><![endif]-->
                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="
                    font-size: 0px;
                    text-align: left;
                    direction: ltr;
                    display: inline-block;
                    vertical-align: top;
                    width: 100%;
                  "
                >
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="vertical-align: top"
                    width="100%"
                    class="cke_show_border"
                  >
                    <tbody>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              text-align: left;
                              font-family: Roboto;
                              font-size: 14px;
                              line-height: 28px;
                              color: #000000;
                              font-weight: 700;
                            "
                          >
                            Customer’s personal information
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 12px 0px;
                            word-break: break-word;
                            height: 1px;
                            border-bottom: 1px solid #00000055;
                          "
                        ></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!--[if mso | IE]></td></tr></table><![endif]-->
      <!--[if mso | IE]>
  <table align='center' border='0' cellpadding='0' cellspacing='0' class='' style='width:600px;' width='600'>
    <tr>
      <td style='line-height:0px;font-size:0px;mso-line-height-rule:exactly;'><![endif]-->
      <div
        style="margin: 20px auto; margin-top: 0px; margin-bottom: 0px"
        class="esd-text"
      >
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width: 100%"
          class="cke_show_border"
        >
          <tbody>
            <tr>
              <td
                style="
                  direction: ltr;
                  font-size: 0px;
                  padding: 0px 60px 20px 60px;
                  text-align: center;
                "
              >
                <!--[if mso | IE]>
          <table role='presentation' border='0' cellpadding='0' cellspacing='0'>
            <tr>
              <td class='' style='vertical-align:top;width:580px;'><![endif]-->
                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="
                    font-size: 0px;
                    text-align: left;
                    direction: ltr;
                    display: inline-block;
                    vertical-align: top;
                    width: 100%;
                  "
                >
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="vertical-align: top"
                    width="100%"
                    class="cke_show_border"
                  >
                    <tbody>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Name</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >{{fullName}}</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Location</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            {{#location}}
                            <div
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                            >
                              {{#name}}{{en}}{{/name}}
                            </div>
                            {{/location}}
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Phone</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >{{phone}}</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Unit Type</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >{{unitType}}</span
                            >
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Email</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >{{email}}</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #00000091;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >Budget</span
                            >
                          </div>
                        </td>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0px 0px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-size: 0px;
                              padding: 0px 0px 0px 0px;
                              word-break: break-word;
                            "
                            align="left"
                            class="esd-frame-element esd-hover-element"
                            contenteditable="true"
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-weight: 400;
                                letter-spacing: 0.268px;
                                color: #000000;
                                line-height: 37px;
                                font-family: Roboto;
                              "
                              >From {{rangeFrom}} to {{rangeTo}}</span
                            >
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!--[if mso | IE]></td></tr></table><![endif]-->
    </div>
  </body>
</html>
