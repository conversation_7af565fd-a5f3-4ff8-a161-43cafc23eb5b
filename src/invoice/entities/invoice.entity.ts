import CrudEntity from 'src/core/entities/crud.entity';
import { XeroPayment } from 'src/xero-payment/entities/xero-payment.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class Invoice extends CrudEntity {
  @Column({ nullable: true })
  xeroInvoiceID?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  originPrice?: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  discountPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalTax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  paid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalDue: number;

  @Column({ type: 'uuid', nullable: true })
  xeroPaymentId?: string;

  @OneToOne(() => XeroPayment, (xeroPayment) => xeroPayment.invoice)
  @JoinColumn({ name: 'xeroPaymentId' })
  xeroPayment?: XeroPayment;
}
