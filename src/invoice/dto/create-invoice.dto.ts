import {
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  Is<PERSON>ptional,
  IsString,
  IsUUI<PERSON>,
} from 'class-validator';

export class CreateInvoiceDto {
  @IsOptional()
  @IsString()
  xeroInvoiceID: string;

  @IsNotEmpty()
  @IsNumber()
  originPrice: number;

  @IsNotEmpty()
  @IsNumber()
  discountPrice: number;

  @IsNotEmpty()
  @IsNumber()
  total: number;

  @IsNotEmpty()
  @IsNumber()
  totalTax: number;

  @IsNotEmpty()
  @IsNumber()
  paid: number;

  @IsNotEmpty()
  @IsNumber()
  totalDue: number;

  @IsOptional()
  @IsUUID()
  xeroPaymentId?: string;
}
