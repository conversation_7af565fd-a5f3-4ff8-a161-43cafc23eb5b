import { Invoice } from '../entities/invoice.entity';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';

class InvoiceFilters
  implements Partial<Pick<Invoice, 'xeroPaymentId' | 'xeroInvoiceID'>>
{
  @IsUUID()
  @IsOptional()
  xeroInvoiceID?: string;

  @IsUUID()
  @IsOptional()
  xeroPaymentId?: string;

  @IsEmail()
  @IsOptional()
  email?: string;
}

export class QueryInvoiceDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => InvoiceFilters)
  filter?: InvoiceFilters;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
