import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { InvoiceService } from './invoice.service';
import { QueryInvoiceDto } from './dto/query-invoices.dto';
import { Roles } from 'src/users/entities/user.entity';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';

@Controller('invoice')
@UseGuards(RolesGuard)
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Get()
  async list(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Query() dto: QueryInvoiceDto,
  ) {
    return await this.invoiceService.list({ role, userId }, dto);
  }

  @Get(':id')
  async getById(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
  ) {
    return await this.invoiceService.getOneByUser({ userId, role }, { id });
  }
}
