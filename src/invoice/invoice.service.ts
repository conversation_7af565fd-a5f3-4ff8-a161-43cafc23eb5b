import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { Invoice } from './entities/invoice.entity';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { QueryInvoiceDto } from './dto/query-invoices.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { Roles } from 'src/users/entities/user.entity';

@Injectable()
export class InvoiceService {
  constructor(
    @InjectRepository(Invoice)
    private readonly invoiceRepository: Repository<Invoice>,
  ) {}

  async createInvoice(data: CreateInvoiceDto): Promise<Invoice> {
    const invoice = this.invoiceRepository.create(data);
    return await this.invoiceRepository.save(invoice);
  }

  async findInvoiceByXeroId(xeroInvoiceID: string): Promise<Invoice> {
    return await this.invoiceRepository.findOne({ where: { xeroInvoiceID } });
  }

  async getOneByUser(
    data: { role: Roles; userId: string },
    query: Partial<Invoice>,
  ) {
    const { role, userId } = data;
    let result: Invoice;
    if ([Roles.USER, Roles.AGENCY].includes(role)) {
      result = await this.invoiceRepository.findOne({
        where: {
          xeroPayment: { userId },
          deletedAt: IsNull(),
          ...query,
        },
      });
    } else {
      result = await this.invoiceRepository.findOne({
        where: {
          deletedAt: IsNull(),
          ...query,
        },
      });
    }

    if (!result) {
      throw new NotFoundException('Invoice not found');
    }
    return result;
  }

  async getOne(query: Partial<Invoice>) {
    const { id } = query;
    const where: FindOptionsWhere<Invoice> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const result = await this.invoiceRepository.findOneBy(where);
    if (!result) {
      throw new NotFoundException('Invoice not found');
    }
    return result;
  }
  async list(
    data: { role: Roles; userId: string },
    queryPackageDto: QueryInvoiceDto,
  ) {
    const { role, userId } = data;
    const { filter } = queryPackageDto;
    const pagination = getPaginationOption(queryPackageDto);

    const queryBuilder = this.invoiceRepository
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.xeroPayment', 'xero_payment')
      .leftJoinAndSelect('xero_payment.user', 'user')
      .skip(pagination.offset)
      .take(pagination.limit);

    if ([Roles.USER, Roles.AGENCY].includes(role)) {
      queryBuilder
        .leftJoin('invoice.xeroPayment', 'xero_payment')
        .where('xero_payment.userId = :userId', {
          userId,
        });
    }

    if (filter) {
      if (filter.xeroInvoiceID)
        queryBuilder.andWhere('invoice.xeroInvoiceID = :xeroInvoiceID', {
          name: filter.xeroInvoiceID,
        });

      if (filter.xeroPaymentId)
        queryBuilder.andWhere('invoice.xeroPaymentId = :xeroPaymentId', {
          description: filter.xeroPaymentId,
        });

      if (filter.email)
        queryBuilder.andWhere('user.email = :email', {
          email: filter.email,
        });
    }

    if (queryPackageDto.search) {
      queryBuilder.andWhere(
        `user.firstName LIKE :search 
           OR user.lastName LIKE :search
            OR user.email LIKE :search`,
        { search: `%${queryPackageDto.search}%` },
      );
    }

    if (queryPackageDto.sort) {
      Object.entries(queryPackageDto.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`invoice.${key}`, order);
      });
    }

    const [packages, total] = await queryBuilder.getManyAndCount();
    return createPaginationResponse(packages, total, pagination);
  }
}
