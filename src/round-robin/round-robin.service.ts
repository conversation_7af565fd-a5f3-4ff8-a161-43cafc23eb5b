import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ContactSaleSubmission,
  EAssignStatus,
} from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { Repository } from 'typeorm';
import { LeadHistory } from './entities/lead-history.entity';
import { TimeSlot } from './entities/time-slot.entity';
import {
  ERoundRobinConfigMode,
  RoundRobinConfig,
} from './entities/round-robin-config.entity';
import { User } from 'src/users/entities/user.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { RoundRobinQueryDto } from './dto/round-robin.query.dto';

@Injectable()
export class RoundRobinService {
  constructor(
    @InjectRepository(RoundRobinConfig)
    private configRepo: Repository<RoundRobinConfig>,

    @InjectRepository(TimeSlot) private timeSlotRepo: Repository<TimeSlot>,

    @InjectRepository(User) private userRepo: Repository<User>,

    @InjectRepository(LeadHistory) private historyRepo: Repository<LeadHistory>,

    @InjectRepository(ContactSaleSubmission)
    private leadRepo: Repository<ContactSaleSubmission>,
  ) {}

  async getConfig(agencyId: string) {
    try {
      return await this.configRepo.findOne({
        where: { agency: { id: agencyId } },
      });
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async updateConfig(agencyId: string, mode: ERoundRobinConfigMode) {
    try {
      let config = await this.getConfig(agencyId);

      if (!config) {
        config = this.configRepo.create({ agencyId, enabled: true, mode });
      }

      config.mode = mode;

      return await this.configRepo.save(config);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  // Turn on/off round robin
  async updateRoundRobinStatus(agencyId: string, enabled: boolean) {
    let config = await this.configRepo.findOne({ where: { agencyId } });
    if (!config) {
      config = this.configRepo.create({
        agencyId,
        enabled,
        mode: ERoundRobinConfigMode.OFF,
      });
    } else {
      config.enabled = enabled;
      config.updatedAt = new Date();
    }
    return this.configRepo.save(config);
  }

  // Check status round robin
  async isRoundRobinEnabled(agencyId: string): Promise<boolean> {
    const config = await this.configRepo.findOne({ where: { agencyId } });
    return config?.enabled ?? false;
  }

  async getTimeSlot(agencyId: string, query: RoundRobinQueryDto) {
    try {
      const pagination = getPaginationOption(query);

      const [result, total] = await this.timeSlotRepo.findAndCount({
        where: { agencyId },
        relations: ['users', 'agency'],
        order: { startTime: 'ASC' },
        skip: pagination.offset,
        take: pagination.limit,
      });

      return createPaginationResponse(result, total, pagination);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async getTimeSlotById(timeSlotId: string) {
    try {
      return await this.timeSlotRepo.findOne({
        where: { id: timeSlotId },
        relations: ['users', 'agency'],
      });
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async deleteTimeSlot(timeSlotId: string) {
    return await this.timeSlotRepo.softDelete(timeSlotId);
  }

  async updateAgentsInTimeSlot(
    timeSlotId: string,
    body: {
      agentIds: string[];
    },
  ) {
    try {
      const timeSlot = await this.getTimeSlotById(timeSlotId);

      if (!timeSlot) {
        throw new BadRequestException(
          `Time slot with ID ${timeSlotId} not found.`,
        );
      }

      await this.timeSlotRepo.manager
        .createQueryBuilder()
        .delete()
        .from('users_timeslots')
        .where('time_slot_id = :timeSlotId', { timeSlotId })
        .execute();

      const pivotData = body.agentIds.map((agentId) => ({
        time_slot_id: timeSlotId,
        user_id: agentId,
      }));

      await this.timeSlotRepo.manager
        .createQueryBuilder()
        .insert()
        .into('users_timeslots')
        .values(pivotData)
        .execute();

      return {
        message: `Successfully updated agents for time slot ${timeSlotId}.`,
        updatedAgentIds: body.agentIds,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update agents for time slot ${timeSlotId}: ${error.message}`,
      );
    }
  }

  // Add Time Slot
  async addTimeSlot(
    agencyId: string,
    startTime: string,
    endTime: string,
    userIds: string[],
  ) {
    // Check overlap
    const slots = await this.timeSlotRepo.find({ where: { agencyId } });
    for (const slot of slots) {
      if (startTime < slot.endTime && endTime > slot.startTime) {
        throw new BadRequestException(
          `Overlap detected with time slot ${slot.startTime} - ${slot.endTime}`,
        );
      }
    }

    // Create and save new time slot
    const newSlot = this.timeSlotRepo.create({
      agencyId,
      startTime,
      endTime,
    });
    const savedSlot = await this.timeSlotRepo.save(newSlot);

    // Prepare data for bulk insert into pivot table
    const pivotData = userIds.map((userId) => ({
      time_slot_id: savedSlot.id,
      user_id: userId,
    }));

    // Use query builder for bulk insert
    await this.timeSlotRepo.manager
      .createQueryBuilder()
      .insert()
      .into('users_timeslots') // Replace with your actual pivot table name
      .values(pivotData)
      .execute();

    return savedSlot;
  }

  // Auto distribute leads in time slots
  @Cron(CronExpression.EVERY_MINUTE) // Every minutes
  async handleLeadDistribution() {
    try {
      const now = new Date();
      const singaporeTime = new Date(
        now.toLocaleString('en-US', { timeZone: 'Asia/Singapore' }),
      );
      const currentTime = `${singaporeTime.getHours()}:${singaporeTime.getMinutes()}`;

      // Get all current timeslots
      const activeSlots = await this.timeSlotRepo
        .createQueryBuilder('slot')
        .where(
          'slot.startTime <= :currentTime AND slot.endTime >= :currentTime',
          { currentTime },
        )
        .getMany();

      for (const slot of activeSlots) {
        // Check config Round Robin of agency
        const config = await this.configRepo.findOne({
          where: { agencyId: slot.agencyId },
        });

        if (!config?.enabled || config.mode === ERoundRobinConfigMode.OFF) {
          continue; // If disbale continue
        }

        // Distribute leads to time slot
        await this.distributeLeads(slot.agencyId);
      }
    } catch (error) {
      console.log(error);
    }
  }

  // Distribute leads to users
  async distributeLeads(agencyId: string) {
    // Check status
    const enabled = await this.isRoundRobinEnabled(agencyId);
    if (!enabled) {
      console.log('Round Robin is disabled for this agency');
      return;
    }

    // Get current timeslot
    const now = new Date();
    const singaporeTime = new Date(
      now.toLocaleString('en-US', { timeZone: 'Asia/Singapore' }),
    );
    const currentTime = `${singaporeTime.getHours()}:${singaporeTime.getMinutes()}`;

    const currentSlot = await this.timeSlotRepo
      .createQueryBuilder('slot')
      .leftJoinAndSelect('slot.users', 'users')
      .where('slot.agencyId = :agencyId', { agencyId })
      .andWhere(
        ':currentTime >= slot.startTime AND :currentTime <= slot.endTime',
        { currentTime },
      )
      .getOne();

    if (!currentSlot) {
      console.log('No active time slot');
      return;
    }

    // Get leads have not been distributed
    const unassignedLeads = await this.leadRepo
      .createQueryBuilder('lead')
      .leftJoinAndSelect('lead.project', 'project')
      .leftJoinAndSelect('lead.user', 'user')
      .where('lead.status = :status', { status: EAssignStatus.UNASSIGNED })
      .andWhere('user.id = :agencyId', { agencyId })
      .getMany();

    if (unassignedLeads.length === 0) {
      console.log('No leads to distribute');
      return;
    }

    // Check the mode
    const config = await this.configRepo.findOne({
      where: { agencyId },
    });

    if (config.mode === ERoundRobinConfigMode.ROUND_ROBIN) {
      this.distributeRoundRobin(unassignedLeads, currentSlot.users, agencyId);
    } else if (config.mode === ERoundRobinConfigMode.DEFAULT) {
      this.distributeDefault(unassignedLeads, currentSlot.users, agencyId);
    }
  }

  // Round Robin logic
  private async distributeRoundRobin(
    leads: ContactSaleSubmission[],
    agents: User[],
    agencyId: string,
  ) {
    let agentIndex = 0;
    for (const lead of leads) {
      const agent = agents[agentIndex % agents.length];
      lead.assignedTo = agent;
      lead.status = EAssignStatus.ASSIGNED;
      await this.leadRepo.save(lead);

      const now = new Date();
      const singaporeTime = new Date(
        now.toLocaleString('en-US', { timeZone: 'Asia/Singapore' }),
      );

      // Logging
      const history = this.historyRepo.create({
        contactSaleSubmission: lead,
        assignedTo: agent,
        agencyId,
        distributedAt: singaporeTime,
      });
      await this.historyRepo.save(history);

      agentIndex++;
    }
  }

  // Default logic
  private async distributeDefault(
    leads: ContactSaleSubmission[],
    agents: User[],
    agencyId: string,
  ) {
    const totalAgents = agents.length;
    let leadIndex = 0;

    for (const agent of agents) {
      while (leadIndex < leads.length) {
        leads[leadIndex].assignedTo = agent;
        leads[leadIndex].status = EAssignStatus.ASSIGNED;
        await this.leadRepo.save(leads[leadIndex]);

        const now = new Date();
        const singaporeTime = new Date(
          now.toLocaleString('en-US', { timeZone: 'Asia/Singapore' }),
        );

        // Logging
        const history = this.historyRepo.create({
          contactSaleSubmission: leads[leadIndex],
          assignedTo: agent,
          agencyId,
          distributedAt: singaporeTime,
        });
        await this.historyRepo.save(history);

        leadIndex++;

        if (leadIndex % totalAgents === 0) break;
      }
    }
  }

  async getContactSaleSubmissionByLeadHistory(
    agencyId: string,
    query: RoundRobinQueryDto,
  ) {
    try {
      const pagination = getPaginationOption(query);

      const qb = this.leadRepo
        .createQueryBuilder('contactSubmissionSale')
        .leftJoinAndSelect('contactSubmissionSale.project', 'project')
        .leftJoinAndSelect('contactSubmissionSale.user', 'user')
        .leftJoinAndSelect('contactSubmissionSale.assignedTo', 'assignedTo')
        .innerJoin(
          'lead_history',
          'lead_history',
          'lead_history.contactSaleSubmissionId = contactSubmissionSale.id AND lead_history.agencyId = :agencyId',
          { agencyId },
        )
        .addSelect('lead_history.distributedAt', 'distributedAt')
        .skip(pagination.offset)
        .take(pagination.limit);

      const { raw, entities } = await qb.getRawAndEntities();

      const total = await qb.getCount();

      const leadsWithDistributedAt = entities.map((lead, index) => ({
        ...lead,
        distributedAt: raw[index]?.distributedAt,
      }));

      return createPaginationResponse(
        leadsWithDistributedAt,
        total,
        pagination,
      );
    } catch (error) {
      throw new BadRequestException(error.message || error);
    }
  }

  // View history
  async getLeadHistory(agencyId: string, query: RoundRobinQueryDto) {
    try {
      const pagination = getPaginationOption(query);

      const [result, total] = await this.historyRepo.findAndCount({
        where: { agencyId },
        relations: ['contactSaleSubmission', 'assignedTo', 'agency'],
        skip: pagination.offset,
        take: pagination.limit,
      });

      return createPaginationResponse(result, total, pagination);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }
}
