import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { RoundRobinService } from './round-robin.service';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { RoundRobinQueryDto } from './dto/round-robin.query.dto';
import { UpdateRoundRobinConfigDto } from './dto/update-config.dto';

@Controller('round-robin')
@Controller()
export class RoundRobinController {
  constructor(private roundRobinService: RoundRobinService) {}

  @Put('status/:agencyId')
  async updateStatus(
    @Param('agencyId') agencyId: string,
    @Body('enabled') enabled: boolean,
  ) {
    return this.roundRobinService.updateRoundRobinStatus(agencyId, enabled);
  }

  @Get('status/:agencyId')
  async getStatus(@Param('agencyId') agencyId: string) {
    return this.roundRobinService.isRoundRobinEnabled(agencyId);
  }

  @Get('config/:agencyId')
  async getConfig(@Param('agencyId') agencyId: string) {
    return this.roundRobinService.getConfig(agencyId);
  }

  @Put('config/:agencyId')
  async updateConfig(
    @Param('agencyId') agencyId: string,
    @Body() body: UpdateRoundRobinConfigDto,
  ) {
    return this.roundRobinService.updateConfig(agencyId, body.mode);
  }

  @Post('time-slot')
  async addTimeSlot(
    @ActiveUser('sub') userId: string,
    @Body()
    body: {
      startTime: string;
      endTime: string;
      agentIds: string[];
    },
  ) {
    return this.roundRobinService.addTimeSlot(
      userId,
      body.startTime,
      body.endTime,
      body.agentIds,
    );
  }

  @Patch('time-slot/:timeSlotId/update-agents')
  async updateAgentsInTimeSlot(
    @Param('timeSlotId') timeSlotId: string,
    @Body()
    body: {
      agentIds: string[];
    },
  ) {
    return this.roundRobinService.updateAgentsInTimeSlot(timeSlotId, body);
  }

  @Get('time-slot')
  async getTimeSlot(
    @ActiveUser('sub') userId: string,
    @Query() query: RoundRobinQueryDto,
  ) {
    return this.roundRobinService.getTimeSlot(userId, query);
  }

  @Get('time-slot/:timeSlotId')
  async getTimeSlotById(@Param('timeSlotId') timeSlotId: string) {
    return this.roundRobinService.getTimeSlotById(timeSlotId);
  }

  @Delete('time-slot/:timeSlotId')
  async deleteTimeSlot(@Param('timeSlotId') timeSlotId: string) {
    return this.roundRobinService.deleteTimeSlot(timeSlotId);
  }

  @Get('history/:agencyId')
  async getHistory(
    @Param('agencyId') agencyId: string,
    @Query() query: RoundRobinQueryDto,
  ) {
    return this.roundRobinService.getContactSaleSubmissionByLeadHistory(
      agencyId,
      query,
    );
  }
}
