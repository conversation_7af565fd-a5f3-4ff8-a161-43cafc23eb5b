import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class LeadHistory extends CrudEntity {
  @Column({ type: 'uuid' })
  contactSaleSubmissionId: string;

  @ManyToOne(() => ContactSaleSubmission)
  @JoinColumn({ name: 'contactSaleSubmissionId' })
  contactSaleSubmission: ContactSaleSubmission;

  @Column({ type: 'uuid' })
  assignedToId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ default: new Date() })
  distributedAt: Date;

  @Column({ type: 'uuid' })
  agencyId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'agencyId' })
  agency: User;
}
