import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Column,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
} from 'typeorm';

@Entity()
export class TimeSlot extends CrudEntity {
  @Column({ type: 'uuid' })
  agencyId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'agencyId' })
  agency: User;

  @Column()
  startTime: string;

  @Column()
  endTime: string;

  @ManyToMany(() => User, (user) => user.timeslots)
  @JoinTable({
    name: 'users_timeslots',
    joinColumn: {
      name: 'time_slot_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
  })
  users: User[];
}
