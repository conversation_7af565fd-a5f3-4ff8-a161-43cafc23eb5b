import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, OneToOne } from 'typeorm';

export enum ERoundRobinConfigMode {
  ROUND_ROBIN = 'round_robin',
  DEFAULT = 'default',
  OFF = 'off',
}

@Entity()
export class RoundRobinConfig extends CrudEntity {
  @Column({ default: true })
  enabled: boolean;

  @Column({ type: 'uuid' })
  agencyId: string;

  @OneToOne(() => User)
  @JoinColumn({ name: 'agencyId' })
  agency: User;

  @Column({
    type: 'enum',
    enum: ERoundRobinConfigMode,
    default: ERoundRobinConfigMode.ROUND_ROBIN,
  })
  mode: ERoundRobinConfigMode;
}
