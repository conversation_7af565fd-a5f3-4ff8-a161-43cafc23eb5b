import { RoundRobinService } from './round-robin.service';
import { RoundRobinController } from './round-robin.controller';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/users/entities/user.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { LeadHistory } from './entities/lead-history.entity';
import { TimeSlot } from './entities/time-slot.entity';
import { RoundRobinConfig } from './entities/round-robin-config.entity';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      RoundRobinConfig,
      TimeSlot,
      LeadHistory,
      ContactSaleSubmission,
      User,
    ]),
  ],
  controllers: [RoundRobinController],
  providers: [RoundRobinService],
})
export class RoundRobinModule {}
