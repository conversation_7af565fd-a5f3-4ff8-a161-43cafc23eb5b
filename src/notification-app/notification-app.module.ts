import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationAppController } from './notification-app.controller';
import { NotificationApp } from './entity/notification-app.entity';
import { NotificationAppService } from './notification-app.service';

@Module({
  imports: [TypeOrmModule.forFeature([NotificationApp])],
  controllers: [NotificationAppController],
  providers: [NotificationAppService],
  exports: [NotificationAppService],
})
export class NotificationAppModule {}
