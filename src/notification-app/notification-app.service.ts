import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { NotificationApp } from './entity/notification-app.entity';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { QueryNotificationAppDto } from './dto/query-notification-app.dto';
import { QueryNotificationAppsDto } from './dto/query-notification-apps.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { CreateNotificationAppDto } from './dto/create-notification-app.dto';
import { UpdateNotificationAppDto } from './dto/update-notification-app.dto';
import { NotificationAppDescription, NotificationAppTitle } from './lang/en';

@Injectable()
export class NotificationAppService {
  constructor(
    @InjectRepository(NotificationApp)
    private readonly notificationAppRepository: Repository<NotificationApp>,
  ) {}

  async getAll(dto: QueryNotificationAppsDto) {
    const { filter, sort, search } = dto;
    const pagination = getPaginationOption(dto);

    const whereNotRead: FindOptionsWhere<NotificationApp> = {
      deletedAt: IsNull(),
      isRead: false,
    };

    const queryBuilder = this.notificationAppRepository
      .createQueryBuilder('notification_app')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.userId) {
        queryBuilder.andWhere('notification_app.userId = :userId', {
          userId: filter.userId,
        });
        whereNotRead.userId = filter.userId;
      }
      if (filter.isRead) {
        queryBuilder.andWhere('notification_app.isRead = :isRead', {
          isRead: filter.isRead,
        });
      }
      if (filter.type) {
        queryBuilder.andWhere('notification_app.type = :type', {
          type: filter.type,
        });
        whereNotRead.type = filter.type;
      }
      if (filter.accessRole) {
        queryBuilder.andWhere('notification_app.accessRole = :accessRole', {
          accessRole: filter.accessRole,
        });
        whereNotRead.accessRole = filter.accessRole;
      }
    }

    if (search) {
      // queryBuilder.andWhere(`userFlag.name LIKE :search`, {
      //   search: `%${search}%`,
      // });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`notification_app.${key}`, order);
      });
    }

    const [result, total] = await queryBuilder.getManyAndCount();
    const unreadCount = await this.notificationAppRepository.count({
      where: whereNotRead,
    });
    return {
      ...createPaginationResponse(
        result.map((item) => ({
          ...item,
          title: this.replaceKeyObj(
            NotificationAppTitle[item.titleKey],
            item.data,
          ),
          description: this.replaceKeyObj(
            NotificationAppDescription[item.descriptionKey],
            item.data,
          ),
        })),
        total,
        pagination,
      ),
      unreadCount,
    };
  }

  replaceKeyObj<T extends string>(data: string, fields: Record<T, string>) {
    const result = data.replace(/{{(.*?)}}/g, (_, key) => {
      return fields[key] !== undefined ? fields[key] : 'NOT_FOUND';
    });
    return result;
  }

  async getOne(query: Partial<NotificationApp>) {
    const { id, userId, type } = query;
    const where: FindOptionsWhere<NotificationApp> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (userId) {
      where.userId = userId;
    }
    if (type) {
      where.type = type;
    }
    const result = await this.notificationAppRepository.findOneBy(where);
    if (!result) {
      throw new NotFoundException('NotificationApp not found');
    }
    return {
      ...result,
      title: this.replaceKeyObj(
        NotificationAppTitle[result.titleKey],
        result.data,
      ),
      description: this.replaceKeyObj(
        NotificationAppDescription[result.descriptionKey],
        result.data,
      ),
    };
  }

  async create(dto: CreateNotificationAppDto) {
    const result = await this.notificationAppRepository.save(dto);
    return result;
  }

  async update(query: Partial<NotificationApp>, dto: UpdateNotificationAppDto) {
    const { id, userId } = query;
    const where: FindOptionsWhere<NotificationApp> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (userId) {
      where.userId = userId;
    }
    const result = await this.notificationAppRepository.update(where, dto);
    return result;
  }

  async delete(query: Partial<NotificationApp>) {
    const { id, type, userId } = query;
    const where: FindOptionsWhere<NotificationApp> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (type) {
      where.type = type;
    }
    if (userId) {
      where.userId = userId;
    }
    const notificationAppData =
      await this.notificationAppRepository.findOneBy(where);
    if (!notificationAppData) {
      throw new NotFoundException('NotificationApp not found');
    }
    const result = await this.notificationAppRepository.softDelete(where);
    return result;
  }
}
