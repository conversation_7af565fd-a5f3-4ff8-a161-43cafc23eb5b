import { EDescriptionK<PERSON> } from '../enums/description-key.enum';
import { ETitleKey } from '../enums/title-key.enum';

export const NotificationAppTitle = {
  [ETitleKey.PAYMENT]: 'Payment',
};

export const NotificationAppDescription = {
  [EDescriptionKey.PENDING_PAYMENT]:
    'Payment pending approval for an order by <b>{{name}}</b>',
  [EDescriptionKey.REJECTED_PAYMENT]: 'Payment was paid by <b>{{name}}</b>',
  [EDescriptionKey.SUCCESSFUL_PAYMENT]: 'Payment was paid by <b>{{name}}</b>',
  [EDescriptionKey.UPLOADED_SCREENSHOT]:
    'Payment pending, <b>{{name}}</b> uploaded screenshot',
};
