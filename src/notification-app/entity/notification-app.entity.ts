import CrudEntity from 'src/core/entities/crud.entity';
import { Roles, User } from 'src/users/entities/user.entity';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ETitleKey } from '../enums/title-key.enum';
import { EDescriptionKey } from '../enums/description-key.enum';

@Entity()
export class NotificationApp extends CrudEntity {
  @Column({ enum: ETitleKey })
  titleKey: ETitleKey;

  @Column({ enum: EDescriptionKey })
  descriptionKey: EDescriptionKey;

  @Column({ default: false })
  isRead: boolean;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  data: Record<string, string>;

  @Column({
    type: 'enum',
    enum: EXeroPaymentType,
  })
  type: EXeroPaymentType;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  userId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({
    type: 'enum',
    enum: Roles,
  })
  accessRole: Roles;
}
