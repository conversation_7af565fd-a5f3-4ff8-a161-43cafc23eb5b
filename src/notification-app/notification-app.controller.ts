import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { Roles } from 'src/users/entities/user.entity';
import { NotificationAppService } from './notification-app.service';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { QueryNotificationAppDto } from './dto/query-notification-app.dto';
import { QueryNotificationAppsDto } from './dto/query-notification-apps.dto';
import { CreateNotificationAppDto } from './dto/create-notification-app.dto';
import { UpdateNotificationAppDto } from './dto/update-notification-app.dto';

@Controller('notification-app')
@UseGuards(RolesGuard)
export class NotificationAppController {
  constructor(
    private readonly notificationAppService: NotificationAppService,
  ) {}

  @Get()
  async getAll(
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
    @Query() dto: QueryNotificationAppsDto,
  ) {
    if (role !== Roles.ADMIN) {
      dto.filter = {
        ...dto.filter,
        userId,
      };
    }
    return await this.notificationAppService.getAll(dto);
  }

  @Post()
  @AccessRoles(Roles.ADMIN)
  async create(
    @ActiveUser('sub') userId: string,
    @Body() dto: CreateNotificationAppDto,
  ) {
    return await this.notificationAppService.create(dto);
  }

  @Put('read/:id')
  async seenById(@Param('id') id: string, @ActiveUser('sub') userId: string) {
    return await this.notificationAppService.update(
      { id, userId },
      { isRead: true },
    );
  }

  @Put('read-all')
  async seenAll(
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
  ) {
    return await this.notificationAppService.update(
      { userId: role === Roles.ADMIN ? undefined : userId, accessRole: role },
      { isRead: true },
    );
  }

  @Patch(':id')
  @AccessRoles(Roles.ADMIN)
  async updateById(
    @Param('id') id: string,
    @Body() dto: UpdateNotificationAppDto,
  ) {
    return await this.notificationAppService.update({ id }, dto);
  }

  @Delete(':id')
  async deleteById(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
  ) {
    return await this.notificationAppService.delete({
      id,
      userId: role === Roles.ADMIN ? undefined : userId,
    });
  }
}
