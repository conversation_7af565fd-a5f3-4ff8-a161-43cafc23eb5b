import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';
import { ETitleKey } from '../enums/title-key.enum';
import { EDescriptionKey } from '../enums/description-key.enum';
import { Roles } from 'src/users/entities/user.entity';

export class CreateNotificationAppDto {
  @IsNotEmpty()
  @IsEnum(ETitleKey)
  titleKey: ETitleKey;

  @IsNotEmpty()
  @IsEnum(EDescriptionKey)
  descriptionKey: EDescriptionKey;

  @IsOptional()
  @IsBoolean()
  isRead?: boolean;

  @IsOptional()
  @IsObject()
  data?: Record<string, string>;

  @IsNotEmpty()
  @IsEnum(EXeroPaymentType)
  type: EXeroPaymentType;

  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsNotEmpty()
  @IsEnum(Roles)
  accessRole?: Roles;
}
