import {
  IsBoolean,
  IsEnum,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { NotificationApp } from '../entity/notification-app.entity';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { Roles } from 'src/users/entities/user.entity';

class NotificationAppFilter
  implements
    Partial<Pick<NotificationApp, 'isRead' | 'userId' | 'type' | 'accessRole'>>
{
  @IsOptional()
  @IsBoolean()
  isRead?: boolean;

  @IsOptional()
  @IsEnum(EXeroPaymentType)
  type?: EXeroPaymentType;

  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsEnum(Roles)
  accessRole?: Roles;
}

export class QueryNotificationAppsDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationAppFilter)
  filter?: NotificationAppFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
