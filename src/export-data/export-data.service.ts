import { Injectable, Res } from '@nestjs/common';
import { FieldInfo, Parser } from 'json2csv';
import * as fs from 'fs';
import * as path from 'path';
import { ProjectService } from 'src/project/project.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Project } from 'src/project/entities/project.entity';
import { In, IsNull, Not, Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { AssetService } from 'src/asset/asset.service';
import { Asset } from 'src/asset/entities/asset.entity';
import { UsersService } from 'src/users/users.service';
import { Roles, User } from 'src/users/entities/user.entity';
import { Response } from 'express';
import { UserConfigService } from 'src/user-config/user-config.service';
import { DomainsService } from 'src/domains/domains.service';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Domain } from 'src/domains/entities/domain.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { ContactSaleSubmissionService } from 'src/contact-sale-submission/contact-sale-submission.service';
import { DownloadContactSaleSubmissionsDto } from './dto/download-contact-sale-submissions.dto';

@Injectable()
export class ExportDataService {
  constructor(
    private readonly projectService: ProjectService,
    private readonly assetService: AssetService,
    private readonly userService: UsersService,
    private readonly userConfigService: UserConfigService,
    private readonly domainService: DomainsService,
    private readonly contactSaleSubmissionService: ContactSaleSubmissionService,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(Asset)
    private readonly assetRepository: Repository<Asset>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    @InjectRepository(Domain)
    private readonly domainRepository: Repository<Domain>,
    @InjectRepository(ContactSaleSubmission)
    private readonly contactSaleSubmissionRepository: Repository<ContactSaleSubmission>,
  ) {}

  private outputDir: string = path.resolve(__dirname, '../../downloads');

  async exportToCSV<T>(
    fields: Array<string | FieldInfo<any>>,
    data: T[],
    fileName: string,
  ) {
    try {
      if (!fs.existsSync(this.outputDir)) {
        fs.mkdirSync(this.outputDir, { recursive: true });
      }

      const parser = new Parser({ fields });
      const csv = parser.parse(data);
      const filePath = path.join(this.outputDir, fileName);
      fs.writeFileSync(filePath, csv, 'utf8');
      console.log(`✅ CSV file saved as ${filePath}`);
      return filePath;
    } catch (err) {
      console.error('❌ Error exporting to CSV:', err);
    }
  }

  downloadCSV(@Res() res: Response, filePath: string, fileName: string) {
    return res.download(filePath, fileName, (err) => {
      if (err) {
        console.error('❌ Error sending file:', err);
        res.status(500).send('Could not download the file');
      } else {
        // ✅ Delete file after successful download
        fs.unlink(filePath, (unlinkErr) => {
          if (unlinkErr) {
            console.error('❌ Error deleting file:', unlinkErr);
          } else {
            console.log(`🗑️ Deleted temporary file: ${filePath}`);
          }
        });
      }
    });
  }

  async exportProjects(res: Response, userId: string) {
    const lang = ELangCode.en;
    const queryBuilder = this.projectRepository
      .createQueryBuilder('project')
      .leftJoin('project.developer', 'developer')
      .leftJoin('project.category', 'category')
      .leftJoin('project.location', 'location')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin(
        'project.userProjects',
        'userProjects',
        'userProjects.projectId = project.id AND userProjects.userId = :userId',
        { userId },
      )
      .select('project.id', 'id')
      .addSelect('project.slug', 'slug')
      .addSelect('project.tenure', 'tenure')
      .addSelect('project.tenureEffectFrom', 'tenureEffectFrom')
      .addSelect('project.coordinates', 'coordinates')
      .addSelect('project.area', 'area')
      .addSelect('project.upcomingLaunch', 'upcomingLaunch')
      .addSelect('project.promotion', 'promotion')
      .addSelect('project.featured', 'featured')
      .addSelect('project.marketSegment', 'marketSegment')
      .addSelect('project.expectedTop', 'expectedTop')
      .addSelect('project.whatIsNextStatus', 'whatIsNextStatus')
      .addSelect('project.whatIsNextStatusOptions', 'whatIsNextStatusOptions')
      .addSelect('project.manualTotalUnitsCount', 'manualTotalUnitsCount')
      .addSelect(
        'project.manualAvailableUnitsCount',
        'manualAvailableUnitsCount',
      )
      .addSelect('project.googleMapUrl', 'googleMapUrl')
      .addSelect('project.desktopLogoId', 'desktopLogoId')
      .addSelect('project.mobileLogoId', 'mobileLogoId')
      //siteplanImages exist when handling
      .addSelect('project.photoId', 'photoId')
      //medias exist when handling
      .addSelect('COUNT(floorPlans.id)', 'floorPlanCount')
      .addSelect(['category.id', 'category.slug'])
      .addSelect(['location.id', 'location.slug'])
      .addSelect(['developer.id'])
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .addSelect(
        createMultilingualSelect('project', 'description', lang),
        'description',
      )
      .addSelect(
        createMultilingualSelect('project', 'address', lang),
        'address',
      )
      .addSelect(
        createMultilingualSelect('project', 'amenityHtml', lang),
        'amenityHtml',
      )
      .addSelect(
        createMultilingualSelect('project', 'facilityHtml', lang),
        'facilityHtml',
      )
      .addSelect(
        createMultilingualSelect('category', 'name', lang),
        'category_name',
      )
      .addSelect(
        createMultilingualSelect('category', 'shortname', lang),
        'category_shortname',
      )
      .addSelect(
        createMultilingualSelect('location', 'name', lang),
        'location_name',
      )
      .addSelect(
        createMultilingualSelect('developer', 'name', lang),
        'developer_name',
      )
      .addSelect(
        `CASE 
            WHEN userProjects.id IS NOT NULL THEN
              jsonb_build_object(
                'id', "userProjects".id, 
                'weight', "userProjects".weight, 
                'featured',  "userProjects".featured, 
                'promotion',  "userProjects".promotion
              )
            ELSE
              NULL
            END`,
        'userProject',
      )
      .addSelect('project.directCommission', 'directCommission')
      .addSelect('project.isCommissionUpTo', 'isCommissionUpTo')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('location.id')
      .addGroupBy('userProjects.id')
      .addGroupBy('developer.id')
      //sort by createdAt
      .addOrderBy('project.createdAt', 'DESC');

    const [data, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    const beautifiedData = data.map(this.projectService._beautifyProject);
    const result = await Promise.all(
      beautifiedData.map(async (project) => {
        const excludeAssetIds = [
          project.photoId,
          project.seoImageId,
          project.sitePlanImageId,
          project.elevationChartId,
        ].filter(Boolean);

        const medias = await this.assetService.getByRelatedId(
          project.id,
          excludeAssetIds,
        );

        const siteplanImages = await this.assetRepository.find({
          where: {
            project: { id: project.id },
          },
          select: ['id', 'type', 'urls'],
        });

        return {
          ...project,
          medias,
          siteplanImages: siteplanImages?.length ? siteplanImages : [],
        };
      }),
    );

    const column = [
      { label: 'id', value: 'id' },
      { label: 'name', value: 'name' },
      { label: 'area', value: 'area' },
      { label: 'tenure', value: 'tenure' },
      { label: 'developerId', value: 'developer.id' },
      { label: 'developerName', value: 'developer.name' },
      { label: 'marketSegment', value: 'marketSegment' },
      { label: 'categoryId', value: 'category.id' },
      { label: 'categoryName', value: 'category.name' },
      { label: 'locationId', value: 'location.id' },
      { label: 'locationName', value: 'location.name' },
      { label: 'whatIsNextSection', value: 'whatIsNextStatus' },
      { label: 'whatIsNextSectionOptions', value: 'whatIsNextStatusOptions' },
      { label: 'tenureEffectFrom', value: 'tenureEffectFrom' },
      { label: 'expectedTop', value: 'expectedTop' },
      { label: 'directCommission', value: 'directCommission' },
      { label: 'manualTotalUnitsCount', value: 'manualTotalUnitsCount' },
      {
        label: 'manualAvailableUnitsCount',
        value: 'manualAvailableUnitsCount',
      },
      { label: 'address', value: 'address' },
      { label: 'description', value: 'description' },
      { label: 'amenityHtml', value: 'amenityHtml' },
      { label: 'facilityHtml', value: 'facilityHtml' },
      { label: 'googleMapUrl', value: 'googleMapUrl' },
      //
      { label: 'desktopLogoId', value: 'desktopLogoId' },
      { label: 'mobileLogoId', value: 'mobileLogoId' },
      {
        label: 'siteplanImages',
        value: (row: Project) =>
          (row?.siteplanImages || []).map((item) => item.id),
      },
      //pictures
      { label: 'photoId', value: 'photoId' },
      {
        label: 'medias',
        value: (row: any) => (row?.medias || []).map((item) => item.id),
      },
    ];
    const fileName = 'projects.csv';
    const pathFile = await this.exportToCSV(column, result, fileName);
    return this.downloadCSV(res, pathFile, fileName);
  }

  async exportUsers(res: Response) {
    const result = await this.userRepository.find({
      where: {
        deletedAt: IsNull(),
        role: Not(In([Roles.ADMIN])),
      },
      relations: ['config', 'config.domains'],
    });
    const column = [
      { label: 'id', value: 'id' },
      {
        label: 'fullName',
        value: (row: User) => `${row.firstName} ${row.lastName}`,
      },
      // { label: 'First Name', value: 'firstName' },
      // { label: 'Last Name', value: 'lastName' },
      { label: 'phone', value: 'phone' },
      { label: 'email', value: 'email' },
      {
        label: 'directoryDomain',
        value: (row: User) => {
          const domains = row?.config?.domains || [];
          const domain = domains.find((domain) => domain.primary);
          return domain?.name || '';
        },
      },
      { label: 'createdAt', value: 'createdAt' },
      { label: 'updatedAt', value: 'updatedAt' },
    ];

    const fileName = 'users.csv';
    const pathFile = await this.exportToCSV(column, result, fileName);
    return this.downloadCSV(res, pathFile, fileName);
  }

  async exportContactSaleSubmissions(
    res: Response,
    query: DownloadContactSaleSubmissionsDto,
  ) {
    const {
      fileName,
      sort,
      endDate,
      fromScore,
      lang = ELangCode.en,
      search,
      startDate,
      toScore,
      userId,
    } = query;

    const queryBuilder = this.contactSaleSubmissionRepository
      .createQueryBuilder('contactSubmissionSale')
      .leftJoinAndSelect('contactSubmissionSale.project', 'project')
      .leftJoinAndSelect('contactSubmissionSale.user', 'user');
    // .leftJoinAndSelect('contactSubmissionSale.assignedTo', 'assignedTo')

    // if (userId) {
    //   queryBuilder.andWhere(
    //     'contactSubmissionSale.assignedTo = :assignedToId',
    //     {
    //       assignedToId: userId,
    //     },
    //   );
    // }

    if (search) {
      queryBuilder.andWhere(
        '(contactSubmissionSale.name ILIKE :search OR contactSubmissionSale.phone ILIKE :search OR contactSubmissionSale.email ILIKE :search OR ' +
          "(project.name ->> 'en') ILIKE :search)",
        {
          search: `%${search}%`,
        },
      );
    }

    if (startDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt >= :startDate', {
        startDate: startDate.toISOString(),
      });
    }

    if (endDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt <= :endDate', {
        endDate: endDate.toISOString(),
      });
    }

    if (fromScore) {
      queryBuilder.andWhere('contactSubmissionSale.score >= :fromScore', {
        fromScore,
      });
    }

    if (query.toScore) {
      queryBuilder.andWhere('contactSubmissionSale.score <= :toScore', {
        toScore,
      });
    }

    if (userId) {
      queryBuilder.andWhere('contactSubmissionSale.user.id = :userId', {
        userId,
      });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        if (key === 'project') {
          queryBuilder.addOrderBy(`project.name ->> 'en'`, order);
        } else {
          queryBuilder.addOrderBy(`contactSubmissionSale.${key}`, order);
        }
      });
    }

    const result = await queryBuilder.getMany();
    const column = [
      { label: 'id', value: 'id' },
      { label: 'Customer Name', value: 'name' },
      { label: 'Customer Email', value: 'email' },
      { label: 'Customer Phone', value: 'phone' },
      {
        label: 'Agent Name',
        value: (row: ContactSaleSubmission) =>
          `${row?.user?.firstName || ''} ${row?.user?.lastName || ''}`,
      },
      { label: 'Agent Email', value: 'user.email' },
      { label: 'Agent Phone', value: 'user.phone' },
      { label: 'Project Id', value: 'project.id' },
      {
        label: 'Project Name',
        value: (row: ContactSaleSubmission) => {
          const obj = row?.project?.name || {};
          return obj[lang] || '';
        },
      },
      { label: 'Score', value: 'score' },
      { label: 'Submit At', value: 'createdAt' },
      { label: 'Appointment', value: 'appointment' },
    ];

    const _fileName = `${fileName}.csv`;
    const pathFile = await this.exportToCSV(column, result, _fileName);
    return this.downloadCSV(res, pathFile, _fileName);
  }
}
