import { forwardRef, Module } from '@nestjs/common';
import { ExportDataService } from './export-data.service';
import { ExportDataController } from './export-data.controller';
import { ProjectModule } from 'src/project/project.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Project } from 'src/project/entities/project.entity';
import { AssetModule } from 'src/asset/asset.module';
import { Asset } from 'src/asset/entities/asset.entity';
import { User } from 'src/users/entities/user.entity';
import { UsersModule } from 'src/users/users.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Domain } from 'src/domains/entities/domain.entity';
import { UserConfigModule } from 'src/user-config/user-config.module';
import { DomainsModule } from 'src/domains/domains.module';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { ContactSaleSubmissionModule } from 'src/contact-sale-submission/contact-sale-submission.module';
import { EmailTemplate } from 'src/email-template/entities/email-template.entity';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { ContactSaleSubmissionLog } from 'src/contact-sale-submission/entities/contact-sale-submission-log.entity';
import { ContactSaleActionLog } from 'src/contact-sale-submission/entities/contact-sale-submission-action-log.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      Asset,
      User,
      UserConfig,
      Domain,
      ContactSaleSubmission,
      Project,
      UserConfig,
      EmailTemplate,
      IpTracking,
      ContactSaleSubmissionLog,
      ContactSaleActionLog,
    ]),
    forwardRef(() => ProjectModule),
    forwardRef(() => AssetModule),
    forwardRef(() => UsersModule),
    forwardRef(() => UserConfigModule),
    forwardRef(() => DomainsModule),
    forwardRef(() => ContactSaleSubmissionModule),
  ],
  controllers: [ExportDataController],
  providers: [ExportDataService],
  exports: [ExportDataService],
})
export class ExportDataModule {}
