import { Body, Controller, Get, Query, Res, UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { ExportDataService } from './export-data.service';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { Response } from 'express';
import { DownloadContactSaleSubmissionsDto } from './dto/download-contact-sale-submissions.dto';

@Controller('export-data')
@UseGuards(RolesGuard)
export class ExportDataController {
  constructor(private readonly exportDataService: ExportDataService) {}

  @Get('projects')
  @AccessRoles(Roles.ADMIN)
  async exportProjects(
    @Res() res: Response,
    @ActiveUser('sub') userId: string,
  ) {
    return await this.exportDataService.exportProjects(res, userId);
  }

  @Get('users')
  @AccessRoles(Roles.ADMIN)
  async exportUsers(@Res() res: Response, @ActiveUser('sub') userId: string) {
    return await this.exportDataService.exportUsers(res);
  }

  @Get('contact-sale-submissions')
  async contactSaleSubmissions(
    @Res() res: Response,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
    @Query() query: DownloadContactSaleSubmissionsDto,
  ) {
    query.userId = role === Roles.ADMIN ? query.userId : userId;
    return await this.exportDataService.exportContactSaleSubmissions(
      res,
      query,
    );
  }
}
