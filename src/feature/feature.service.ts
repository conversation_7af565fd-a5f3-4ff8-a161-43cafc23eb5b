import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Feature } from './entities/feature.entity';
import { QueryFeatureDto } from './dto/query-feature.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { CreateFeatureDto } from './dto/create-feature.dto';

@Injectable()
export class FeatureService {
  constructor(
    @InjectRepository(Feature)
    private readonly featureRepository: Repository<Feature>,
  ) {}

  async create(body: CreateFeatureDto): Promise<Feature> {
    const feature = await this.featureRepository.findBy({ name: body.name });

    if (feature.length !== 0) {
      throw new BadRequestException('Feature already exists');
    }

    await this.featureRepository.save(body);
    return await this.featureRepository.findOneBy({ name: body.name });
  }

  async findAll(queryDto: QueryFeatureDto) {
    const { name } = queryDto;
    const pagination = getPaginationOption(queryDto);

    const query = this.featureRepository
      .createQueryBuilder('feature')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (name) {
      query.andWhere('feature.name = :name', { name });
    }

    const [data, total] = await query.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async update(body: UpdateFeatureDto, id: string) {
    const feature = await this.featureRepository.findOneBy({ id });

    if (!feature) {
      throw new NotFoundException('Feature not found');
    }

    await this.featureRepository.update(id, body);
    return await this.featureRepository.findOneBy({ id });
  }
}
