import CrudEntity from 'src/core/entities/crud.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { EFeature } from '../feature.enum';

@Entity()
export class Feature extends CrudEntity {
  @Column()
  name: string;

  @Column({ default: true })
  enabled: boolean;

  @OneToMany(() => UserFeature, (userFeature) => userFeature.feature)
  userFeature: UserFeature[];

  @Column({
    type: 'enum',
    enum: EFeature,
    nullable: true,
  })
  type: EFeature;
}
