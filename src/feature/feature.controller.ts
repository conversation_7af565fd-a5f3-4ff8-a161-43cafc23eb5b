import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { FeatureService } from './feature.service';
import { QueryFeatureDto } from './dto/query-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { CreateFeatureDto } from './dto/create-feature.dto';

@Controller('feature')
export class FeatureController {
  constructor(private readonly featureService: FeatureService) {}

  @Post()
  create(@Body() body: CreateFeatureDto) {
    return this.featureService.create(body);
  }

  @Get()
  findAll(@Query() queryDto: QueryFeatureDto) {
    return this.featureService.findAll(queryDto);
  }

  @Patch(':id')
  update(@Body() body: UpdateFeatureDto, @Param('id') id: string) {
    return this.featureService.update(body, id);
  }
}
