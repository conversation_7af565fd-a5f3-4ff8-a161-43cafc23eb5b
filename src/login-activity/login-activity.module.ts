import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoginActivity } from './entities/login-activity.entity';
import { LoginActivityService } from './login-activity.service';
import { LoginActivityController } from './login-activity.controller';

@Module({
  imports: [TypeOrmModule.forFeature([LoginActivity])],
  controllers: [LoginActivityController],
  providers: [LoginActivityService],
  exports: [LoginActivityService],
})
export class LoginActivityModule {}
