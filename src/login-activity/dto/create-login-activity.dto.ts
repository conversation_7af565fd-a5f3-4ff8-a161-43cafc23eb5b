import { IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateLoginActivityDto {
  @IsUUID()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  device?: Record<string, string>;

  @IsString()
  @IsOptional()
  ip?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsOptional()
  @IsString()
  referral?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  browser?: string;

  @IsString()
  @IsOptional()
  os?: string;
}
