import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { EStatus } from '../login-activity.enum';

@Entity()
export class LoginActivity extends CrudEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ nullable: true, type: 'jsonb' })
  device: Record<string, string>;

  @Column({ nullable: true })
  ip: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  referral: string;

  @Column({ nullable: true })
  os: string;

  @Column({
    type: 'enum',
    enum: EStatus,
    nullable: true,
  })
  status: EStatus;

  @Column({ nullable: true })
  sessionId: string;

  @Column({ nullable: true })
  loggedOutAt: Date;
}
