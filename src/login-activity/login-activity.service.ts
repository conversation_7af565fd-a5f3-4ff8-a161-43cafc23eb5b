import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LoginActivity } from './entities/login-activity.entity';
import { Repository } from 'typeorm';
import { CreateLoginActivityDto } from './dto/create-login-activity.dto';
import { QueryLoginActivityDto } from './dto/query-login-activity.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EOrderType } from 'src/core/enums/sort.enum';

@Injectable()
export class LoginActivityService {
  constructor(
    @InjectRepository(LoginActivity)
    private readonly loginActivityRepo: Repository<LoginActivity>,
  ) {}

  async create(createLoginActivityDto: CreateLoginActivityDto) {
    return await this.loginActivityRepo.save(createLoginActivityDto);
  }

  async findAll(queryLoginActivityDto: QueryLoginActivityDto) {
    const pagination = getPaginationOption(queryLoginActivityDto);
    const { sort, sortBy = 'createdAt' } = queryLoginActivityDto;

    const queryBuilder = this.loginActivityRepo
      .createQueryBuilder('loginActivity')
      .leftJoinAndSelect('loginActivity.user', 'user')
      .orderBy(
        `loginActivity.${sortBy}`,
        sort === EOrderType.ASC ? 'ASC' : 'DESC',
      )
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (queryLoginActivityDto.userId) {
      queryBuilder.andWhere('loginActivity.userId = :userId', {
        userId: queryLoginActivityDto.userId,
      });
    }

    if (queryLoginActivityDto.device) {
      queryBuilder.andWhere('loginActivity.device = :device', {
        device: queryLoginActivityDto.device,
      });
    }

    if (queryLoginActivityDto.ip) {
      queryBuilder.andWhere('loginActivity.ip = :ip', {
        ip: queryLoginActivityDto.ip,
      });
    }

    if (queryLoginActivityDto.location) {
      queryBuilder.andWhere('loginActivity.location = :location', {
        location: queryLoginActivityDto.location,
      });
    }

    if (queryLoginActivityDto.referral) {
      queryBuilder.andWhere('loginActivity.referral = :referral', {
        referral: queryLoginActivityDto.referral,
      });
    }

    if (queryLoginActivityDto.startDate && queryLoginActivityDto.endDate) {
      if (queryLoginActivityDto.startDate > queryLoginActivityDto.endDate) {
        throw new Error('Start date must be less than end date');
      }
    }

    if (queryLoginActivityDto.startDate) {
      queryBuilder.andWhere('loginActivity.createdAt >= :startDate', {
        startDate: queryLoginActivityDto.startDate,
      });
    }

    if (queryLoginActivityDto.endDate) {
      queryBuilder.andWhere('loginActivity.createdAt <= :endDate', {
        endDate: queryLoginActivityDto.endDate,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }
}
