import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { LoginActivityService } from './login-activity.service';
import { CreateLoginActivityDto } from './dto/create-login-activity.dto';
import { QueryLoginActivityDto } from './dto/query-login-activity.dto';

@Controller('login-activity')
export class LoginActivityController {
  constructor(private readonly loginActivityService: LoginActivityService) {}

  @Post()
  create(@Body() createLoginActivityDto: CreateLoginActivityDto) {
    return this.loginActivityService.create(createLoginActivityDto);
  }

  @Get()
  listing(@Query() queryLoginActivityDto: QueryLoginActivityDto) {
    return this.loginActivityService.findAll(queryLoginActivityDto);
  }
}
