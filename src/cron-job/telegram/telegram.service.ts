import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Bot } from 'src/bot/entities/bot.entity';
import { EFeature } from 'src/feature/feature.enum';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { TelegramService } from 'src/telegram/telegram.service';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { User } from 'src/users/entities/user.entity';
import { In, Repository } from 'typeorm';
import * as moment from 'moment-timezone';

@Injectable()
export class TelegramJobService {
  constructor(
    @InjectRepository(IpTracking)
    private readonly ipTrackingRepo: Repository<IpTracking>,
    @InjectRepository(Bot)
    private readonly botRepo: Repository<Bot>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
    @InjectRepository(UserFeature)
    private readonly userFeatureRepo: Repository<UserFeature>,

    private readonly telegramService: TelegramService,
  ) {}

  public async collectDataAndSendMessage() {
    const singaporeTimezone = 'Asia/Singapore';
    const singaporeOffset = 8;
    const today = new Date();

    const yesterdayStart = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate() - 1,
        0 - singaporeOffset,
        0,
        0,
      ),
    );

    const yesterdayEnd = new Date(
      Date.UTC(
        today.getUTCFullYear(),
        today.getUTCMonth(),
        today.getUTCDate() - 1,
        23 - singaporeOffset,
        59,
        59,
      ),
    );

    const yesterdayStartInDatabaseTimezone = moment(yesterdayStart)
      .tz(singaporeTimezone)
      .format();
    const yesterdayEndInDatabaseTimezone = moment(yesterdayEnd)
      .tz(singaporeTimezone)
      .format();

    const ipTrackingQueryBuilder = this.ipTrackingRepo
      .createQueryBuilder('ipTracking')
      .where('ipTracking.country = :country', { country: 'Singapore' })
      .andWhere('ipTracking.createdAt BETWEEN :start AND :end', {
        start: yesterdayStartInDatabaseTimezone,
        end: yesterdayEndInDatabaseTimezone,
      });

    const totalClicksByDomain = await ipTrackingQueryBuilder
      .groupBy('ipTracking.domain')
      .select([
        'COUNT(ipTracking.projectId) as totalClicks',
        'ipTracking.domain',
      ])
      .getRawMany();

    const totalVisitedProjectsByDomain = await ipTrackingQueryBuilder
      .groupBy('ipTracking.domain')
      .select([
        'COUNT(DISTINCT ipTracking.projectId) as totalVisitedProjects',
        'ipTracking.domain',
      ])
      .distinct(true)
      .getRawMany();

    const bots = await this.botRepo.find();

    const data = [];
    for (const bot of bots) {
      const userConfig = await this.userConfigRepo.findOne({
        where: {
          userId: bot.userId,
        },
        relations: {
          domains: true,
        },
      });

      let matchedDomains: string;
      const totalClicks = totalClicksByDomain?.find((item) => {
        const isMatchedDomain = userConfig?.domains?.some(
          (domain) => domain.name === item.ipTracking_domain,
        );

        if (isMatchedDomain) {
          matchedDomains = item.ipTracking_domain;
        }

        return isMatchedDomain;
      });

      const totalVisitedProjects = totalVisitedProjectsByDomain?.find((item) =>
        userConfig?.domains?.some(
          (domain) => domain.name === item.ipTracking_domain,
        ),
      );
      const statsByDomain = {
        domain: matchedDomains,
        totalClicks: totalClicks?.totalclicks ?? 0,
        totalVisitedProjects: totalVisitedProjects?.totalvisitedprojects ?? 0,
        chatId: bot.chatId,
        userId: userConfig?.userId,
      };

      data.push(statsByDomain);
    }

    for (const item of data) {
      const isEnableTracking = await this.isEnableTrackingIp(item.userId).catch(
        (e) => {
          console.log('Error', e);
        },
      );

      if (!isEnableTracking) {
        continue;
      }

      const formattedDate = moment(yesterdayStart)
        .tz(singaporeTimezone)
        .format('DD/MM/YYYY');
      const weekday = moment(yesterdayStart)
        .tz(singaporeTimezone)
        .format('dddd');

      const message = `${formattedDate}, ${weekday}\nTotal Clicks: ${item.totalClicks}\nTotal Visited Projects: ${item.totalVisitedProjects}`;

      await this.telegramService
        .sendMessage(item.chatId, message, { isPin: true })
        .catch((error) => {
          console.error('Error sending message:', error.message);
        });

      await this.telegramService.sleep(3000);
    }
  }

  private async isEnableTrackingIp(userId: string) {
    const userFeatures = await this.userFeatureRepo.find({
      where: {
        userId,
      },
      relations: ['feature'],
    });

    const ipTrackingFeature = userFeatures?.find((userFeature) => {
      return (
        userFeature?.feature?.type ===
        EFeature.IPAccessTrackerWithTelegramAlerts
      );
    });

    if (!ipTrackingFeature) {
      return true;
    }

    return ipTrackingFeature.enabled;
  }
}
