import { Modu<PERSON> } from '@nestjs/common';
import { TelegramJobService } from './telegram.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { Bot } from 'src/bot/entities/bot.entity';
import { TelegramModule } from 'src/telegram/telegram.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { User } from 'src/users/entities/user.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([IpTracking, Bot, UserConfig, User, UserFeature]),
    TelegramModule,
  ],
  controllers: [],
  providers: [TelegramJobService],
  exports: [TelegramJobService],
})
export class TelegramJobModule {}
