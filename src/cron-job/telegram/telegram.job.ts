import { TelegramJobService } from './telegram.service';
import { Cron } from '@nestjs/schedule';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TelegramJob {
  constructor(private readonly telegramJobService: TelegramJobService) {}

  // Define the cron job
  @Cron(process.env.TELE_BOT_SCHEDULE || '0 12 * * *')
  handleCron() {
    console.log(`Running a cron job at ${process.env.TELE_BOT_SCHEDULE}`);
    // Add your logic here
    this.telegramJobService.collectDataAndSendMessage();
  }
}
