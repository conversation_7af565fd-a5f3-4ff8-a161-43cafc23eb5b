import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Notification } from './entities/notification.entity';
import { In, Repository } from 'typeorm';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { ENotificationKeys } from './enums/notification.enum';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepo: Repository<Notification>,
  ) {}

  async updateByKey(body: CreateNotificationDto) {
    const notification = await this.notificationRepo.findOneBy({
      key: body.key,
    });

    if (body.value === null || body.value === undefined) {
      body.value = null;
    }
    if (!notification) {
      await this.notificationRepo.save(body);
    }
    await this.notificationRepo.update({ key: body.key }, body);
    return this.getDetailByKey(body.key);
  }

  async getDetailByKey(key: string) {
    const item = await this.notificationRepo.findOneBy({ key });

    if (!item) {
      throw new NotFoundException('Notification is not found');
    }

    const result = {
      key: item.key,
      value: item.value,
      updatedAt: item.updatedAt,
    };

    return result;
  }

  async getSiteDisclaimers() {
    const results = await this.notificationRepo.find({
      where: {
        key: In([
          ENotificationKeys.FooterDisclaimer,
          ENotificationKeys.FormNotice,
        ]),
      },
    });

    return {
      footerDisclaimer:
        results.find((i) => i.key === ENotificationKeys.FooterDisclaimer)
          ?.value ?? null,
      formNotice:
        results.find((i) => i.key === ENotificationKeys.FormNotice)?.value ??
        null,
    };
  }
}
