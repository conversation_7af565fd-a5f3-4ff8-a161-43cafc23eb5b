import { Body, Controller, Get, Put, Query } from '@nestjs/common';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { NotificationService } from './notification.service';
import { CreateNotificationDto } from './dto/create-notification.dto';

@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('by-key')
  getDetailByKey(@Query('key') key: string) {
    return this.notificationService.getDetailByKey(key);
  }

  @Put('by-key')
  @AccessRoles(Roles.ADMIN)
  updateByKey(@Body() body: CreateNotificationDto) {
    return this.notificationService.updateByKey(body);
  }
}
