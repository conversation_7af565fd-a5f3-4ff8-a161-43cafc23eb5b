import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { ECategoryType } from '../enums/category-type.enum';

@Entity()
export class Category extends CrudEntity {
  @Column({ type: 'jsonb' })
  name?: Record<string, string>;

  @Column({ type: 'jsonb' })
  shortname?: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @OneToMany(() => Project, (project) => project.category)
  projects: Project[];

  @Column({
    type: 'enum',
    enum: ECategoryType,
    default: ECategoryType.Residential,
  })
  type: ECategoryType;
}
