import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { ECategoryType } from '../enums/category-type.enum';
import { OmitType } from '@nestjs/mapped-types';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum ECategorySortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  name = 'name',
  type = 'type',
}

export class QueryCategoryDto extends OmitType(PaginationQueryDto, ['lang']) {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(ECategoryType)
  @IsOptional()
  type?: ECategoryType;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(ECategorySortBy)
  @IsOptional()
  sortBy?: ECategorySortBy;
}
