import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { QueryCategoryDto } from './dto/query-category.dto';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('category')
@CacheOption('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  @Public()
  @UseInterceptors(CacheInterceptor)
  getAll(@Query() query: LanguageQueryDto) {
    return this.categoryService.getAll(query.lang);
  }

  @Get('share')
  @Public()
  @UseInterceptors(CacheInterceptor)
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_CATEGORIES])
  share(@Query() query: LanguageQueryDto) {
    return this.categoryService.getAll(query.lang);
  }

  @Get('listing')
  @Public()
  @UseInterceptors(CacheInterceptor)
  listing(@Query() query: QueryCategoryDto) {
    return this.categoryService.listing(query);
  }

  @Get('group-by-type')
  @Public()
  groupByType(@Query() query: LanguageQueryDto) {
    return this.categoryService.groupByType(query.lang);
  }

  @Post()
  create(@Body() body: CreateCategoryDto) {
    return this.categoryService.create(body);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() body: UpdateCategoryDto) {
    return this.categoryService.update(id, body);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.categoryService.delete(id);
  }
}
