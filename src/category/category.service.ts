import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Category } from './entities/category.entity';
import { Repository } from 'typeorm';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { generateSlug } from 'src/core/utils/slug.util';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { ECategoryType } from './enums/category-type.enum';
import { ECategorySortBy, QueryCategoryDto } from './dto/query-category.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EOrderType } from 'src/core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepo: Repository<Category>,
    private readonly redisService: RedisService,
  ) {}

  async create(body: CreateCategoryDto) {
    if (body.slug) {
      const existed = await this.categoryRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug =
      body.slug ?? (await generateSlug(body.name, this.categoryRepo));

    const data: Partial<Category> = {
      ...body,
      slug,
      name: { [ELangCode.en]: body.name },
      shortname: { [ELangCode.en]: body.shortname },
    };

    await this.redisService.deletePattern('category@*');
    return await this.categoryRepo.save(data);
  }

  async getAll(lang = ELangCode.en) {
    return await this.categoryRepo
      .createQueryBuilder('category')
      .select('category.id', 'id')
      .addSelect('category.slug', 'slug')
      .addSelect('category.type', 'type')
      .addSelect('category.createdAt', 'createdAt')
      .addSelect('category.updatedAt', 'updatedAt')
      .addSelect('category.deletedAt', 'deletedAt')
      .addSelect(createMultilingualSelect('category', 'name', lang), 'name')
      .addSelect(
        createMultilingualSelect('category', 'shortname', lang),
        'shortname',
      )
      .getRawMany();
  }

  async listing(query: QueryCategoryDto) {
    const pagination = getPaginationOption(query);
    const orderBy = query.sortBy || ECategorySortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.categoryRepo
      .createQueryBuilder('category')
      .orderBy(`category.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.name) {
      queryBuilder.andWhere(
        `"category"."name"->>'${ELangCode.en}' ILIKE :name`,
        { name: `%${query.name}%` },
      );
    }

    if (query.type) {
      queryBuilder.andWhere('category.type = :type', { type: query.type });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async groupByType(lang = ELangCode.en) {
    const records = await this.getAll(lang);

    const result: Record<ECategoryType, any> = {
      [ECategoryType.Commercial]: records.filter(
        (r) => r.type === ECategoryType.Commercial,
      ),
      [ECategoryType.Residential]: records.filter(
        (r) => r.type === ECategoryType.Residential,
      ),
    };

    return result;
  }

  async update(id: string, body: UpdateCategoryDto) {
    const item = await this.categoryRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Category is not found');
    }
    await this.redisService.deletePattern('category@*');

    const langCode = body.lang ?? ELangCode.en;

    if (body.slug && body.slug !== item.slug) {
      const existed = await this.categoryRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== item.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    const data: Partial<Category> = {
      ...item,
      ...body,
      name: {
        ...item.name,
        [langCode]: body.name ?? item.name?.[langCode],
      },
      shortname: {
        ...item.shortname,
        [langCode]: body.shortname ?? item.shortname?.[langCode],
      },
    };

    return await this.categoryRepo.save(data);
  }

  async delete(id: string) {
    const item = await this.categoryRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Category is not found');
    }
    await this.redisService.deletePattern('category@*');
    return await this.categoryRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  async findByName(name: string) {
    return await this.redisService.cacheWrapper(
      `category@name:${name}`,
      async () => {
        return await this.categoryRepo.findOne({
          where: {
            name: {
              en: name,
            },
          },
        });
      },
    );
  }

  formatResponse(item: Category, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      shortname: getLangValue(lang, item.shortname, fallback),
    };
  }
}
