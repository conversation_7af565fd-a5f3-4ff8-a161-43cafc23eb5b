import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { AnalyticService } from 'src/analytic/analytic.service';
import { Asset } from 'src/asset/entities/asset.entity';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { ELangCode } from 'src/core/enums/lang.enum';
import { EOrderType } from 'src/core/enums/sort.enum';
import { beautifyObject } from 'src/core/utils/common.util';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { Location } from 'src/location/entities/location.entity';
import { LocationService } from 'src/location/location.service';
import { Project } from 'src/project/entities/project.entity';
import { ProjectService } from 'src/project/project.service';
import { RedisService } from 'src/redis/redis.service';
import { Section } from 'src/section/entities/section.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { ELayout, LayoutItemLength } from 'src/section/enums/layout.enum';
import { SectionService } from 'src/section/section.service';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { User } from 'src/users/entities/user.entity';
import { Brackets, Repository } from 'typeorm';

import { ProjectFloorPlanQueryDto } from './dto/project-floor-plan-query.dto';
import { AutoCompleteSearchQueryDto } from './dto/property-auto-complete-search-query.dto';
import { PropertyCompareDto } from './dto/property-compare.dto';
import { PropertyListingDto } from './dto/property-listing.dto';
import { PropertySectionQuery } from './dto/property-section-query.dto';
import { IRawUnitTypeDetail } from './interfaces/unit-type.interface';
import { QueryLocationDto } from 'src/location/dto/query-location.dto';
import { UnitTransactionService } from 'src/unit-transaction/unit-transaction.service';
import {
  EProjectProiceListSortBy,
  QueryGetProjectPriceList,
} from './dto/price-list.query.dto';
import { Unit } from 'src/unit/entities/unit.entity';
import { EUnitStatus } from 'src/unit/enums/unit.enum';
import { EAssetRelation } from 'src/asset/enums/asset.enum';

@Injectable()
export class PropertyService {
  constructor(
    @InjectRepository(UserProject)
    private readonly repo: Repository<UserProject>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly projectService: ProjectService,
    private readonly locationService: LocationService,
    private readonly unitTypeService: UnitTypeService,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    @InjectRepository(FloorPlan)
    private readonly floorPlanRepo: Repository<FloorPlan>,
    @InjectRepository(Section)
    private readonly sectionRepo: Repository<Section>,
    @InjectRepository(SectionProject)
    private readonly sectionProjectRepo: Repository<SectionProject>,
    @InjectRepository(UnitTransaction)
    private readonly unitTransactionRepo: Repository<UnitTransaction>,

    private readonly sectionService: SectionService,
    private readonly redisService: RedisService,
    private readonly analyticService: AnalyticService,

    private readonly unitTransactionService: UnitTransactionService,

    @InjectRepository(Unit)
    private readonly unitRepo: Repository<Unit>,
  ) {}

  async listing(domain: string | undefined, query: PropertyListingDto) {
    let user: User;
    if (domain) {
      user = await this.getUserDomain(domain);
    }

    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.repo
      .createQueryBuilder('userProject')
      .leftJoin('userProject.user', 'user')
      .innerJoinAndSelect('userProject.project', 'project')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoinAndSelect('project.developer', 'developer')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .groupBy('userProject.id')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .addGroupBy('developer.id')
      .addGroupBy('location.id')
      .orderBy('userProject.weight', 'DESC')
      .addOrderBy('name', 'ASC')
      // .where('user.id = :userId', { userId: user.id })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (!domain) {
      queryBuilder
        .addSelect('floorPlans')
        .addSelect('unitType')
        .addGroupBy('floorPlans.id')
        .addGroupBy('unitType.id');
    }

    if (user) {
      queryBuilder.andWhere('user.id = :userId', { userId: user.id });
    }

    if (query.minPrice) {
      queryBuilder.andWhere('floorPlans.availableUnits > 0');
      queryBuilder.andWhere('floorPlans.minPrice >= :minPrice', {
        minPrice: query.minPrice,
      });
    }

    if (query.maxPrice) {
      queryBuilder.andWhere('floorPlans.availableUnits > 0');
      queryBuilder.andWhere('floorPlans.maxPrice <= :maxPrice', {
        maxPrice: query.maxPrice,
      });
    }

    if (query.name) {
      queryBuilder.andWhere(
        `regexp_replace(${createMultilingualSelect('project', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :name`,
        {
          name: `%${query.name.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%`,
        },
      );
    }

    if (query.location) {
      queryBuilder.andWhere('location.slug = :location', {
        location: query.location,
      });
    }

    if (query.tenure) {
      queryBuilder.andWhere('tenure = :tenure', {
        tenure: query.tenure,
      });
    }

    if (query.unitType) {
      queryBuilder.andWhere('unitType.slug IN (:...unitType)', {
        unitType: Array.isArray(query.unitType)
          ? query.unitType
          : [query.unitType],
      });
    }

    if (query.availableUnits) {
      queryBuilder.andWhere('floorPlans.availableUnits >= :availableUnits', {
        availableUnits: query.availableUnits,
      });
    }

    if (query.unitMinSize) {
      queryBuilder.andWhere('floorPlans.area >= :minArea', {
        minArea: query.unitMinSize,
      });
    }

    if (query.unitMaxSize) {
      queryBuilder.andWhere('floorPlans.area <= :maxArea', {
        maxArea: query.unitMaxSize,
      });
    }

    if (query.maxUnitPSF) {
      queryBuilder.andWhere(
        '(CASE WHEN floorPlans.area > 0 THEN floorPlans.maxPrice / floorPlans.area ELSE 0 END) <= :maxPSF',
        {
          maxPSF: query.maxUnitPSF,
        },
      );
    }

    if (query.minUnitPSF) {
      queryBuilder.andWhere(
        '(CASE WHEN floorPlans.area > 0 THEN floorPlans.minPrice / floorPlans.area ELSE 0 END) <= :minPSF',
        {
          minPSF: query.maxUnitPSF,
        },
      );
    }

    if (query.category) {
      queryBuilder.andWhere('category.slug IN (:...category)', {
        category: Array.isArray(query.category)
          ? query.category
          : [query.category],
      });
    }

    if (query.marketSegment) {
      queryBuilder.andWhere('project.marketSegment IN (:...marketSegment)', {
        marketSegment: Array.isArray(query.marketSegment)
          ? query.marketSegment
          : [query.marketSegment],
      });
    }

    if (query.status) {
      queryBuilder.andWhere('project.status IN (:...status)', {
        status: Array.isArray(query.status) ? query.status : [query.status],
      });
    }

    if (typeof query.featured === 'boolean') {
      queryBuilder.andWhere('userProject.featured = :featured', {
        featured: query.featured,
      });
    }

    if (typeof query.promotion === 'boolean') {
      queryBuilder.andWhere('userProject.promotion = :promotion', {
        promotion: query.promotion,
      });
    }

    if (typeof query.upcomingLaunch === 'boolean') {
      // queryBuilder.andWhere('project.upcomingLaunch = :upcomingLaunch', {
      //   upcomingLaunch: query.upcomingLaunch,
      // });
      queryBuilder.andWhere(
        '(project.upcomingLaunch = :upcomingLaunch OR userProject.upcomingLaunch = :upcomingLaunch)',
        {
          upcomingLaunch: query.upcomingLaunch,
        },
      );
    }

    if (query.topSoon) {
      const nextThreeMonths = moment().add(3, 'months').endOf('month').toDate();
      const pastOneYearAgo = moment()
        .startOf('month')
        .subtract(1, 'year')
        .toDate();
      queryBuilder
        .andWhere(
          '(project.expectedTop BETWEEN :pastOneYearAgo AND :nextThreeMonths)',
          {
            pastOneYearAgo,
            nextThreeMonths,
          },
        )
        .orderBy('project.expectedTop', 'DESC');
    }

    if (query.top) {
      if (!isNaN(Number(query.top))) {
        const year = parseInt(query.top);
        const topStartDate = moment().year(year).startOf('year').toDate();
        const topEndDate = moment().year(year).endOf('year').toDate();

        queryBuilder.andWhere(
          'project.expectedTop BETWEEN :topStartDate AND :topEndDate',
          {
            topStartDate,
            topEndDate,
          },
        );
      }
    }

    if (query.whatIsNextStatus) {
      queryBuilder.andWhere('project.whatIsNextStatus = :whatIsNextStatus', {
        whatIsNextStatus: query.whatIsNextStatus,
      });
    }

    if (query.whatIsNextStatusOptions) {
      queryBuilder.andWhere(
        `"project"."whatIsNextStatusOptions"::jsonb = :options::jsonb`,
        {
          options: JSON.stringify(query.whatIsNextStatusOptions),
        },
      );
    }

    if (query.developerId) {
      queryBuilder.andWhere('project.developerId = :developerId', {
        developerId: query.developerId,
      });
    }

    const [result, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    let data = await this.processProjectListing(result.entities, lang, {
      minPrice: query.minPrice,
      maxPrice: query.maxPrice,
    });

    if (query.minPrice || query.maxPrice) {
      data = data.map((item) => {
        const { unitTypes } = item;

        const matchingUnits = unitTypes.filter((unit) => {
          const minPriceInRange =
            query.minPrice !== undefined && unit.minPrice !== null
              ? unit.minPrice >= query.minPrice &&
                unit.minPrice <= query.maxPrice
              : false;

          const maxPriceInRange =
            query.maxPrice !== undefined && unit.maxPrice !== null
              ? unit.maxPrice >= query.minPrice &&
                unit.maxPrice <= query.maxPrice
              : false;

          return minPriceInRange || maxPriceInRange;
        });

        const updatedUnitTypes = unitTypes.map((unit) => ({
          ...unit,
          active: false,
        }));

        if (matchingUnits.length > 0) {
          const firstMatchingUnit = matchingUnits[0];
          const index = unitTypes.findIndex(
            (unit) => unit.id === firstMatchingUnit.id,
          );
          if (index !== -1) {
            updatedUnitTypes[index].active = true;
          }
        } else if (updatedUnitTypes.length > 0) {
          updatedUnitTypes[0].active = true;
        }

        return {
          ...item,
          unitTypes: updatedUnitTypes,
        };
      });
    }

    return createPaginationResponse(data, total, pagination);
  }

  async getAll(query: PropertyListingDto) {
    return await this.listing(undefined, query);
  }

  async autoCompleteSearch(domain: string, query: AutoCompleteSearchQueryDto) {
    const user = await this.getUserDomain(domain);
    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.repo
      .createQueryBuilder('userProject')
      .leftJoin('userProject.user', 'user')
      .leftJoin('userProject.project', 'project')
      .leftJoin('project.location', 'location')
      .select('project.slug', 'projectSlug')
      .addSelect(
        createMultilingualSelect('project', 'name', lang),
        'projectName',
      )
      .addSelect(
        createMultilingualSelect('location', 'name', lang),
        'locationName',
      )
      .addSelect('location.slug', 'locationSlug')
      .where('user.id = :userId', { userId: user.id })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.name) {
      queryBuilder.andWhere(
        `regexp_replace(${createMultilingualSelect('project', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :name`,
        {
          name: `%${query.name.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%`,
        },
      );
    }

    const [result, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(result, total, pagination);
  }

  async getFeatured(domain: string, query: PaginationQueryDto) {
    const user = await this.getUserDomain(domain);
    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.repo
      .createQueryBuilder('userProject')
      .innerJoinAndSelect('userProject.project', 'project')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .groupBy('userProject.id')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .addGroupBy('location.id')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere(
        '(userProject.featured = :featured OR userProject.promotion = :promotion OR project.promotion = :promotion)',
        { featured: true, promotion: true },
      )
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .orderBy('userProject.weight', 'DESC')
      .addOrderBy('name', 'ASC')
      .take(pagination.limit)
      .skip(pagination.offset);

    const [result, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    const data = await this.processProjectListing(result.entities, lang);

    return createPaginationResponse(data, total, pagination);
  }

  async getAllLocations(domain: string, query: LanguageQueryDto) {
    const lang = query.lang || ELangCode.en;
    const user = await this.getUserDomain(domain);

    const queryBuilder = this.locationRepo
      .createQueryBuilder('location')
      .orderBy(createMultilingualSelect('location', 'name', lang), 'ASC');

    const locations = await queryBuilder.getMany();
    const res = await Promise.all(
      locations.map(async (location) => {
        const projects = await this.getByLocation(location, user.id, lang);
        if (!projects.length) {
          return undefined;
        }
        return {
          ...this.locationService.formatResponse(location, lang),
          projects,
        };
      }),
    );

    return res.filter(Boolean);
  }

  async getByLocation(location: Location, userId: string, lang: ELangCode) {
    const queryBuilder = this.repo
      .createQueryBuilder('userProject')
      .innerJoin('userProject.project', 'project')
      .select('project.id', 'id')
      .addSelect('project.slug', 'slug')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .orderBy('project.promotion', 'ASC', 'NULLS LAST')
      .addOrderBy('userProject.promotion', 'ASC', 'NULLS LAST')
      .addOrderBy('userProject.featured', 'ASC', 'NULLS LAST')
      .addOrderBy('name', 'ASC')
      .where('userProject.userId = :userId', { userId })
      .andWhere('project.locationId = :locationId', {
        locationId: location.id,
      })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .take(5);

    const data = await queryBuilder.getRawMany();

    return data;
  }

  async getSections(domain: string, query: PropertySectionQuery) {
    const user = await this.getUserDomain(domain);
    const lang = query.lang || ELangCode.en;
    const pagination = getPaginationOption(query);

    const locationQuery: QueryLocationDto = {
      lang: ELangCode.en,
      sort: {},
    };

    const isDynamicLayoutAdvanceMode =
      await this.sectionService.checkIsDynamicLayoutAdvanceMode(user.id);
    const locationList = await this.locationService.getAll(locationQuery);
    const locationIds = locationList.map((location) => location.id);

    let queryBuilder;

    if (isDynamicLayoutAdvanceMode) {
      queryBuilder = this.sectionRepo
        .createQueryBuilder('section')
        .leftJoin('section.config', 'config')
        .leftJoinAndSelect('section.location', 'location')
        .leftJoinAndSelect('section.photo', 'photo')
        .where('config.userId = :userId', { userId: user.id })
        .andWhere('section.visible = :visible', { visible: true })
        .andWhere(
          new Brackets((qb) => {
            qb.where('location.id IS NULL').orWhere(
              'location.id IN (:...locationIds)',
              { locationIds },
            );
          }),
        )
        .orderBy('section.weight', 'ASC')
        .addOrderBy('section.weightTimestamp', 'DESC')
        .take(pagination.limit)
        .skip(pagination.offset);
    } else {
      queryBuilder = this.sectionRepo
        .createQueryBuilder('section')
        .leftJoin('section.config', 'config')
        .leftJoinAndSelect('section.location', 'location')
        .leftJoinAndSelect('section.photo', 'photo')
        .where('config.userId = :userId', { userId: user.id })
        .andWhere('section.visible = :visible', { visible: true })
        .andWhere('location.id IS NOT NULL')
        .orderBy('location.name', 'ASC')
        .take(pagination.limit)
        .skip(pagination.offset);
    }

    const [sections, total] = await queryBuilder.getManyAndCount();
    const data = await Promise.all(
      sections.map(async (section) => {
        if (
          [
            ELayout.SectionHTMLContent,
            ELayout.SectionPromotionBanner,
            ELayout.SectionCTA,
            ELayout.SectionFeaturedLaunches,
          ].includes(section.layout)
        ) {
          return this.sectionService.formatResponse(section, lang);
        }
        const projects = await this.getSectionProjects(
          section,
          lang,
          query,
          user,
        );

        return {
          ...this.sectionService.formatResponse(section, lang),
          projects,
        };
      }),
    );

    return createPaginationResponse(data, total, pagination);
  }

  async getSectionProjects(
    section: Section,
    lang: ELangCode,
    query: PropertySectionQuery,
    user: User,
  ): Promise<any> {
    const limit = LayoutItemLength[section.layout];

    const queryBuilder = this.sectionProjectRepo
      .createQueryBuilder('sectionProject')
      .distinctOn(['sectionProject.projectId'])
      .innerJoinAndSelect('sectionProject.project', 'project')
      .innerJoin(
        UserProject,
        'userProject',
        'userProject.userId = :userId AND sectionProject.projectId = userProject.projectId',
        { userId: user.id },
      )
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .groupBy('sectionProject.id')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .addGroupBy('location.id')
      .orderBy('sectionProject.projectId', 'DESC')
      .where('sectionProject.sectionId = :sectionId', {
        sectionId: section.id,
      });
    // .andWhere('userProject.isShow = :isShow', { isShow: true });

    // if (limit > 0) {
    //   queryBuilder.take(limit);
    // }

    // if (query.location) {
    //   queryBuilder.andWhere('location.slug IN (:...location)', {
    //     location: query.location,
    //   });
    // }
    // if (query.minPrice) {
    //   queryBuilder.andWhere('floorPlans.maxPrice >= :minPrice', {
    //     minPrice: query.minPrice,
    //   });
    // }
    // if (query.maxPrice) {
    //   queryBuilder.andWhere('floorPlans.minPrice <= :maxPrice', {
    //     maxPrice: query.maxPrice,
    //   });
    // }
    // if (query.unitType) {
    //   queryBuilder.andWhere('unitType.slug IN (:...unitType)', {
    //     unitType: Array.isArray(query.unitType)
    //       ? query.unitType
    //       : [query.unitType],
    //   });
    // }

    const result = await queryBuilder.getRawAndEntities();

    const projectsEntities = result.entities;

    const sortedProjects = projectsEntities.sort((a, b) => {
      if (a.weight !== b.weight) {
        return a.weight - b.weight;
      }
      return (
        new Date(a.weightTimestamp).getTime() -
        new Date(b.weightTimestamp).getTime()
      );
    });

    const uniqueProjects = sortedProjects.filter(
      (project, index, self) =>
        index === self.findIndex((t) => t.project.id === project.project.id),
    );

    const data = await this.processProjectListing(uniqueProjects, lang);

    return data;
  }

  async getUserDomain(domain: string) {
    const user = await this.userRepo.findOne({
      where: {
        config: {
          domains: { name: domain },
        },
      },
      relations: {
        config: true,
      },
    });
    if (!user) {
      throw new NotFoundException('Domain is not found');
    }
    return user;
  }

  formatResponse = (
    item: UserProject | SectionProject,
    lang: ELangCode,
    fallback = ELangCode.en,
  ) => {
    return {
      ...item,
      project: this.projectService.formatResponse(item.project, lang, fallback),
    };
  };

  async compare(query: PropertyCompareDto) {
    const lang = query.lang ?? ELangCode.en;
    const slugs = Array.isArray(query.projectSlugs)
      ? query.projectSlugs
      : [query.projectSlugs];

    const result = await this.projectRepo
      .createQueryBuilder('project')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.developer', 'developer')
      .leftJoinAndSelect('project.photo', 'photo')
      .addSelect('SUM(floorPlans.totalUnits)', 'totalUnits')
      .addSelect('SUM(floorPlans.availableUnits)', 'availableUnits')
      .addSelect('GREATEST(MIN(floorPlans.minPrice), 0)', 'minPrice')
      .addSelect('(MAX(floorPlans.maxPrice))', 'maxPrice')
      .addSelect(
        'GREATEST(MIN(CASE WHEN floorPlans.area > 0 THEN floorPlans.minPrice / floorPlans.area ELSE 0 END), 0)',
        'minPSF',
      )
      .addSelect(
        'MAX(CASE WHEN floorPlans.area > 0 THEN floorPlans.maxPrice / floorPlans.area ELSE 0 END)',
        'maxPSF',
      )
      .where('project.slug IN (:...slugs)', {
        slugs,
      })
      .groupBy('project.id')
      .addGroupBy('location.id')
      .addGroupBy('developer.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .getRawAndEntities();

    return result.entities.map((item) => {
      const project = this.projectService.formatResponse(item, lang);
      const raw = result.raw.find(
        (rawItem) => project.id === rawItem.project_id,
      );

      let minPrice = 0;
      let maxPrice = 0;
      let minPSF = 0;
      let maxPSF = 0;
      let totalUnits = 0;
      let availableUnits = 0;

      if (raw.minPrice) {
        minPrice = +raw.minPrice;
      }
      if (raw.maxPrice) {
        maxPrice = +raw.maxPrice;
      }
      if (raw.minPSF) {
        minPSF = +raw.minPSF;
      }
      if (raw.maxPSF) {
        maxPSF = +raw.maxPSF;
      }

      totalUnits = +raw.totalUnits;
      availableUnits = +raw.availableUnits;

      project['totalUnits'] = totalUnits;
      project['availableUnits'] = availableUnits;
      project['minPrice'] = minPrice;
      project['maxPrice'] = maxPrice;
      project['minPSF'] = minPSF;
      project['maxPSF'] = maxPSF;

      this.analyticService.incrementProjectViewCount(project.id);

      return project;
    });
  }

  async detail(
    domain: string | undefined,
    slug: string,
    query: LanguageQueryDto,
  ) {
    let user: User;
    if (domain) {
      user = await this.getUserDomain(domain);
    }

    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.projectRepo
      .createQueryBuilder('project')
      .innerJoin('project.userProjects', 'userProjects')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.projectAmenities', 'projectAmenities')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.developer', 'developer')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoinAndSelect('project.seoImage', 'seoImage')
      .leftJoinAndSelect('project.virtualTours', 'virtualTours')
      .leftJoinAndSelect('virtualTours.thumbnail', 'thumbnail')
      .leftJoinAndMapMany(
        'project.medias',
        Asset,
        'medias',
        'medias.relationId = project.id AND medias.relation = :relation',
        { relation: EAssetRelation.Project },
      )
      .leftJoinAndSelect('project.sitePlans', 'sitePlans')
      .leftJoinAndSelect('project.mobileLogo', 'mobileLogo')
      .leftJoinAndSelect('project.desktopLogo', 'desktopLogo')
      .leftJoinAndSelect('project.siteplanImages', 'siteplanImages')
      .leftJoinAndSelect('project.brochureAsset', 'brochureAsset')
      .where('project.slug = :slug', { slug })
      // .andWhere('userProjects.userId = :userId', { userId: user.id })
      .andWhere('userProjects.isShow = :isShow', { isShow: true });

    if (user) {
      queryBuilder.andWhere('userProjects.userId = :userId', {
        userId: user.id,
      });
    }

    let project = await queryBuilder.getOne().then((value) => {
      if (value) {
        return this.projectService.formatResponse(value, lang);
      }
    });

    if (!project) {
      // If user project not found => User hasn't added this project to their project inventory
      // => Throw error from now

      throw new NotFoundException('Project not found');

      // project = await this.projectRepo
      //   .createQueryBuilder('project')
      //   .innerJoin('project.userProjects', 'userProjects')
      //   .leftJoinAndSelect('project.category', 'category')
      //   .leftJoinAndSelect('project.projectAmenities', 'projectAmenities')
      //   .leftJoinAndSelect('project.location', 'location')
      //   .leftJoinAndSelect('project.developer', 'developer')
      //   .leftJoinAndSelect('project.photo', 'photo')
      //   .leftJoinAndSelect('project.seoImage', 'seoImage')
      //   .leftJoinAndSelect('project.virtualTours', 'virtualTours')
      //   .leftJoinAndSelect('virtualTours.thumbnail', 'thumbnail')
      //   .leftJoinAndMapMany(
      //     'project.medias',
      //     Asset,
      //     'medias',
      //     'medias.relationId = project.id AND medias.relation = :relation',
      //     { relation: EAssetRelation.Project },
      //   )
      //   .leftJoinAndSelect('project.sitePlans', 'sitePlans')
      //   .leftJoinAndSelect('project.mobileLogo', 'mobileLogo')
      //   .leftJoinAndSelect('project.desktopLogo', 'desktopLogo')
      //   .leftJoinAndSelect('project.siteplanImages', 'siteplanImages')
      //   .where('project.slug = :slug', { slug })
      //   .andWhere('userProjects.isShow = :isShow', { isShow: true })
      //   .getOne()
      //   .then((value) => {
      //     if (value) {
      //       return this.projectService.formatResponse(value, lang);
      //     }
      //   });
    }

    const unitTypes = await this.getUnitTypesDetail([project.id], lang);
    const countUnitsStatistic = await this.projectService.countUnitsStatistic(
      project.id,
    );

    project['totalUnits'] = countUnitsStatistic.totalUnitsCount;
    project['availableUnits'] = countUnitsStatistic.availableUnitsCount;
    project['unitTypes'] = unitTypes;

    this.analyticService
      .incrementProjectViewCount(project.id)
      .catch(console.log);

    return project;
  }

  async countFloorPlans(domain: string, slug: string, query: LanguageQueryDto) {
    const user = await this.getUserDomain(domain);
    let userProject = await this.repo
      .createQueryBuilder('userProject')
      .innerJoin('userProject.project', 'project')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere('project.slug = :slug', { slug })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .getOne();
    if (!userProject) {
      userProject = await this.repo
        .createQueryBuilder('userProject')
        .innerJoin('userProject.project', 'project')
        .andWhere('project.slug = :slug', { slug })
        .andWhere('userProject.isShow = :isShow', { isShow: true })
        .getOne();
    }

    const lang = query.lang || ELangCode.en;
    const queryBuilder = this.floorPlanRepo
      .createQueryBuilder('floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .select('unitType.id', 'id')
      .addSelect('unitType.slug', 'slug')
      .addSelect(
        `${createMultilingualSelect('unitType', 'title', lang, ELangCode.en)}`,
        'title',
      )
      .addSelect('COUNT(floorPlans.id)', 'floorPlanCount')
      .groupBy('unitType.id')
      .where('floorPlans.projectId = :projectId', {
        projectId: userProject.projectId,
      });
    const data = await queryBuilder.getRawMany();

    return data.map((item) => ({
      ...item,
      floorPlanCount: +item.floorPlanCount,
    }));
  }

  async getFloorPlans(
    domain: string,
    slug: string,
    query: ProjectFloorPlanQueryDto,
  ) {
    const sortIn = query.sort || EOrderType.ASC;
    const user = await this.getUserDomain(domain);
    let userProject = await this.repo
      .createQueryBuilder('userProject')
      .innerJoin('userProject.project', 'project')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere('project.slug = :slug', { slug })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .getOne();
    if (!userProject) {
      userProject = await this.repo
        .createQueryBuilder('userProject')
        .innerJoin('userProject.project', 'project')
        .andWhere('project.slug = :slug', { slug })
        .andWhere('userProject.isShow = :isShow', { isShow: true })
        .getOne();
    }

    const lang = query.lang || ELangCode.en;
    const pagination = getPaginationOption(query);
    const queryBuilder = this.floorPlanRepo
      .createQueryBuilder('floorPlans')
      .leftJoin('floorPlans.project', 'project')
      .innerJoin('project.userProjects', 'userProjects')
      .leftJoin('floorPlans.unitType', 'unitType')
      .leftJoin('floorPlans.photo', 'photo')
      .select('unitType.id', 'unitType_id')
      .addSelect('unitType.slug', 'unitType_slug')
      .addSelect(
        `${createMultilingualSelect('unitType', 'title', lang, ELangCode.en)}`,
        'unitType_title',
      )
      .addSelect('floorPlans.id', 'id')
      .addSelect('floorPlans.area', 'area')
      .addSelect(
        `${createMultilingualSelect('floorPlans', 'name', lang, ELangCode.en)}`,
        'name',
      )
      .addSelect('photo.id', 'photo_id')
      .addSelect('photo.urls', 'photo_urls')
      .addSelect('photo.type', 'photo_type')
      .addSelect('floorPlans.totalUnits', 'totalUnits')
      .addSelect('floorPlans.availableUnits', 'availableUnits')
      .addSelect('GREATEST(MIN(floorPlans.minPrice), 0)', 'minPrice')
      .addSelect('(MAX(floorPlans.maxPrice))', 'maxPrice')
      .addSelect(
        'GREATEST(MIN(CASE WHEN floorPlans.area > 0 THEN floorPlans.minPrice / floorPlans.area ELSE 0 END), 0)',
        'minPSF',
      )
      .addSelect(
        'MAX(CASE WHEN floorPlans.area > 0 THEN floorPlans.maxPrice / floorPlans.area ELSE 0 END)',
        'maxPSF',
      )
      .addGroupBy('unitType.id')
      .addGroupBy('photo.id')
      .addGroupBy('floorPlans.id')
      .where('project.slug = :slug', { slug })
      .orderBy('unitType.title', sortIn)
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.unitTypeIds) {
      queryBuilder.andWhere('floorPlans.unitTypeId IN (:...unitTypeIds)', {
        unitTypeIds: Array.isArray(query.unitTypeIds)
          ? query.unitTypeIds
          : [query.unitTypeIds],
      });
    }

    const [result, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(
      result.map((item) => beautifyObject(item, ['unitType', 'photo'])),
      total,
      pagination,
    );
  }

  async getAllProjectSlug(domain: string) {
    const user = await this.getUserDomain(domain);

    const data = await this.repo
      .createQueryBuilder('userProject')
      .leftJoin('userProject.project', 'project')
      .select('project.slug', 'slug')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere('project.slug IS NOT NULL')
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .getRawMany();

    return data.map((i) => i.slug);
  }

  async getUnitTransactions(slug: string, domain: string) {
    const user = await this.getUserDomain(domain);
    let userProject = await this.repo
      .createQueryBuilder('userProject')
      .innerJoin('userProject.project', 'project')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .andWhere('project.slug = :slug', { slug })
      .getOne();

    if (!userProject) {
      userProject = await this.repo
        .createQueryBuilder('userProject')
        .innerJoin('userProject.project', 'project')
        .andWhere('project.slug = :slug', { slug })
        .andWhere('userProject.isShow = :isShow', { isShow: true })
        .getOne();
    }

    const now = new Date();
    const oneMonthAgo = moment().subtract(1, 'month').startOf('day').toDate();
    const threeMonthsAgo = moment()
      .subtract(3, 'months')
      .startOf('day')
      .toDate();
    const sixMonthsAgo = moment().subtract(6, 'months').startOf('day').toDate();
    const twelveMonthsAgo = moment()
      .subtract(1, 'year')
      .startOf('day')
      .toDate();
    const twoYearsAgo = moment().subtract(2, 'years').startOf('day').toDate();

    const listingData = await this.unitTransactionService.listingCombinedRaw({
      projectSlug: slug,
    });

    listingData.items.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );

    const countAll = listingData.items.length;
    const countLast1Month = listingData.items.filter(
      (item) => new Date(item.timestamp) >= oneMonthAgo,
    ).length;
    const countLast3Months = listingData.items.filter(
      (item) => new Date(item.timestamp) >= threeMonthsAgo,
    ).length;
    const countLast6Months = listingData.items.filter(
      (item) => new Date(item.timestamp) >= sixMonthsAgo,
    ).length;
    const countLast12Months = listingData.items.filter(
      (item) => new Date(item.timestamp) >= twelveMonthsAgo,
    ).length;
    const countLast2Years = listingData.items.filter(
      (item) => new Date(item.timestamp) >= twoYearsAgo,
    ).length;

    const result = [
      {
        count: countAll,
        label: 'All transactions',
        fromDate: null,
        toDate: now,
      },
      {
        count: countLast1Month,
        label: 'Last 1 month',
        fromDate: oneMonthAgo,
        toDate: now,
      },
      {
        count: countLast3Months,
        label: 'Last 3 months',
        fromDate: threeMonthsAgo,
        toDate: now,
      },
      {
        count: countLast6Months,
        label: 'Last 6 months',
        fromDate: sixMonthsAgo,
        toDate: now,
      },
      {
        count: countLast12Months,
        label: 'Last 12 months',
        fromDate: twelveMonthsAgo,
        toDate: now,
      },
      {
        count: countLast2Years,
        label: 'Last 2 years',
        fromDate: twoYearsAgo,
        toDate: now,
      },
    ];

    return result;
  }

  async getCuratedProject(domain: string, slug: string) {
    const user = await this.getUserDomain(domain);
    const lang = ELangCode.en;

    let currentProject = await this.repo
      .createQueryBuilder('userProject')
      .innerJoinAndSelect('userProject.project', 'project')
      .where('project.slug = :slug', { slug })
      .andWhere('userProject.userId = :userId', { userId: user.id })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .getOne();

    if (!currentProject) {
      currentProject = await this.repo
        .createQueryBuilder('userProject')
        .innerJoinAndSelect('userProject.project', 'project')
        .andWhere('project.slug = :slug', { slug })
        .andWhere('userProject.isShow = :isShow', { isShow: true })
        .getOne();
    }

    const locationId = currentProject.project.locationId;
    const queryBuilder = this.repo
      .createQueryBuilder('userProject')
      .leftJoin('userProject.user', 'user')
      .innerJoinAndSelect('userProject.project', 'project')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .groupBy('userProject.id')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .addGroupBy('location.id')
      .where('user.id = :userId', { userId: user.id })
      .andWhere('project.id != :projectId', {
        projectId: currentProject.projectId,
      })
      .orderBy('userProject.weight', 'DESC')
      .addOrderBy('name', 'ASC');

    const theSameLocationQueryBuilder = queryBuilder
      .clone()
      .andWhere('project.locationId = :locationId', { locationId })
      .take(7);

    const theSameLocationResult =
      await theSameLocationQueryBuilder.getRawAndEntities();

    const theSameLocationProjects = await this.processProjectListing(
      theSameLocationResult.entities,
      lang,
    );

    if (theSameLocationProjects.length) {
      queryBuilder.andWhere('project.id NOT IN (:...projectIds)', {
        projectIds: theSameLocationProjects.map((v) => v.id),
      });
    }

    const featuredResult = await this.repo
      .createQueryBuilder('userProject')
      .innerJoinAndSelect('userProject.project', 'project')
      .leftJoinAndSelect('project.category', 'category')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin('floorPlans.unitType', 'unitType')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .addSelect('RANDOM()', 'random')
      .groupBy('userProject.id')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('photo.id')
      .addGroupBy('location.id')
      .where('userProject.userId = :userId', { userId: user.id })
      .andWhere(
        '(userProject.featured = :featured OR userProject.promotion = :promotion OR project.promotion = :promotion)',
        { featured: true, promotion: true },
      )
      .andWhere('project.id != :projectId', {
        projectId: currentProject.projectId,
      })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .take(8 - theSameLocationProjects.length)
      .orderBy('random')
      .getRawAndEntities();

    let featuredProjects = await this.processProjectListing(
      featuredResult.entities,
      lang,
    );

    featuredProjects = featuredProjects.sort(() => Math.random() - 0.5);

    return [...featuredProjects, ...theSameLocationProjects];
  }

  async processProjectListing(
    entities: Array<UserProject | SectionProject>,
    lang: ELangCode,
    query: Pick<PropertyListingDto, 'minPrice' | 'maxPrice'> = {},
  ) {
    const unitTypes = entities.length
      ? await this.getUnitTypesDetail(
          entities.map((i) => i.projectId),
          lang,
          query,
        )
      : [];

    return entities.map((item) => {
      const project = this.formatResponse(item, lang).project;

      let availableUnits = 0;
      let totalUnits = 0;

      const projectUnitTypes = unitTypes.filter(
        (unit) => unit.projectId === project.id,
      );

      projectUnitTypes.forEach((unit) => {
        availableUnits += unit.availableUnits || 0;
        totalUnits += unit.totalUnits || 0;
      });

      project['totalUnits'] = totalUnits;
      project['availableUnits'] = availableUnits;
      project['unitTypes'] = projectUnitTypes;

      return project;
    });
  }

  async getUnitTypesDetail(
    projectIds: string[],
    lang: ELangCode,
    query: Partial<Pick<PropertyListingDto, 'minPrice' | 'maxPrice'>> = {},
  ): Promise<Array<IRawUnitTypeDetail>> {
    const queryBuilder = this.floorPlanRepo
      .createQueryBuilder('floorPlans')
      .innerJoin('floorPlans.unitType', 'unitType')
      .select([
        'unitType.id AS id',
        'unitType.slug AS slug',
        `${createMultilingualSelect('unitType', 'title', lang)} AS title`,
      ])
      .addSelect('floorPlans.projectId', 'projectId')
      .addSelect('SUM(floorPlans.totalUnits)', 'totalUnits')
      .addSelect('SUM(floorPlans.availableUnits)', 'availableUnits')
      .addSelect(
        'MIN(floorPlans.minPrice) FILTER (WHERE floorPlans.availableUnits > 0)',
        'minPrice',
      )
      .addSelect(
        'MAX(floorPlans.maxPrice) FILTER (WHERE floorPlans.availableUnits > 0)',
        'maxPrice',
      )
      .addSelect('MAX(floorPlans.area)', 'maxArea')
      .addSelect('MIN(floorPlans.area)', 'minArea')
      .addSelect(
        'GREATEST(MIN(CASE WHEN floorPlans.area > 0 THEN floorPlans.minPrice / floorPlans.area ELSE 0 END), 0)',
        'minPSF',
      )
      .addSelect(
        'MAX(CASE WHEN floorPlans.area > 0 THEN floorPlans.maxPrice / floorPlans.area ELSE 0 END)',
        'maxPSF',
      )
      .where('floorPlans.projectId IN (:...projectIds)', { projectIds });

    if (query?.minPrice) {
      queryBuilder.andWhere('floorPlans.minPrice >= :minPrice', {
        minPrice: query.minPrice,
      });
    }

    if (query?.maxPrice) {
      queryBuilder.andWhere('floorPlans.maxPrice <= :maxPrice', {
        maxPrice: query.maxPrice,
      });
      queryBuilder.andWhere('floorPlans.availableUnits > 0'); // make sure only available units are considered
    }

    return await queryBuilder
      .groupBy('floorPlans.projectId')
      .addGroupBy('unitType.id')
      .orderBy('CASE WHEN unitType.bedRoomCount = 0 THEN 0 ELSE 1 END', 'DESC')
      .addOrderBy('unitType.bedRoomCount', 'ASC')
      .getRawMany()
      .then((records) => {
        if (records.length === 1 && !records[0].id) {
          return [];
        }
        return records.map((item) => ({
          ...item,
          totalUnits: +item.totalUnits,
          availableUnits: +item.availableUnits,
        }));
      });
  }

  async getProjectPriceList(
    domain: string,
    slug: string,
    query: QueryGetProjectPriceList,
  ) {
    const user = await this.getUserDomain(domain);
    const pagination = getPaginationOption(query);
    pagination.limit = pagination.limit || 50;
    pagination.page = pagination.page || 1;

    pagination.offset = (pagination.page - 1) * pagination.limit;
    const lang = query.lang ?? ELangCode.en;
    const orderBy = query.sortBy || EProjectProiceListSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    let currentProject = await this.repo
      .createQueryBuilder('userProject')
      .innerJoinAndSelect('userProject.project', 'project')
      .where('project.slug = :slug', { slug })
      .andWhere('userProject.userId = :userId', { userId: user.id })
      .andWhere('userProject.isShow = :isShow', { isShow: true })
      .getOne();

    if (!currentProject) {
      currentProject = await this.repo
        .createQueryBuilder('userProject')
        .innerJoinAndSelect('userProject.project', 'project')
        .andWhere('project.slug = :slug', { slug })
        .andWhere('userProject.isShow = :isShow', { isShow: true })
        .getOne();
    }

    const units = await this.unitRepo
      .createQueryBuilder('unit')
      .leftJoinAndSelect('unit.floorPlan', 'floorPlan')
      .leftJoinAndSelect('floorPlan.unitType', 'unitType')
      .select([
        'unit.status AS unit_status',
        'unit.name AS unit_name',
        'floorPlan.area AS floorPlan_area',
        'unit.price AS unit_price',
        'unitType.bedRoomCount AS unitType_bedRoomCount',
      ])
      .addSelect(
        'GREATEST(MIN(CASE WHEN floorPlan.area > 0 THEN floorPlan.minPrice / floorPlan.area ELSE 0 END), 0)',
        'minPSF',
      )
      .addSelect(
        'MAX(CASE WHEN floorPlan.area > 0 THEN floorPlan.maxPrice / floorPlan.area ELSE 0 END)',
        'maxPSF',
      )
      .addSelect(
        createMultilingualSelect('floorPlan', 'name', lang),
        'floorPlan_name',
      )
      .addSelect(
        createMultilingualSelect('unitType', 'title', lang),
        'unitType_title',
      )
      .where('unit.projectId = :projectId', {
        projectId: currentProject.project.id,
      })
      .groupBy('unit.status')
      .addGroupBy('unit.name')
      .addGroupBy('floorPlan.area')
      .addGroupBy('unit.price')
      .addGroupBy('unitType.bedRoomCount')
      .addGroupBy('floorPlan.name')
      .addGroupBy('unitType.title')
      .getRawMany();

    const unitTransactions = await this.unitTransactionRepo
      .createQueryBuilder('unitTransaction')
      .leftJoin('unitTransaction.unitType', 'unitType')
      .leftJoin('unitType.floorPlans', 'floorPlan')
      .select([
        'unitTransaction.status AS unit_status',
        'unitTransaction.unitName AS unit_name',
        'unitTransaction.projectId AS project_id',
        'floorPlan.area AS floorPlan_area',
        'floorPlan.minPrice AS unit_price',
        'unitType.bedRoomCount AS unitType_bedRoomCount',
        'GREATEST(MIN(CASE WHEN floorPlan.area > 0 THEN floorPlan.minPrice / floorPlan.area ELSE 0 END), 0) AS minPSF',
        'MAX(CASE WHEN floorPlan.area > 0 THEN floorPlan.maxPrice / floorPlan.area ELSE 0 END) AS maxPSF',
        createMultilingualSelect('floorPlan', 'name', lang) +
          ' AS floorPlan_name',
        createMultilingualSelect('unitType', 'title', lang) +
          ' AS unitType_title',
      ])
      .where('unitTransaction.projectId = :projectId', {
        projectId: currentProject.project.id,
      })
      .groupBy('unitTransaction.status')
      .addGroupBy('unitTransaction.unitName')
      .addGroupBy('unitTransaction.projectId')
      .addGroupBy('floorPlan.area')
      .addGroupBy('floorPlan.minPrice')
      .addGroupBy('unitType.bedRoomCount')
      .addGroupBy('floorPlan.name')
      .addGroupBy('unitType.title')
      .getRawMany();

    const mergedUnits = [...units, ...unitTransactions];

    const uniqueUnits = Array.from(
      new Map(mergedUnits.map((unit) => [unit.unit_name, unit])).values(),
    );

    uniqueUnits.sort((a, b) => {
      const compareA = a[orderBy] ?? '';
      const compareB = b[orderBy] ?? '';
      if (sorting === EOrderType.ASC) {
        return compareA > compareB ? 1 : -1;
      } else {
        return compareA < compareB ? 1 : -1;
      }
    });

    const paginatedUnits = uniqueUnits.slice(
      pagination.offset,
      pagination.offset + pagination.limit,
    );

    const camelCaseResult = paginatedUnits.map((row) => ({
      unitStatus: row.unit_status,
      unitName: row.unit_name,
      floorPlanArea: row.floorPlan_area ? +row.floorPlan_area : null,
      unitPrice: row.unit_price ? +row.unit_price : null,
      bedRoomCount: row.unitType_bedRoomCount
        ? +row.unitType_bedRoomCount
        : null,
      minPSF: row.minPSF ? +row.minPSF : null,
      maxPSF: row.maxPSF ? +row.maxPSF : null,
      floorPlanName: row.floorPlan_name ? row.floorPlan_name : null,
      unitTypeTitle: row.unitType_title ? row.unitType_title : null,
    }));

    return createPaginationResponse(
      camelCaseResult,
      uniqueUnits.length,
      pagination,
    );
  }
}
