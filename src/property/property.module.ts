import { forwardRef, Module } from '@nestjs/common';
import { PropertyController } from './property.controller';
import { PropertyService } from './property.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { ProjectModule } from 'src/project/project.module';
import { User } from 'src/users/entities/user.entity';
import { Project } from 'src/project/entities/project.entity';
import { UserConfigLocation } from 'src/user-config-location/entities/user-config-location.entity';
import { LocationModule } from 'src/location/location.module';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { Section } from 'src/section/entities/section.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { SectionModule } from 'src/section/section.module';
import { RedisModule } from 'src/redis/redis.module';
import { AnalyticModule } from 'src/analytic/analytic.module';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { Location } from 'src/location/entities/location.entity';
import { DeveloperModule } from 'src/developer/developer.module';
import { Unit } from 'src/unit/entities/unit.entity';
import { UnitTransactionModule } from 'src/unit-transaction/unit-transaction.module';
import { ApiKeyModule } from 'src/api-key/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProject,
      User,
      Project,
      UserConfigLocation,
      FloorPlan,
      Section,
      SectionProject,
      UnitTransaction,
      Location,
      Unit,
    ]),
    ProjectModule,
    LocationModule,
    UnitTypeModule,
    SectionModule,
    RedisModule,
    AnalyticModule,
    DeveloperModule,
    UnitTransactionModule,
    forwardRef(() => ApiKeyModule),
  ],
  controllers: [PropertyController],
  providers: [PropertyService],
  exports: [PropertyService],
})
export class PropertyModule {}
