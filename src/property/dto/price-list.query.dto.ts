import { IsEnum, IsNumber, IsOptional, Max, Min } from 'class-validator';
import { ELangCode } from 'src/core/enums/lang.enum';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EProjectProiceListSortBy {
  createdAt = 'createdAt',
  unitStatus = 'unitStatus',
  unitName = 'unitName',
  floorPlanArea = 'floorPlanArea',
  unitPrice = 'unitPrice',
  bedRoomCount = 'bedRoomCount',
  minPSF = 'minPSF',
  maxPSF = 'maxPSF',
  floorPlanName = 'floorPlanName',
  unitTypeTitle = 'unitTypeTitle',
}

export class QueryGetProjectPriceList {
  @IsEnum(ELangCode)
  @IsOptional()
  lang?: ELangCode;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EProjectProiceListSortBy)
  @IsOptional()
  sortBy?: EProjectProiceListSortBy;
}
