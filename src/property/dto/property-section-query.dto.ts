import { IsNumber, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class PropertySectionQuery extends PaginationQueryDto {
  @IsString({ each: true })
  @IsOptional()
  location?: string[];

  @IsString({ each: true })
  @IsOptional()
  unitType?: string[];

  @IsNumber()
  @IsOptional()
  minPrice?: number;

  @IsNumber()
  @IsOptional()
  maxPrice?: number;
}
