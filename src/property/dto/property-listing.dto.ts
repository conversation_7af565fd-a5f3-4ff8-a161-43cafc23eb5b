import {
  IsBoolean,
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EMarketSegment } from 'src/project/enums/market-segment.enum';
import {
  EProjectStatus,
  EProjectWhatIsNextStatus,
} from 'src/project/enums/project-status.enum';
import { ETenure } from 'src/project/enums/tenure.enum';
import { ETOPStatus } from 'src/project/enums/top-status.enum';

export class PropertyListingDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(ETenure)
  @IsOptional()
  tenure?: ETenure;

  @IsNumber()
  @IsOptional()
  minPrice?: number;

  @IsNumber()
  @IsOptional()
  maxPrice?: number;

  @IsString({ each: true })
  @IsOptional()
  unitType?: string[];

  @IsString({ each: true })
  @IsOptional()
  location?: string;

  @IsString({ each: true })
  @IsOptional()
  category?: string[];

  @IsEnum(EMarketSegment, { each: true })
  @IsOptional()
  marketSegment?: EMarketSegment[];

  @IsEnum(EProjectStatus, { each: true })
  @IsOptional()
  status?: EProjectStatus[];

  @IsNumber()
  @IsOptional()
  unitMinSize?: number;

  @IsNumber()
  @IsOptional()
  unitMaxSize?: number;

  @IsNumber()
  @IsOptional()
  minUnitPSF?: number;

  @IsNumber()
  @IsOptional()
  maxUnitPSF?: number;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  featured?: boolean;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  promotion?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  topSoon?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  upcomingLaunch?: boolean;

  @IsString()
  @IsOptional()
  top?: string;

  @IsEnum(EProjectWhatIsNextStatus)
  @IsOptional()
  whatIsNextStatus?: EProjectWhatIsNextStatus;

  @IsEnum(EProjectWhatIsNextStatus, { each: true })
  @IsOptional()
  whatIsNextStatusOptions?: EProjectWhatIsNextStatus[];

  @IsUUID()
  @IsOptional()
  developerId?: string;

  @IsNumber()
  @IsOptional()
  availableUnits?: number;
}
