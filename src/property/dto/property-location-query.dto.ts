import { IsNumber, IsOptional, IsString } from 'class-validator';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';

export class PropertyLocationQuery extends LanguageQueryDto {
  @IsString({ each: true })
  @IsOptional()
  location?: string[];

  @IsString({ each: true })
  @IsOptional()
  unitType?: string[];

  @IsNumber()
  @IsOptional()
  minPrice?: number;

  @IsNumber()
  @IsOptional()
  maxPrice?: number;
}
