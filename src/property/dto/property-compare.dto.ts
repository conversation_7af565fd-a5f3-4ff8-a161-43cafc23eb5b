import { Is<PERSON>rray, IsEnum, IsOptional, IsString } from 'class-validator';
import { ELangCode } from 'src/core/enums/lang.enum';

export class PropertyCompareDto {
  @IsArray()
  @IsString({ each: true })
  readonly projectSlugs: string[];

  @IsOptional()
  @IsEnum(ELangCode)
  lang?: ELangCode;
}

export type TProrpertyCompareDto = {
  projectId: string;
  projectSlug: string;
  projectName: string;
  marketSegment: string;
  location: string;
  developer: string;
  projectCategory: string;
  siteArea: string;
  totalUnits: number;
  availableUnits: number;
  minPrice: string;
  maxPrice: string;
  minPSF: string;
  maxPSF: string;
  tenure: string;
  expectedTop: string;
};
