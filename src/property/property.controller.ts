import {
  Controller,
  Get,
  Param,
  Headers,
  Query,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { PropertyService } from './property.service';
import { PropertyListingDto } from './dto/property-listing.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { PropertyCompareDto } from './dto/property-compare.dto';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { ProjectFloorPlanQueryDto } from './dto/project-floor-plan-query.dto';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { PropertySectionQuery } from './dto/property-section-query.dto';
import { AutoCompleteSearchQueryDto } from './dto/property-auto-complete-search-query.dto';
import { QueryGetProjectPriceList } from './dto/price-list.query.dto';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('property')
@CacheOption('property')
export class PropertyController {
  constructor(private readonly propertyService: PropertyService) {}

  @Get('unit-transactions/:slug')
  @Public()
  @RequireHeader('user-domain')
  getUnitTransactions(
    @Param('slug') slug: string,

    @Headers('user-domain') domain: string,
  ) {
    return this.propertyService.getUnitTransactions(slug, domain);
  }

  @Get()
  @Public()
  @RequireHeader('user-domain')
  listing(
    @Headers('user-domain') domain: string,
    @Query() query: PropertyListingDto,
  ) {
    return this.propertyService.listing(domain, query);
  }

  @Get('share')
  @Public()
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_PROPERTIES])
  getAll(@Query() query: PropertyListingDto) {
    return this.propertyService.getAll(query);
  }

  @Get('auto-complete-search')
  @Public()
  @RequireHeader('user-domain')
  autocompleteSearch(
    @Headers('user-domain') domain: string,
    @Query() query: AutoCompleteSearchQueryDto,
  ) {
    return this.propertyService.autoCompleteSearch(domain, query);
  }

  @Get('compare')
  @Public()
  @RequireHeader('user-domain')
  compare(@Query() query: PropertyCompareDto) {
    return this.propertyService.compare(query);
  }

  @Get('featured')
  @Public()
  @RequireHeader('user-domain')
  getFeatured(
    @Headers('user-domain') domain: string,
    @Query() query: PaginationQueryDto,
  ) {
    return this.propertyService.getFeatured(domain, query);
  }

  @Get(':slug/curated')
  @Public()
  @RequireHeader('user-domain')
  getCurated(
    @Headers('user-domain') domain: string,
    @Param('slug') slug: string,
  ) {
    return this.propertyService.getCuratedProject(domain, slug);
  }

  @Get('by-location')
  @Public()
  @RequireHeader('user-domain')
  getByLocation(
    @Headers('user-domain') domain: string,
    @Query() query: LanguageQueryDto,
  ) {
    return this.propertyService.getAllLocations(domain, query);
  }

  @Get('sections')
  @Public()
  @RequireHeader('user-domain')
  getSection(
    @Headers('user-domain') domain: string,
    @Query() query: PropertySectionQuery,
  ) {
    return this.propertyService.getSections(domain, query);
  }

  @Get('slugs')
  @Public()
  @RequireHeader('user-domain')
  getProjectSlugs(@Headers('user-domain') domain: string) {
    return this.propertyService.getAllProjectSlug(domain);
  }

  @UseInterceptors(CacheInterceptor)
  @Get(':slug/price-list')
  @Public()
  @RequireHeader('user-domain')
  getProjectPriceList(
    @Headers('user-domain') domain: string,
    @Param('slug') slug: string,
    @Query() query: QueryGetProjectPriceList,
  ) {
    return this.propertyService.getProjectPriceList(domain, slug, query);
  }

  @Get(':slug')
  @Public()
  @RequireHeader('user-domain')
  detail(
    @Headers('user-domain') domain: string,
    @Param('slug') slug: string,
    @Query() query: LanguageQueryDto,
  ) {
    return this.propertyService.detail(domain, slug, query);
  }

  @Get('share/slug/:slug')
  @Public()
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_PROPERTY_BY_SLUG])
  shareGetBySlug(
    @Query() query: PropertyListingDto,
    @Param('slug') slug: string,
  ) {
    return this.propertyService.detail(undefined, slug, query);
  }

  @UseInterceptors(CacheInterceptor)
  @Get(':slug/floor-plans')
  @Public()
  @RequireHeader('user-domain')
  getFloorPlans(
    @Headers('user-domain') domain: string,
    @Param('slug') slug: string,
    @Query() query: ProjectFloorPlanQueryDto,
  ) {
    return this.propertyService.getFloorPlans(domain, slug, query);
  }

  @Get(':slug/floor-plans/count')
  @Public()
  @RequireHeader('user-domain')
  getFloorPlansCount(
    @Headers('user-domain') domain: string,
    @Param('slug') slug: string,
    @Query() query: LanguageQueryDto,
  ) {
    return this.propertyService.countFloorPlans(domain, slug, query);
  }
}
