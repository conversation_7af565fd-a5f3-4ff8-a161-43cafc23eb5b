import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { Project } from 'src/project/entities/project.entity';
import { Repository } from 'typeorm';
import {
  IStepRule,
  PRICE_RULES,
  PSF_RULES,
  ROOM_AREA_RULES,
} from './constants/common.constant';

@Injectable()
export class CommonService {
  private readonly minPrice = 800000;
  constructor(
    @InjectRepository(FloorPlan)
    private readonly floorPlanRepo: Repository<FloorPlan>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
  ) {}

  async topListing() {
    const options = [];
    const queryBuilder = await this.projectRepo
      .createQueryBuilder('project')
      .select('DISTINCT EXTRACT(YEAR FROM project.expectedTop)', 'year')
      .where('project.deletedAt IS NULL')
      .andWhere('EXTRACT(YEAR FROM project.expectedTop) IS NOT NULL')
      .orderBy('year', 'ASC')
      .getRawMany();

    if (queryBuilder.length > 0) {
      queryBuilder.forEach((item) => {
        options.push({ label: item.year, value: item.year });
      });
    }

    return options;
  }

  async priceListing() {
    const minMaxPrices = await this.floorPlanRepo
      .createQueryBuilder('floorPlan')
      .select([
        // 'MIN(floorPlan.minPrice) AS minPrice',
        'MAX(floorPlan.maxPrice) AS maxPrice',
      ])
      .where('floorPlan.deletedAt IS NULL')
      .getRawOne();

    return this.generateStepArray(
      this.minPrice,
      minMaxPrices.maxprice,
      PRICE_RULES,
    );
  }

  async generateStepArray(
    min: number,
    max: number,
    rules: IStepRule[],
  ): Promise<number[]> {
    const result: number[] = [];
    let currentPrice = min;

    const findStep = (price: number) => {
      return rules.find((rule) => price < rule.limit)?.step ?? min;
    };

    let step = findStep(min);
    currentPrice = Math.floor(min / step) * step;

    while (currentPrice <= max) {
      result.push(currentPrice);
      currentPrice += step;
      step = findStep(currentPrice);
    }

    if (result[result.length - 1] !== max) {
      result.push(currentPrice);
    }

    return result;
  }

  async roomAreaListing() {
    const { minArea, maxArea } = await this.floorPlanRepo
      .createQueryBuilder('floorPlan')
      .select('MIN(floorPlan.area)', 'minArea')
      .addSelect('MAX(floorPlan.area)', 'maxArea')
      .getRawOne();

    return {
      minArea: minArea,
      maxArea: maxArea,
    };

    // return this.generateStepArray(minArea, maxArea, ROOM_AREA_RULES);
  }

  async psfListing() {
    const minMaxPrices = await this.floorPlanRepo
      .createQueryBuilder('floorPlan')
      .select([
        'ROUND(MIN(CASE WHEN floorPlan.area > 0 THEN floorPlan.minPrice / floorPlan.area ELSE 0 END)) AS minPrice',
        'ROUND(MAX(CASE WHEN floorPlan.area > 0 THEN floorPlan.maxPrice / floorPlan.area ELSE 0 END)) AS maxPrice',
      ])
      .where('floorPlan.deletedAt IS NULL')
      .getRawOne();

    return {
      minPrice: minMaxPrices.minprice,
      maxPrice: minMaxPrices.maxprice,
    };

    // return this.generateStepArray(
    //   minMaxPrices.minprice,
    //   minMaxPrices.maxprice,
    //   PSF_RULES,
    // );
  }
}
