import { Controller, Get, UseGuards, UseInterceptors } from '@nestjs/common';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CommonService } from './common.service';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('common')
@CacheOption('common', 60 * 5)
@Public()
@UseInterceptors(CacheInterceptor)
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  @Get('top-listing')
  listing() {
    return this.commonService.topListing();
  }

  @Get('top-listing/share')
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_TOP_LISTING])
  topListingShare() {
    return this.commonService.topListing();
  }

  @Get('price-listing')
  priceListing() {
    return this.commonService.priceListing();
  }

  @Get('price-listing/share')
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_PRICING])
  priceListingShare() {
    return this.commonService.priceListing();
  }

  @Get('room-area-listing')
  roomAreaListing() {
    return this.commonService.roomAreaListing();
  }

  @Get('psf-listing')
  psfListing() {
    return this.commonService.psfListing();
  }
}
