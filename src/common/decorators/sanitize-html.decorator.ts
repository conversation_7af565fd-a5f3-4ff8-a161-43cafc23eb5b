import { Transform } from 'class-transformer';
import * as sanitizeHtml from 'sanitize-html';

function deepSanitize(value: any): any {
  if (typeof value === 'string') {
    return sanitizeHtml(value);
  }

  if (Array.isArray(value)) {
    return value.map((item) => deepSanitize(item));
  }

  if (typeof value === 'object' && value !== null) {
    const sanitizedObj: any = {};
    for (const key in value) {
      sanitizedObj[key] = deepSanitize(value[key]);
    }
    return sanitizedObj;
  }

  return value;
}

export function SanitizeHtml() {
  return Transform(({ value }) => deepSanitize(value));
}
