import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'isSortObject', async: false })
export class IsSortObject implements ValidatorConstraintInterface {
  checkObjBySort(val: unknown) {
    return Object.values(val).every((val: any) =>
      ['ASC', 'DESC'].includes(val),
    );
  }

  /**
   * Just support for object-style query (e.g., sort[createdAt]=DESC)
   * Does NOT support stringified JSON (e.g., sort={"createdAt":"DESC"})
   */
  validate(value: any, _args: ValidationArguments) {
    if (typeof value === 'object') {
      return this.checkObjBySort(value);
    }
    // try {
    //   const obj = JSON.parse(value);
    //   return this.checkObjBySort(obj);
    // } catch (error) {
    //   return false;
    // }
    return false;
  }

  defaultMessage(_args: ValidationArguments) {
    return 'Each sort value must be either "ASC" or "DESC"';
  }
}
