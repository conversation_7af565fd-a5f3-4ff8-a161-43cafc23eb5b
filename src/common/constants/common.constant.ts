export interface IStepRule {
  limit: number;
  step: number;
}

export const PRICE_RULES: Array<IStepRule> = [
  { limit: 1000000, step: 100000 },
  { limit: 2000000, step: 250000 },
  { limit: 5000000, step: 500000 },
  { limit: 10000000, step: 1000000 },
  { limit: 20000000, step: 5000000 },
  { limit: 50000000, step: 10000000 },
  { limit: 200000000, step: 50000000 },
  { limit: 500000000, step: 100000000 },
];
export const ROOM_AREA_RULES: Array<IStepRule> = [
  { limit: 500, step: 500 },
  { limit: 1000, step: 250 },
  { limit: 1200, step: 200 },
  { limit: 1500, step: 300 },
  { limit: 3000, step: 500 },
  { limit: 5000, step: 1000 },
  { limit: 10000, step: 2500 },
  { limit: 20000, step: 5000 },
  { limit: 5000000, step: 10000 },
];

export const PSF_RULES: Array<IStepRule> = [
  { limit: 1000, step: 1000 },
  { limit: 3000, step: 500 },
  { limit: 5000, step: 1000 },
  { limit: 10000, step: 2500 },
  { limit: 100000, step: 10000 },
];
