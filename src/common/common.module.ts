import { forwardRef, Module } from '@nestjs/common';
import { CommonService } from './common.service';
import { CommonController } from './common.controller';
import { RedisModule } from 'src/redis/redis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { Project } from 'src/project/entities/project.entity';
import { ApiKeyModule } from 'src/api-key/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FloorPlan, Project]),
    RedisModule,
    forwardRef(() => ApiKeyModule),
  ],
  exports: [CommonService],
  providers: [CommonService],
  controllers: [CommonController],
})
export class CommonModule {}
