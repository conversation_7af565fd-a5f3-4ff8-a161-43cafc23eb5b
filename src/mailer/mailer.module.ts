import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailerService } from './mailer.service';
import { EmailLogModule } from 'src/email-log/email-log.module';
import { MailerController } from './mailer.controller';

@Module({
  imports: [TypeOrmModule.forFeature([]), EmailLogModule],
  controllers: [MailerController],
  providers: [MailerService],
  exports: [MailerService],
})
export class MailerModule {}
