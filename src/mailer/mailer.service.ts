import { ConfigType } from '@nestjs/config';
import { readFile } from 'fs';
import { render } from 'mustache';
import { Inject, Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { SendEmailDto } from './dto/email.dto';
import awsConfig from '../core/configs/aws.config';

import mjml2html = require('mjml');
import { EmailLogService } from 'src/email-log/email-log.service';

@Injectable()
export class MailerService {
  private readonly transporter: nodemailer.Transporter;

  constructor(
    @Inject(awsConfig.KEY)
    private readonly awsConfiguration: ConfigType<typeof awsConfig>,

    private readonly emailLogService: EmailLogService,
  ) {
    this.transporter = nodemailer.createTransport({
      host: awsConfiguration.sesSmtpEndpoint,
      port: 465,
      secure: true,
      auth: {
        user: awsConfiguration.sesSmtpUser,
        pass: awsConfiguration.sesSmtpPass,
      },
    });
  }

  static generateMail(
    template: string,
    patterns: Record<string, any> = {},
  ): string {
    return render(template, patterns);
  }

  async sendMail(
    data: SendEmailDto,
    params: Record<string, any> = {},
    extension: 'html' | 'mjml' = 'html',
  ) {
    const { to, subject, html, text } = data;
    const getHtml = await this.loadTemplate(`${html}.${extension}`);
    const parsedTemplate = MailerService.generateMail(getHtml, {
      subject,
      ...params,
    });
    return new Promise((resolve) => {
      this.transporter
        .sendMail({
          from: `"ProjectSG Group" <${this.awsConfiguration.sesEmailSender}>`,
          to: Array.isArray(to) ? to.join(', ') : to,
          subject,
          text,
          html:
            extension === 'mjml'
              ? mjml2html(parsedTemplate).html
              : parsedTemplate,
        })
        .then(async () => {
          console.log('@@@ Send mail success');

          await this.emailLogService.create({
            sender: this.awsConfiguration.sesEmailSender,
            receiver: Array.isArray(to) ? to.join(', ') : to,
            subject,
            template: `${html}.${extension}`,
            params,
            text,
          });

          resolve({
            status: 'success',
          });
        })
        .catch(async (err) => {
          console.log('@@@ Send mail error', err);

          await this.emailLogService.create({
            sender: this.awsConfiguration.sesEmailSender,
            receiver: Array.isArray(to) ? to.join(', ') : to,
            subject,
            template: `${html}.${extension}`,
            params,
            text,
            failedReason: err.message,
          });

          resolve({
            status: 'failed',
            response: err?.response,
            message: err?.message,
            stack: err?.stack,
          });
        });
    });
  }

  async loadTemplate(filename: string) {
    return await new Promise<string>((res, rej) => {
      readFile(`${process.cwd()}/emails/${filename}`, 'utf8', (err, data) => {
        if (err) {
          rej(err);
        } else {
          res(data);
        }
      });
    });
  }

  async sendHtmlStringMail(
    data: { to: string; subject: string; text: string },
    htmlContent: string,
    params: Record<string, any> = {},
  ) {
    const { to, subject, text } = data;

    const parsedHtmlContent = MailerService.generateMail(htmlContent, {
      subject,
      ...params,
    });

    return new Promise((resolve) => {
      this.transporter
        .sendMail({
          from: `"ProjectSG Group" <${this.awsConfiguration.sesEmailSender}>`,
          to: Array.isArray(to) ? to.join(', ') : to,
          subject,
          text,
          html: parsedHtmlContent,
        })
        .then(async () => {
          console.log('@@@ Send HTML string mail success');

          await this.emailLogService.create({
            sender: this.awsConfiguration.sesEmailSender,
            receiver: Array.isArray(to) ? to.join(', ') : to,
            subject,
            template: 'HTML String',
            params,
            text,
          });

          resolve({
            status: 'success',
          });
        })
        .catch(async (err) => {
          console.log('@@@ Send HTML string mail error', err);

          await this.emailLogService.create({
            sender: this.awsConfiguration.sesEmailSender,
            receiver: Array.isArray(to) ? to.join(', ') : to,
            subject,
            template: 'HTML String',
            params,
            text,
            failedReason: err.message,
          });

          resolve({
            status: 'failed',
          });
        });
    });
  }
}
