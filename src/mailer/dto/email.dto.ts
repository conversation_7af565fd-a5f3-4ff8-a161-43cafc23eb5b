import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

export class SendEmailDto {
  @IsString({ each: true })
  to: string | string[];

  @IsString()
  subject: string;

  @IsString()
  html: string;

  @IsString()
  @IsOptional()
  text?: string;

  @IsOptional()
  @IsObject()
  params?: Record<string, any>;

  @IsOptional()
  @IsEnum(['html', 'mjml'])
  extension?: 'html' | 'mjml';
}
