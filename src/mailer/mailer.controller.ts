import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { MailerService } from './mailer.service';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { SendEmailDto } from './dto/email.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';

@Controller('mailer')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class MailerController {
  constructor(private readonly mailerService: MailerService) {}

  @Post()
  async create(@Body() dto: SendEmailDto) {
    dto.params = dto.params || {};
    dto.extension = dto.extension || 'html';
    return await this.mailerService.sendMail(dto, dto.params, dto.extension);
  }
}
