import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { XeroPaymentHistory } from './entities/xero-payment-history.entity';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Roles } from 'src/users/entities/user.entity';
import { QueryXeroPaymentHistoriesDto } from './dto/query-xero-payment-history.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UpdateXeroPaymentHistoryDto } from './dto/update-xero-payment-history.dto';
import { CreateXeroPaymentHistoryDto } from './dto/create-xero-payment-history.dto';

@Injectable()
export class XeroPaymentHistoryService {
  constructor(
    @InjectRepository(XeroPaymentHistory)
    private readonly xeroPaymentHistoryRepository: Repository<XeroPaymentHistory>,
    private readonly configService: ConfigService,
  ) {}

  async getAll(
    data: { role: Roles; userId: string },
    dto: QueryXeroPaymentHistoriesDto,
  ) {
    const { role, userId } = data;
    const { filter } = dto;
    const pagination = getPaginationOption(dto);

    const queryBuilder = this.xeroPaymentHistoryRepository
      .createQueryBuilder('xeroPaymentHistory')
      .leftJoin('xeroPaymentHistory.asset', 'asset')
      .addSelect(['asset.urls'])
      .skip(pagination.offset)
      .take(pagination.limit);

    if ([Roles.USER, Roles.AGENCY].includes(role)) {
      queryBuilder.where('xeroPaymentHistory.userId = :userId', {
        userId,
      });
    }

    if (filter) {
      if (filter.xeroPaymentId)
        queryBuilder.andWhere(
          'xeroPaymentHistory.xeroPaymentId = :xeroPaymentId',
          {
            xeroPaymentId: filter.xeroPaymentId,
          },
        );
    }

    if (dto.search) {
      queryBuilder.andWhere(
        `xeroPaymentHistory.xeroInvoiceID LIKE :search 
           OR xeroPaymentHistory.xeroPaymentId LIKE :search`,
        { search: `%${dto.search}%` },
      );
    }

    const [packages, total] = await queryBuilder.getManyAndCount();
    return createPaginationResponse(packages, total, pagination);
  }

  async setInvoiceHistory(
    query: { xeroPaymentID: string },
    dto: UpdateXeroPaymentHistoryDto,
  ) {
    const { xeroPaymentID } = query;
    const xeroPaymentHistoryData =
      await this.xeroPaymentHistoryRepository.findOneBy({
        deletedAt: IsNull(),
        xeroPaymentID,
      });
    await this.xeroPaymentHistoryRepository.save({
      ...(xeroPaymentHistoryData ? xeroPaymentHistoryData : {}),
      ...dto,
    });
  }

  async getOneByUser(
    data: { role: Roles; userId: string },
    query: Partial<XeroPaymentHistory>,
  ) {
    const { role, userId } = data;
    let result: XeroPaymentHistory;
    if ([Roles.USER, Roles.AGENCY].includes(role)) {
      result = await this.xeroPaymentHistoryRepository.findOne({
        where: {
          userId,
          deletedAt: IsNull(),
          ...query,
        },
      });
    } else {
      result = await this.xeroPaymentHistoryRepository.findOne({
        where: {
          deletedAt: IsNull(),
          ...query,
        },
      });
    }

    if (!result) {
      throw new NotFoundException('XeroPaymentHistory not found');
    }
    return result;
  }

  async create(dto: CreateXeroPaymentHistoryDto) {
    const result = await this.xeroPaymentHistoryRepository.save(dto);
    return result;
  }

  async createOver(data: XeroPaymentHistory) {
    const result = await this.xeroPaymentHistoryRepository.save(data);
    return result;
  }

  async update(
    query: Partial<XeroPaymentHistory>,
    dto: UpdateXeroPaymentHistoryDto,
  ) {
    const { ...data } = dto;
    const result = await this.xeroPaymentHistoryRepository.update(query, data);
    return result;
  }

  async save(dto: UpdateXeroPaymentHistoryDto) {
    const { ...data } = dto;
    const result = await this.xeroPaymentHistoryRepository.save(data);
    return result;
  }

  async delete(query: Partial<XeroPaymentHistory>) {
    const { id } = query;
    const where: FindOptionsWhere<XeroPaymentHistory> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const promotionData =
      await this.xeroPaymentHistoryRepository.findOneBy(where);
    if (!promotionData) {
      throw new NotFoundException('XeroPaymentHistory not found');
    }

    const result = await this.xeroPaymentHistoryRepository.softDelete(where);
    return result;
  }

  async findTheLatest(xeroPaymentId: string) {
    const where: FindOptionsWhere<XeroPaymentHistory> = {
      deletedAt: IsNull(),
      xeroPaymentId,
    };
    const xeroPaymentHistoryData =
      await this.xeroPaymentHistoryRepository.findOne({
        where,
        order: { createdAt: 'desc' },
      });
    return xeroPaymentHistoryData;
  }
}
