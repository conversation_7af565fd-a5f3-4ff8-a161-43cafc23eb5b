import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { XeroPaymentHistory } from './entities/xero-payment-history.entity';
import { XeroPaymentHistoryController } from './xero-payment-history.controller';
import { XeroPaymentHistoryService } from './xero-payment-history.service';

@Module({
  imports: [TypeOrmModule.forFeature([XeroPaymentHistory])],
  providers: [XeroPaymentHistoryService],
  controllers: [XeroPaymentHistoryController],
  exports: [XeroPaymentHistoryService],
})
export class XeroPaymentHistoryModule {}
