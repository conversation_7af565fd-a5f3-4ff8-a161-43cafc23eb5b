import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Package } from 'src/package/entities/package.entity';
import { User } from 'src/users/entities/user.entity';
import { PromotionUsage } from 'src/promotion-usage/entities/promotion-usage.entity';
import { XeroPayment } from 'src/xero-payment/entities/xero-payment.entity';
import { EXeroPaymentMethod } from 'src/xero-payment/enums/xero-payment-method';
import { EXeroPaymentStatus } from 'src/xero-payment/enums/xero-payment-status';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class XeroPaymentHistory extends CrudEntity {
  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'uuid' })
  xeroPaymentId: string;

  //Related to xero payment
  @Column({ type: 'uuid', nullable: true })
  xeroPaymentID?: string;

  @ManyToOne(
    () => XeroPayment,
    (xeroPayment) => xeroPayment.xeroPaymentHistories,
  )
  @JoinColumn({ name: 'xeroPaymentId' })
  xeroPayment: XeroPayment;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  packageId: string;

  @ManyToOne(() => Package)
  @JoinColumn({ name: 'packageId' })
  package: Package;

  @Column({ nullable: true })
  paymentUrl?: string;

  @Column({ nullable: true })
  onlineInvoiceUrl?: string;

  @Column({ nullable: true })
  xeroInvoiceID?: string;

  @Column({
    type: 'enum',
    enum: EXeroPaymentStatus,
    default: EXeroPaymentStatus.PENDING,
  })
  status: EXeroPaymentStatus;

  @Column({ type: 'uuid', nullable: true })
  promotionUsageId?: string;

  @ManyToOne(() => PromotionUsage)
  @JoinColumn({ name: 'promotionUsageId' })
  promotionUsage?: PromotionUsage;

  @Column({
    type: 'enum',
    enum: EXeroPaymentType,
  })
  xeroPaymentType: EXeroPaymentType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  originPrice?: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountPrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalTax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  total: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  paid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalDue: number;

  @Column({ type: 'uuid', nullable: true })
  assetId?: string;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset?: Asset;

  @Column({
    type: 'enum',
    enum: EXeroPaymentMethod,
    nullable: true,
  })
  method: EXeroPaymentMethod;

  @Column({
    nullable: true,
  })
  reason?: string;
}
