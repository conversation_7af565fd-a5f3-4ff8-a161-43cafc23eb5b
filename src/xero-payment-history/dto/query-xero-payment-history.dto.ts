import {
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { XeroPaymentHistory } from '../entities/xero-payment-history.entity';

class XeroPaymentHistoryFilter
  implements Partial<Pick<XeroPaymentHistory, 'description' | 'xeroPaymentId'>>
{
  description?: string;

  @IsOptional()
  @IsUUID()
  xeroPaymentId?: string;
}

export class QueryXeroPaymentHistoriesDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => XeroPaymentHistoryFilter)
  filter?: XeroPaymentHistoryFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
