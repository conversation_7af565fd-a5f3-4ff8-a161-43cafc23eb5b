import {
  <PERSON>Date,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EXeroPaymentMethod } from 'src/xero-payment/enums/xero-payment-method';
import { EXeroPaymentStatus } from 'src/xero-payment/enums/xero-payment-status';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';

export class CreateXeroPaymentHistoryDto {
  @IsString()
  @IsOptional()
  description?: string;

  @IsUUID()
  @IsNotEmpty()
  xeroPaymentId: string;

  @IsUUID()
  @IsOptional()
  xeroPaymentID?: string;

  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsUUID()
  @IsNotEmpty()
  packageId: string;

  @IsString()
  @IsOptional()
  paymentUrl?: string;

  @IsString()
  @IsOptional()
  onlineInvoiceUrl?: string;

  @IsEnum(EXeroPaymentStatus)
  @IsNotEmpty()
  status: EXeroPaymentStatus;

  @IsUUID()
  @IsOptional()
  promotionUsageId?: string;

  @IsEnum(EXeroPaymentType)
  @IsNotEmpty()
  xeroPaymentType: EXeroPaymentType;

  @IsString()
  @IsOptional()
  xeroInvoiceID?: string;

  @IsOptional()
  @IsNumber()
  originPrice?: number;

  @IsOptional()
  @IsNumber()
  discountPrice?: number;

  @IsOptional()
  @IsNumber()
  total?: number;

  @IsOptional()
  @IsNumber()
  totalTax?: number;

  @IsOptional()
  @IsNumber()
  paid?: number;

  @IsOptional()
  @IsNumber()
  totalDue?: number;

  @IsOptional()
  @IsUUID()
  assetId?: string;

  @IsOptional()
  @IsEnum(EXeroPaymentMethod)
  method?: EXeroPaymentMethod;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsDate()
  createdAt?: Date;

  @IsOptional()
  @IsDate()
  updatedAt?: Date;
}
