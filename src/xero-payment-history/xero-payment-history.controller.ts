import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { XeroPaymentHistoryService } from './xero-payment-history.service';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { QueryXeroPaymentHistoriesDto } from './dto/query-xero-payment-history.dto';

@Controller('xero-payment-history')
@UseGuards(RolesGuard)
export class XeroPaymentHistoryController {
  constructor(
    private readonly xeroPaymentHistoryService: XeroPaymentHistoryService,
  ) {}

  @Get()
  async getAll(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Query() dto: QueryXeroPaymentHistoriesDto,
  ) {
    return await this.xeroPaymentHistoryService.getAll({ role, userId }, dto);
  }

  @Get(':id')
  async getById(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
  ) {
    return await this.xeroPaymentHistoryService.getOneByUser(
      { userId, role },
      { id },
    );
  }
}
