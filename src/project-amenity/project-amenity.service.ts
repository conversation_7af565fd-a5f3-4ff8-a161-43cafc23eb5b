import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateProjectAmenityDto } from './dto/create-project-amenity.dto';
import { UpdateProjectAmenityDto } from './dto/update-project-amenity.dto';
import { ELangCode } from '../core/enums/lang.enum';
import { ProjectAmenity } from './entities/project-amenity.entity';
import { Project } from '../project/entities/project.entity';
import {
  EProjectAmenitySortBy,
  ProjectAmenityQueryDto,
} from './dto/project-amenity-query.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from '../core/utils/pagination.util';
import { createMultilingualSelect } from '../core/utils/sql.util';
import { beautifyObject } from '../core/utils/common.util';
import { IProjectAmenityRaw } from './interfaces/raw-project-amenity.interface';
import { getLangValue } from '../core/utils/multi-language.ulti';
import { EOrderType } from 'src/core/enums/sort.enum';

@Injectable()
export class ProjectAmenityService {
  constructor(
    @InjectRepository(ProjectAmenity)
    private readonly projectAmenityRepo: Repository<ProjectAmenity>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
  ) {}

  async create(body: CreateProjectAmenityDto) {
    const project = await this.projectRepo.findOneBy({ id: body.projectId });
    if (!project) {
      throw new NotFoundException('Project is not found');
    }
    const data: Partial<ProjectAmenity> = {
      ...body,
      duration: body?.duration ?? 0,
      distance: body?.distance ?? 0,
      type: { [ELangCode.en]: body.type },
      name: { [ELangCode.en]: body.name },
    };
    return await this.projectAmenityRepo.save(data);
  }

  async listing(query: ProjectAmenityQueryDto) {
    const pagination = getPaginationOption(query);
    const lang = query.lang ?? ELangCode.en;

    const orderBy = query.sortBy || EProjectAmenitySortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.projectAmenityRepo
      .createQueryBuilder('projectAmenity')
      .leftJoinAndSelect('projectAmenity.photo', 'photo')
      .where('projectAmenity.projectId = :projectId', {
        projectId: query.projectId,
      })
      .orderBy(`projectAmenity.${orderBy}`, sorting)
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        `${createMultilingualSelect('projectAmenity', 'name', lang)} ILIKE :name`,
        { name: `%${query.search}%` },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  findOne(id: number) {
    return `This action returns a #${id} projectAmenity`;
  }

  async update(id: string, body: UpdateProjectAmenityDto) {
    const langCode = body.lang ?? ELangCode.en;
    const amenity = await this.projectAmenityRepo.findOneBy({ id });

    if (!amenity) {
      throw new NotFoundException('Amenity is not found');
    }

    const data: Partial<ProjectAmenity> = {
      ...body,
      duration: body?.duration ?? 0,
      distance: body?.distance ?? 0,
      type: {
        ...amenity.type,
        [langCode]: body.type,
      },
      name: {
        ...amenity.type,
        [langCode]: body.name,
      },
    };

    return await this.projectAmenityRepo.save({
      ...amenity,
      ...data,
    });
  }

  async delete(id: string) {
    const amenity = await this.projectAmenityRepo.findOneBy({ id });

    if (!amenity) {
      throw new NotFoundException('Amenity is not found');
    }

    return await this.projectAmenityRepo.update(
      { id },
      { deletedAt: new Date() },
    );
  }

  _beautifyProjectAmenity(raw: IProjectAmenityRaw) {
    return beautifyObject(raw, ['photo']);
  }

  formatResponse(
    item: ProjectAmenity,
    lang: ELangCode,
    fallback = ELangCode.en,
  ) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      type: getLangValue(lang, item.type, fallback),
    };
  }
}
