import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ProjectAmenityService } from './project-amenity.service';
import { CreateProjectAmenityDto } from './dto/create-project-amenity.dto';
import { UpdateProjectAmenityDto } from './dto/update-project-amenity.dto';
import { ProjectAmenityQueryDto } from './dto/project-amenity-query.dto';

@Controller('project-amenity')
export class ProjectAmenityController {
  constructor(private readonly projectAmenityService: ProjectAmenityService) {}

  @Post()
  create(@Body() body: CreateProjectAmenityDto) {
    return this.projectAmenityService.create(body);
  }

  @Get()
  listing(@Query() query: ProjectAmenityQueryDto) {
    return this.projectAmenityService.listing(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.projectAmenityService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() body: UpdateProjectAmenityDto) {
    return this.projectAmenityService.update(id, body);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.projectAmenityService.delete(id);
  }
}
