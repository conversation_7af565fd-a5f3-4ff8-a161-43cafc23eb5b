import { Test, TestingModule } from '@nestjs/testing';
import { ProjectAmenityController } from './project-amenity.controller';
import { ProjectAmenityService } from './project-amenity.service';

describe('ProjectAmenityController', () => {
  let controller: ProjectAmenityController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProjectAmenityController],
      providers: [ProjectAmenityService],
    }).compile();

    controller = module.get<ProjectAmenityController>(ProjectAmenityController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
