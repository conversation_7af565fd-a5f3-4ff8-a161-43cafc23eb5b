import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectAmenityService } from './project-amenity.service';
import { ProjectAmenityController } from './project-amenity.controller';
import { ProjectAmenity } from './entities/project-amenity.entity';
import { Project } from '../project/entities/project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      ProjectAmenity,
    ]),
  ],
  controllers: [ProjectAmenityController],
  providers: [ProjectAmenityService],
  exports: [ProjectAmenityService],
})
export class ProjectAmenityModule {
}
