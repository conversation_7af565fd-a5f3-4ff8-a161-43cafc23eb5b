import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EProjectAmenitySortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  name = 'name',
}

export class ProjectAmenityQueryDto extends PaginationQueryDto {
  @IsUUID()
  projectId: string;

  @IsString()
  @IsOptional()
  search?: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EProjectAmenitySortBy)
  @IsOptional()
  sortBy?: EProjectAmenitySortBy;
}
