import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString, IsUUID } from 'class-validator';

export class CreateProjectAmenityDto {
  @IsString()
  name: string;

  @IsString()
  type: string;

  @IsString()
  @IsOptional()
  coordinates?: string;

  @IsUUID()
  @IsNotEmpty()
  projectId: string;

  @IsNumber()
  @IsOptional()
  distance?: number;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @IsUUID()
  @IsOptional()
  photoId?: string;
}
