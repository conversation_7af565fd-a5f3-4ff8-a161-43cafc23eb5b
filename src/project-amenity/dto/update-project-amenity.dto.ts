import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';
import { OmitType, PartialType } from '@nestjs/mapped-types';
import { ELangCode } from '../../core/enums/lang.enum';
import { CreateProjectAmenityDto } from './create-project-amenity.dto';

export class UpdateProjectAmenityDto extends PartialType(
  OmitType(CreateProjectAmenityDto, ['projectId']),
) {
  @IsEnum(ELangCode)
  @IsOptional()
  lang?: ELangCode;

  @IsString()
  name: string;

  @IsString()
  type: string;

  @IsString()
  @IsOptional()
  coordinates?: string;

  @IsNumber()
  @IsOptional()
  distance?: number;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @IsUUID()
  @IsOptional()
  photoId?: string;
}
