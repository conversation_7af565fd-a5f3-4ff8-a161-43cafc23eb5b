import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { Asset } from '../../asset/entities/asset.entity';

@Entity()
export class ProjectAmenity extends CrudEntity {
  @Column({ type: 'jsonb' })
  name: Record<string, string>;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project, (project) => project.projectAmenities)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'float' })
  distance: number;

  @Column({ type: 'integer' })
  duration: number;

  @Column({ nullable: true, type: 'uuid' })
  photoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo: Asset;

  @Column({
    type: 'jsonb',
  })
  type?: Record<string, string>;

  @Column({ nullable: true })
  coordinates?: string;
}
