import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { Promotion } from '../entities/promotion.entity';
import { EPromotionStatus } from '../enums/promotion-status.enum';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { EPromotionType } from '../enums/promotion-type.enum.';
import { EDiscountType } from '../enums/discount-type.enum';

class PromotionFilter
  implements
    Partial<Pick<Promotion, 'discount' | 'discountType' | 'status' | 'type'>>
{
  @IsOptional()
  @IsNumber()
  discount?: number;

  @IsOptional()
  @IsEnum(EDiscountType)
  discountType?: EDiscountType;

  @IsOptional()
  @IsEnum(EDiscountType)
  status?: EPromotionStatus;

  @IsOptional()
  @IsEnum(EDiscountType)
  type?: EPromotionType;
}

export class QueryPromotionsDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PromotionFilter)
  filter?: PromotionFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
