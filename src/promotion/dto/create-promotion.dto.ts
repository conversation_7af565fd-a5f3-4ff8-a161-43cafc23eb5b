import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { EDiscountType } from '../enums/discount-type.enum';
import { EPromotionStatus } from '../enums/promotion-status.enum';
import { Type } from 'class-transformer';
import { EPromotionType } from '../enums/promotion-type.enum.';
import { EUsageLimitType } from '../enums/usage-limit-type.enum';

export class CreatePromotionDto {
  @IsNotEmpty()
  @IsString()
  code: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false })
  discount: number;

  @IsOptional()
  @IsEnum(EDiscountType)
  discountType?: EDiscountType;

  @IsOptional()
  @IsEnum(EPromotionStatus)
  status?: EPromotionStatus;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  @IsOptional()
  @IsEnum(EPromotionType)
  type?: EPromotionType;

  @IsOptional()
  @IsEnum(EUsageLimitType)
  usageLimitType?: EUsageLimitType;

  @IsOptional()
  @IsNumber()
  usageLimit?: number;
}
