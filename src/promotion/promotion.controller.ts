import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { Roles } from 'src/users/entities/user.entity';
import { PromotionService } from './promotion.service';
import { QueryPromotionsDto } from './dto/query-promotions.dto';
import { CreatePromotionDto } from './dto/create-promotion.dto';
import { UpdatePromotionDto } from './dto/update-promotion.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { EPromotionType } from './enums/promotion-type.enum.';
import { EPromotionStatus } from './enums/promotion-status.enum';

@Controller('promotion')
@UseGuards(RolesGuard)
export class PromotionController {
  constructor(private readonly promotionService: PromotionService) {}

  @Get()
  @AccessRoles(Roles.ADMIN)
  async getAll(@Query() dto: QueryPromotionsDto) {
    return await this.promotionService.getAll(dto);
  }

  @Get('code/:code')
  async getByCode(
    @Param('code') code: string,
    @ActiveUser('sub') userId: string,
  ) {
    return await this.promotionService.getOne({ code, userId });
  }

  @Get('apply/:code')
  async applyByCode(
    @Param('code') code: string,
    @ActiveUser('sub') userId: string,
  ) {
    return await this.promotionService.getOne({
      code,
      type: EPromotionType.PACKAGE,
      status: EPromotionStatus.ACTIVE,
      activeAt: new Date(),
      userId,
    });
  }

  @Get(':id')
  @AccessRoles(Roles.ADMIN)
  async getById(@Param('id') id: string) {
    return await this.promotionService.getOne({ id });
  }

  @Post()
  @AccessRoles(Roles.ADMIN)
  async create(@Body() body: CreatePromotionDto) {
    return await this.promotionService.create(body);
  }

  @Put(':id')
  @AccessRoles(Roles.ADMIN)
  async updateById(@Param('id') id: string, @Body() body: UpdatePromotionDto) {
    return await this.promotionService.update({ id }, body);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  async deleteById(@Param('id') id: string) {
    return await this.promotionService.delete({ id });
  }
}
