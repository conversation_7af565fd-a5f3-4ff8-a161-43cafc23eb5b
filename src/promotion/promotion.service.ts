import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Promotion } from './entities/promotion.entity';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { QueryPromotionsDto } from './dto/query-promotions.dto';
import { CreatePromotionDto } from './dto/create-promotion.dto';
import { UpdatePromotionDto } from './dto/update-promotion.dto';
import { PromotionUsageService } from 'src/promotion-usage/promotion-usage.service';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EUsageLimitType } from './enums/usage-limit-type.enum';

@Injectable()
export class PromotionService {
  constructor(
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,

    private readonly promotionUsageService: PromotionUsageService,
  ) {}

  async getAll(dto: QueryPromotionsDto) {
    const { filter, sort, search } = dto;

    const pagination = getPaginationOption(dto);

    const queryBuilder = this.promotionRepository
      .createQueryBuilder('promotion')
      .loadRelationCountAndMap(
        'promotion.usageCount',
        'promotion.promotionUsages',
      )
      .groupBy('promotion.id')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.discount)
        queryBuilder.andWhere('promotion.discount = :discount', {
          price: filter.discount,
        });

      if (filter.discountType)
        queryBuilder.andWhere('promotion.discountType = :discountType', {
          price: filter.discountType,
        });

      if (filter.status)
        queryBuilder.andWhere('promotion.status = :status', {
          numberOfCredit: filter.status,
        });

      if (filter.type)
        queryBuilder.andWhere('promotion.type = :type', {
          numberOfCredit: filter.type,
        });
    }

    if (search) {
      queryBuilder.andWhere(
        `promotion.code LIKE :search 
           OR promotion.description LIKE :search`,
        { search: `%${search}%` },
      );
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`promotion.${key}`, order);
      });
    }

    const [promotions, total] = await queryBuilder.getManyAndCount();
    return createPaginationResponse(promotions, total, pagination);
  }

  async getOne(
    query: Partial<Promotion> & { activeAt?: Date; userId?: string },
  ) {
    const { id, code, status, activeAt, userId } = query;

    const queryBuilder =
      this.promotionRepository.createQueryBuilder('promotion');

    queryBuilder.where('promotion.deletedAt IS NULL');

    if (id) {
      queryBuilder.andWhere('promotion.id = :id', { id });
    }

    if (code) {
      queryBuilder.andWhere('promotion.code = :code', { code });
    }

    if (status) {
      queryBuilder.andWhere('promotion.status = :status', { status });
    }

    if (activeAt) {
      queryBuilder.andWhere(
        `(promotion.startDate IS NULL OR promotion.startDate <= :activeAt)`,
        { activeAt },
      );

      queryBuilder.andWhere(
        `(promotion.endDate IS NULL OR promotion.endDate >= :activeAt)`,
        { activeAt },
      );
    }

    const result = await queryBuilder.getOne();
    if (!result) {
      throw new NotFoundException('Promotion not found');
    }

    if (userId) {
      switch (result.usageLimitType) {
        case EUsageLimitType.PER_USER: {
          const promotionUsageCount = await this.promotionUsageService.count({
            filter: { promotionId: result.id, byUserId: userId },
          });
          const usageLimit = Number(result.usageLimit);
          if (promotionUsageCount >= usageLimit) {
            throw new BadRequestException(
              'You have reached your promotional limit',
            );
          }
          break;
        }
        case EUsageLimitType.PER_PROMO_CODE: {
          const promotionUsageCount = await this.promotionUsageService.count({
            filter: { promotionId: result.id },
          });
          const usageLimit = Number(result.usageLimit);
          if (promotionUsageCount >= usageLimit) {
            throw new BadRequestException('Promotion has reached its limit');
          }
          break;
        }
        default: {
          break;
        }
      }
    }
    return result;
  }

  async create(dto: CreatePromotionDto) {
    const { code } = dto;
    const isExists = await this.promotionRepository.existsBy({
      deletedAt: IsNull(),
      code,
    });
    if (isExists) {
      throw new ConflictException('Promotion code already exists');
    }
    const result = await this.promotionRepository.save(dto);
    return result;
  }

  async update(query: Partial<Promotion>, dto: UpdatePromotionDto) {
    const { ...data } = dto;
    const result = await this.promotionRepository.update(query, data);
    return result;
  }

  async delete(query: Partial<Promotion>) {
    const { id } = query;
    const where: FindOptionsWhere<Promotion> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const promotionData = await this.promotionRepository.findOneBy(where);
    if (!promotionData) {
      throw new NotFoundException('Promotion not found');
    }

    const result = await this.promotionRepository.softDelete(where);
    return result;
  }

  generateRandomText(length: number = 10): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}
