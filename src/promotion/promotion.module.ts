import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Promotion } from './entities/promotion.entity';
import { PromotionService } from './promotion.service';
import { PromotionController } from './promotion.controller';
import { PromotionUsageModule } from 'src/promotion-usage/promotion-usage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Promotion]),
    forwardRef(() => PromotionUsageModule),
  ],
  providers: [PromotionService],
  controllers: [PromotionController],
  exports: [PromotionService],
})
export class PromotionModule {}
