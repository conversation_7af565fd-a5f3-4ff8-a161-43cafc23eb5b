import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { EPromotionStatus } from '../enums/promotion-status.enum';
import { EDiscountType } from '../enums/discount-type.enum';
import { EPromotionType } from '../enums/promotion-type.enum.';
import { PromotionUsage } from 'src/promotion-usage/entities/promotion-usage.entity';
import { EUsageLimitType } from '../enums/usage-limit-type.enum';

@Entity()
export class Promotion extends CrudEntity {
  @Column({ unique: true })
  code: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  discount?: number;

  @Column({
    type: 'enum',
    enum: EDiscountType,
    nullable: true,
  })
  discountType?: EDiscountType;

  @Column({
    type: 'enum',
    enum: EPromotionStatus,
    default: EPromotionStatus.ACTIVE,
  })
  status: EPromotionStatus;

  @Column({ type: 'timestamptz', nullable: true })
  startDate?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  endDate?: Date;

  @Column({
    type: 'enum',
    enum: EPromotionType,
    nullable: true,
  })
  type: EPromotionType;

  @OneToMany(() => PromotionUsage, (promotionUsage) => promotionUsage.promotion)
  promotionUsages: PromotionUsage[];

  @Column({
    type: 'enum',
    enum: EUsageLimitType,
    nullable: true,
  })
  usageLimitType?: EUsageLimitType;

  @Column({ nullable: true })
  usageLimit?: number;
}
