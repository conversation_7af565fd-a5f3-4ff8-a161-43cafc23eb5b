import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { HttpExceptionFilter } from './core/filter/http-exception.filter';
import { UnauthorizedExceptionFilter } from './core/filter/unauthorized-exception.filter';
import * as bodyParser from 'body-parser';
import { existsSync, mkdirSync } from 'fs';

async function bootstrap() {
  const logDir = 'logs';
  if (!existsSync(logDir)) {
    mkdirSync(logDir);
  }

  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });
  /**
   * Prevent NestJS from parsing the request body as JSON
   * This middleware ensures the body is processed as raw data (Buffer)
   */
  app.use(
    '/xero-payment/webhooks',
    bodyParser.raw({ type: 'application/json' }),
  );
  const port = process.env.SERVER_PORT || 3000;
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  app.useGlobalFilters(
    new HttpExceptionFilter(),
    new UnauthorizedExceptionFilter(),
  );
  app.useGlobalInterceptors(new LoggerErrorInterceptor());
  app.enableCors({
    origin:
      process.env.NODE_ENV === 'production'
        ? [
            'https://project.sg',
            'https://admin.project.sg',
            /^https:\/\/.*\.project\.sg$/,
            /^https:\/\/.*\.directbooking\.sg$/,
          ]
        : [
            'http://localhost:3333',
            'http://property-admin-development.s3-website-ap-southeast-1.amazonaws.com',
            'https://staging.project.sg',
            'https://admin.staging.project.sg',
            /^https:\/\/.*\.ngrok-free\.app$/,
            /^https:\/\/.*\.staging\.project\.sg$/,
            /^https:\/\/.*\.staging\.directbooking\.sg$/,
            /^https:\/\/.*\.trycloudflare\.com$/,
          ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    credentials: true,
  });
  // app.useLogger(app.get(Logger));

  await app.listen(port);
}
bootstrap();
