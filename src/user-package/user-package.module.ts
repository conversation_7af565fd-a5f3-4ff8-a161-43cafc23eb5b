import { forwardRef, Module } from '@nestjs/common';
import { UserPackageService } from './user-package.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserPackage } from './entities/user-package.entity';
import { BullModule } from '@nestjs/bull';
import { UserPackageProcessor } from './user-package.processor';
import { UserPackageController } from './user-package.controller';
import { Package } from 'src/package/entities/package.entity';
import { PackageModule } from 'src/package/package.module';
import { UsersModule } from 'src/users/users.module';
import { MailerModule } from 'src/mailer/mailer.module';
import { UserConfigModule } from 'src/user-config/user-config.module';
import { XeroPaymentModule } from 'src/xero-payment/xero-payment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserPackage, Package]),
    BullModule.registerQueue({
      name: 'user_package_queue',
    }),
    forwardRef(() => PackageModule),
    forwardRef(() => UsersModule),
    forwardRef(() => MailerModule),
    forwardRef(() => UserConfigModule),
    forwardRef(() => XeroPaymentModule),
  ],
  providers: [UserPackageService, UserPackageProcessor],
  exports: [UserPackageService],
  controllers: [UserPackageController],
})
export class UserPackageModule {}
