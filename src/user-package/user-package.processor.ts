import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Job, Queue } from 'bull';
import { UserPackage } from './entities/user-package.entity';
import { IsNull, Repository } from 'typeorm';
import { UserPackageService } from './user-package.service';
import { UsersService } from 'src/users/users.service';
import { MailerService } from 'src/mailer/mailer.service';
import { EMAIL_TEMPLATE } from 'src/mailer/enums/mailer.enum';
import * as moment from 'moment-timezone';
import { UserConfigService } from 'src/user-config/user-config.service';
import { ConfigService } from '@nestjs/config';
import { XeroPaymentService } from 'src/xero-payment/xero-payment.service';
import { EXeroPaymentType } from 'src/xero-payment/enums/xero-payment-type.enum';

@Processor('user_package_queue')
export class UserPackageProcessor {
  constructor(
    @InjectRepository(UserPackage)
    private readonly userPackageRepo: Repository<UserPackage>,
    @InjectQueue('user_package_queue')
    private readonly userPackageQueue: Queue,

    private readonly userPackageService: UserPackageService,
    private readonly usersService: UsersService,
    private readonly mailerService: MailerService,
    private readonly userConfigService: UserConfigService,
    private readonly configService: ConfigService,
    private readonly xeroPaymentService: XeroPaymentService,
  ) {}

  @Process('handleExpiredUserPackage')
  async handleExpiredUserPackage(job: Job) {
    console.log(
      `Processing handleExpiredUserPackage job: ${JSON.stringify(job.data)}`,
    );

    try {
      const { id } = job.data;

      await this.userPackageRepo.update({ id }, { isExpired: true });
      const userPackageData = await this.userPackageService.getOne({ id }, [
        'user',
        'package',
      ]);
      const { user, package: packageData } = userPackageData;
      await this.xeroPaymentService.findOrCreatePayment(user.id, {
        packageId: packageData.id,
        type: EXeroPaymentType.PACKAGE,
      });

      await this.userPackageQueue.add(
        'handleDeletedExpiredUserPackage',
        { id },
        //delay 3 days to delete
        {
          //hard code 3m to test
          // delay: 3 * 60 * 1000,
          delay: 3 * 24 * 60 * 60 * 1000,
          jobId: `handleDeletedExpiredUserPackage_${id}`,
        },
      );
      const userConfig = await this.userConfigService.findOneByUser(user.id);
      await this.mailerService.sendMail(
        {
          html: EMAIL_TEMPLATE.ACCOUNT_INACTIVE,
          to: user.email,
          subject: 'Account Now Inactive',
          text: '',
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: user.firstName + ' ' + user.lastName,
          packageName: packageData.name,
          amount: `${(packageData.price || 0).toFixed(2)}`,
          //parse to utc +8 SGT
          dueDate: moment(userPackageData.expiredAt)
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
          paymentLink: this.configService.get('ADMIN_LINK'),
        },
        'mjml',
      );
      console.log('Expire handleExpiredUserPackage successfully.');
    } catch (error) {
      console.error(
        'Error processing handleExpiredUserPackage job:',
        error.message,
      );
    }
  }

  @Process('handleDeletedExpiredUserPackage')
  async handleDeletedExpiredUserPackage(job: Job) {
    console.log(
      `Processing handleDeletedExpiredUserPackage job: ${JSON.stringify(job.data)}`,
    );

    try {
      const { id } = job.data;
      //find id and //if deleted and return null
      const userPackageData = await this.userPackageService.getOne({ id }, [
        'package',
        'user',
      ]);
      const { user, package: packageData } = userPackageData;
      const now = new Date();
      await this.userPackageRepo.update({ id }, { deletedAt: now });
      const userConfig = await this.userConfigService.findOneByUser(user.id);
      await this.usersService.updateStatusAccount(
        userPackageData.userId,
        'inactive',
      );
      await this.mailerService.sendMail(
        {
          html: EMAIL_TEMPLATE.ACCOUNT_INACTIVE,
          to: user.email,
          subject: 'Account Now Inactive',
          text: '',
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: user.firstName + ' ' + user.lastName,
          packageName: packageData.name,
          amount: `${(packageData.price || 0).toFixed(2)}`,
          //parse to utc +8 SGT
          dueDate: moment(userPackageData.expiredAt)
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
          paymentLink: this.configService.get('ADMIN_LINK'),
        },
        'mjml',
      );

      console.log('handleDeletedExpiredUserPackage successfully.');
    } catch (error) {
      console.error(
        'Error processing handleDeletedExpiredUserPackage job:',
        error.message,
      );
    }
  }

  @Process('handleReminderPayment')
  async handleReminderPayment(job: Job) {
    console.log(
      `Processing handleReminderPayment job: ${JSON.stringify(job.data)}`,
    );

    try {
      const { id } = job.data;
      const userPackage = await this.userPackageRepo.findOne({
        where: { deletedAt: IsNull(), id },
        relations: ['user', 'package', 'payment'],
      });
      if (userPackage) {
        const { user, package: packageData } = userPackage;
        const userConfig = await this.userConfigService.findOneByUser(user.id);
        await this.mailerService.sendMail(
          {
            html: EMAIL_TEMPLATE.PAYMENT_DUE_REMINDER,
            to: userPackage.user.email,
            subject: 'Renewal Payment',
            text: '',
          },
          {
            currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
            agentName: user.firstName + ' ' + user.lastName,
            packageName: packageData.name,
            amount: `${(packageData.price || 0).toFixed(2)}`,
            //parse to utc +8 SGT
            dueDate: moment(userPackage.expiredAt)
              .utcOffset(8)
              .format('HH:mm on MMM DD, YYYY'),
          },
          'mjml',
        );
      }

      console.log('handleReminderPayment successfully.');
    } catch (error) {
      console.error(
        'Error processing handleReminderPayment job:',
        error.message,
      );
    }
  }
}
