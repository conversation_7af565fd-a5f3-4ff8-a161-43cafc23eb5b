import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { UserPackage } from '../entities/user-package.entity';

class UserPackageFilter
  implements Partial<Pick<UserPackage, 'packageId' | 'enabled'>>
{
  @IsOptional()
  @IsUUID()
  packageId?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}

export class QueryUserPackagesDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => UserPackageFilter)
  filter?: UserPackageFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
