import {
  IsBoolean,
  IsDate,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  IsUUID,
} from 'class-validator';

export class CreateUserPackageDto {
  @IsUUID()
  @IsOptional()
  userPackageId?: string;

  @IsUUID()
  @IsNotEmpty()
  packageId: string;

  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsOptional()
  @IsBoolean()
  isExpired?: boolean;

  @IsOptional()
  @IsDate()
  expiredAt?: Date;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}
