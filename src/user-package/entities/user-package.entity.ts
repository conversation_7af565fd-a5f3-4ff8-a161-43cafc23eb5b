import CrudEntity from 'src/core/entities/crud.entity';
import { Package } from 'src/package/entities/package.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class UserPackage extends CrudEntity {
  @Column({ type: 'uuid' })
  packageId: string;

  @ManyToOne(() => Package)
  @JoinColumn({ name: 'packageId' })
  package: Package;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ default: false })
  isExpired: boolean;

  @Column({ type: 'timestamp', default: null })
  expiredAt: Date;

  @Column({ default: true })
  enabled: boolean;
}
