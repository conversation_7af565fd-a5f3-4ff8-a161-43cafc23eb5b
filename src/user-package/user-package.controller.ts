import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AttachUserPackageDto } from './dto/attach-user-package.dto';
import { UpdateUserPackageDto } from './dto/update-user-package.dto';
import { QueryUserPackagesDto } from './dto/query-user-packages.dto';
import { UserPackage } from './entities/user-package.entity';
import { UserPackageService } from './user-package.service';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { QueryUserPackageDto } from './dto/query-user-package.dto';

@Controller('user-package')
@UseGuards(RolesGuard)
export class UserPackageController {
  constructor(private readonly userPackageService: UserPackageService) {}

  @Get()
  @AccessRoles(Roles.ADMIN)
  async get(@Query() query: QueryUserPackagesDto): Promise<UserPackage[]> {
    return await this.userPackageService.getAll(query);
  }

  @Get('me')
  async getMe(@ActiveUser('sub') userId: string): Promise<UserPackage> {
    return await this.userPackageService.getByUserId(userId);
  }

  @Get('by-user')
  @AccessRoles(Roles.ADMIN)
  async getCurrent(@Query() dto: QueryUserPackageDto): Promise<UserPackage> {
    return await this.userPackageService.getCurrent(dto);
  }

  @Post()
  @AccessRoles(Roles.ADMIN)
  async create(@Body() body: AttachUserPackageDto): Promise<UserPackage> {
    const { userId, packageId } = body;
    return await this.userPackageService.attachUserPackage({
      userId,
      packageId,
    });
  }

  @Patch('/:id')
  @AccessRoles(Roles.ADMIN)
  async update(
    @Param('id') id: string,
    @Body() body: UpdateUserPackageDto,
  ): Promise<UserPackage> {
    return await this.userPackageService.updateById(id, body);
  }

  @Delete()
  async deleteByUser(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
  ): Promise<void> {
    return await this.userPackageService.delete({ userId });
  }

  @Delete('/:id')
  @AccessRoles(Roles.ADMIN)
  async delete(@Param('id') id: string): Promise<void> {
    return await this.userPackageService.delete({ id });
  }
}
