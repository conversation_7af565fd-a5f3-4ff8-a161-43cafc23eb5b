import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { Package } from 'src/package/entities/package.entity';
import { FindOptionsWhere, IsNull, Not, Repository } from 'typeorm';
import { CreateUserPackageDto } from './dto/create-user-package.dto';
import { UpdateUserPackageDto } from './dto/update-user-package.dto';
import { QueryUserPackagesDto } from './dto/query-user-packages.dto';
import { UserPackage } from './entities/user-package.entity';
import { EPackageStatus } from 'src/package/enums/package-status.enum';
import { QueryUserPackageDto } from './dto/query-user-package.dto';
import { PackageService } from 'src/package/package.service';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class UserPackageService {
  constructor(
    @InjectRepository(UserPackage)
    private readonly userPackageRepo: Repository<UserPackage>,

    @InjectRepository(Package)
    private readonly packageRepo: Repository<Package>,

    @InjectQueue('user_package_queue')
    private readonly userPackageQueue: Queue,

    private readonly packageService: PackageService,
    private readonly usersService: UsersService,
  ) {}

  async getOne(
    query: Partial<UserPackage>,
    leftJoins: ('user' | 'package')[] = [],
  ) {
    const { id, userId } = query;

    const queryBuilder = this.userPackageRepo
      .createQueryBuilder('user_package')
      .where('user_package.deletedAt IS NULL');

    if (id) {
      queryBuilder.andWhere('user_package.id = :id', { id });
    }
    if (userId) {
      queryBuilder.andWhere('user_package.userId = :userId', { userId });
    }

    if (leftJoins.length) {
      for await (const leftJoin of leftJoins) {
        queryBuilder.leftJoinAndSelect(`user_package.${leftJoin}`, leftJoin);
      }
    }

    const result = await queryBuilder.getOne();
    if (!result) {
      throw new NotFoundException('UserPackage not found');
    }

    return result;
  }

  async create(dto: CreateUserPackageDto): Promise<UserPackage> {
    const { userPackageId, packageId, userId, enabled, expiredAt, isExpired } =
      dto;
    const result = await this.userPackageRepo.save({
      id: userPackageId,
      packageId,
      userId,
      enabled,
      expiredAt,
      isExpired,
    });
    await this.usersService.updateStatusAccount(userId, 'active');

    if (expiredAt) {
      await this.addExpiredQueueJob(result.id, expiredAt);
    }

    return result;
  }

  async getAll(query: QueryUserPackagesDto): Promise<any> {
    const { search, sort, filter } = query;
    const pagination = getPaginationOption(query);

    const queryBuilder = this.userPackageRepo
      .createQueryBuilder('user_package')
      .leftJoinAndSelect('user_package.user', 'user')
      .leftJoinAndSelect('user_package.package', 'package')
      .andWhere('package.status = :status', { status: EPackageStatus.ACTIVE })
      .andWhere('user_package.deletedAt IS NULL')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.packageId)
        queryBuilder.andWhere('user_package.status = :packageId', {
          packageId: filter.packageId,
        });

      if (filter.enabled)
        queryBuilder.andWhere('user_package.enabled = :enabled', {
          enabled: filter.enabled,
        });
    }

    if (search) {
      const userSearchString =
        'user.email LIKE :search OR user.firstName LIKE :search OR user.lastName LIKE :search OR user.phone LIKE :search';
      const packageSearchString =
        'OR package.name LIKE :search OR package.description LIKE :search';
      queryBuilder.andWhere(`${userSearchString} ${packageSearchString}`, {
        search: `%${query.search}%`,
      });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`user_package.${key}`, order);
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async getByUserId(userId: string): Promise<UserPackage> {
    return await this.userPackageRepo.findOne({
      where: { user: { id: userId }, deletedAt: IsNull() },
      relations: ['user', 'package'],
    });
  }

  async getCurrent(dto: QueryUserPackageDto): Promise<UserPackage> {
    const { userId } = dto;
    return await this.userPackageRepo.findOne({
      where: { user: { id: userId }, deletedAt: IsNull() },
      relations: ['user', 'package'],
    });
  }

  async getByPackageId(packageId: string): Promise<UserPackage> {
    return await this.userPackageRepo.findOne({
      where: { package: { id: packageId }, deletedAt: IsNull() },
      relations: ['user', 'package'],
    });
  }

  async updateById(
    id: string,
    updateUserPackageDto: UpdateUserPackageDto,
  ): Promise<UserPackage> {
    try {
      const userPackageData = await this.userPackageRepo.findOne({
        where: { id, deletedAt: IsNull() },
        relations: ['user', 'package'],
      });

      if (!userPackageData) {
        throw new NotFoundException('UserPackage not found');
      }
      await this.removeQueueJob(userPackageData.id);
      let isExpired: boolean = false;
      if (userPackageData.expiredAt) {
        const now = Date.now();
        const expiresInMs = userPackageData.expiredAt.getTime() - now;
        isExpired = expiresInMs <= 0 ? true : false;
        await this.addExpiredQueueJob(
          userPackageData.id,
          userPackageData.expiredAt,
        );
      }

      return await this.userPackageRepo.save({
        ...userPackageData,
        ...updateUserPackageDto,
        isExpired,
      });
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async delete(query: Partial<UserPackage>) {
    try {
      const { id, userId } = query;
      const where: FindOptionsWhere<UserPackage> = { deletedAt: IsNull() };
      if (!id && !userId) {
        throw new BadRequestException('Please add id or userId');
      }
      if (id) {
        where.id = id;
      }
      if (userId) {
        where.userId = userId;
      }
      const userPackageData = await this.userPackageRepo.findOne({
        where,
        relations: ['user', 'package'],
        order: { createdAt: 'DESC' },
      });
      if (!userPackageData) {
        throw new NotFoundException('UserPackage not found');
      }

      await this.removeQueueJob(userPackageData.id);
      await this.usersService.updateStatusAccount(userId, 'inactive');
      await this.userPackageRepo.softDelete(where);
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async attachUserPackage({
    userId,
    packageId,
  }: {
    userId: string;
    packageId: string;
  }): Promise<UserPackage> {
    try {
      // NOTE: get active package data
      const packageData = await this.packageService.getPackage({
        id: packageId,
        status: EPackageStatus.ACTIVE,
      });

      if (!packageData) {
        throw new NotFoundException('Package not found');
      }

      // if (!packageData?.duration) {
      //   throw new BadRequestException('Package duration is not defined');
      // }

      const userPackagesData = await this.userPackageRepo.findBy({
        // packageId:Not(packageId),
        userId,
        deletedAt: IsNull(),
      });

      let userPackageFound: UserPackage | undefined;
      const userPackagesOther: UserPackage[] = [];

      for (const userPackage of userPackagesData) {
        if (userPackage.packageId === packageId) {
          userPackageFound = userPackage;
        } else {
          userPackagesOther.push(userPackage);
        }
      }
      //remove all other old user_package
      if (userPackagesOther.length > 0) {
        for await (const userPackageItem of userPackagesOther) {
          await this.removeQueueJob(userPackageItem.id);
          await this.userPackageRepo.update(
            { id: userPackageItem.id },
            { deletedAt: new Date(), isExpired: true },
          );
        }
      }

      const duration = packageData.duration;
      const now = new Date();
      let expiredDate = new Date(now);
      expiredDate.setDate(now.getDate() + duration);
      // renew this user package
      if (userPackageFound) {
        await this.removeQueueJob(userPackageFound.id);
        expiredDate = new Date(userPackageFound.expiredAt);
        expiredDate.setDate(expiredDate.getDate() + duration);
      }

      return await this.create({
        userPackageId: userPackageFound ? userPackageFound.id : undefined,
        userId,
        packageId,
        isExpired: false,
        enabled: true,
        expiredAt: duration ? expiredDate : undefined,
      });
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async addExpiredQueueJob(userPackageId: string, expiredAt: Date) {
    const now = Date.now();
    const expiresInMs = expiredAt.getTime() - now;
    if (expiresInMs <= 0) {
      return;
    }

    await this.userPackageQueue.add(
      'handleExpiredUserPackage',
      { id: userPackageId },
      {
        delay: expiresInMs,
        jobId: `handleExpiredUserPackage_${userPackageId}`,
      },
    );

    const delayReminderTime = expiresInMs - 10 * 24 * 60 * 60 * 1000;
    if (delayReminderTime > 0) {
      await this.userPackageQueue.add(
        'handleReminderPayment',
        { id: userPackageId },
        {
          delay: delayReminderTime,
          jobId: `handleReminderPayment_${userPackageId}`,
        },
      );
    }
  }

  async removeQueueJob(userPackageId: string) {
    //remove job expired
    await this.userPackageQueue.removeJobs(
      `handleExpiredUserPackage_${userPackageId}`,
    );
    //remove job deleted expired
    await this.userPackageQueue.removeJobs(
      `handleDeletedExpiredUserPackage_${userPackageId}`,
    );
    //remove reminder payment job
    await this.userPackageQueue.removeJobs(
      `handleReminderPayment_${userPackageId}`,
    );
  }
}
