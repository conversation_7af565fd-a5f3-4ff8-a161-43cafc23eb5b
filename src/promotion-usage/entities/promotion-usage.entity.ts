import CrudEntity from 'src/core/entities/crud.entity';
import { Promotion } from 'src/promotion/entities/promotion.entity';
import { User } from 'src/users/entities/user.entity';
import { XeroPayment } from 'src/xero-payment/entities/xero-payment.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class PromotionUsage extends CrudEntity {
  @Column({ type: 'uuid' })
  promotionId: string;

  @ManyToOne(() => Promotion)
  @JoinColumn({ name: 'promotionId' })
  promotion: Promotion;

  @OneToOne(() => XeroPayment)
  xeroPayment?: XeroPayment;

  @Column({ type: 'uuid', nullable: true })
  byUserId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'byUserId' })
  byUser?: User;
}
