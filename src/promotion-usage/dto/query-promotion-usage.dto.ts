import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { PromotionUsage } from '../entities/promotion-usage.entity';
import { Promotion } from 'src/promotion/entities/promotion.entity';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';

class PromotionUsageFilter
  implements Partial<Pick<PromotionUsage, 'promotionId' | 'byUserId'>>
{
  @IsUUID()
  @IsOptional()
  promotionId?: string;

  @IsUUID()
  @IsOptional()
  byUserId?: string;
}

export class QueryPromotionUsageDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PromotionUsageFilter)
  filter?: PromotionUsageFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
