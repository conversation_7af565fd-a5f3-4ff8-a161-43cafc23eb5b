import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { PromotionUsage } from './entities/promotion-usage.entity';
import { QueryPromotionUsageDto } from './dto/query-promotion-usage.dto';
import { CreatePromotionUsageDto } from './dto/create-promotion-usage.dto';
import { UpdatePromotionUsageDto } from './dto/update-promotion-usage.dto';

@Injectable()
export class PromotionUsageService {
  constructor(
    @InjectRepository(PromotionUsage)
    private readonly promotionUsageRepository: Repository<PromotionUsage>,
  ) {}

  async count(dto: Pick<QueryPromotionUsageDto, 'filter'>) {
    const { filter } = dto;
    const queryBuilder =
      this.promotionUsageRepository.createQueryBuilder('promotion_usage');

    if (filter) {
      if (filter.promotionId) {
        queryBuilder.andWhere('promotion_usage.promotionId = :promotionId', {
          promotionId: filter.promotionId,
        });
      }

      if (filter.byUserId) {
        queryBuilder.andWhere('promotion_usage.byUserId = :byUserId', {
          byUserId: filter.byUserId,
        });
      }
    }
    return queryBuilder.getCount();
  }

  async getAll(dto: QueryPromotionUsageDto) {
    const { filter, sort, search } = dto;

    const pagination = getPaginationOption(dto);

    const queryBuilder = this.promotionUsageRepository
      .createQueryBuilder('promotion_usage')
      .leftJoinAndSelect('promotion_usage.byUser', 'user')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.promotionId)
        queryBuilder.andWhere('promotion_usage.promotionId = :promotionId', {
          promotionId: filter.promotionId,
        });
    }

    if (search) {
      // queryBuilder.andWhere(`promotion_usage.??? LIKE :search`, {
      //   search: `%${search}%`,
      // });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`promotion_usage.${key}`, order);
      });
    }

    const [promotionUsages, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(promotionUsages, total, pagination);
  }

  async getOne(query: Partial<PromotionUsage>) {
    const { id } = query;

    const queryBuilder = this.promotionUsageRepository
      .createQueryBuilder('promotion_usage')
      .leftJoinAndSelect('promotion_usage.promotion', 'promotion')
      .where('promotion_usage.deletedAt IS NULL');

    if (id) {
      queryBuilder.andWhere('promotion_usage.id = :id', { id });
    }

    const result = await queryBuilder.getOne();
    if (!result) {
      throw new NotFoundException('PromotionUsage not found');
    }

    return result;
  }

  async delete(query: Partial<PromotionUsage>) {
    const { id } = query;
    const where: FindOptionsWhere<PromotionUsage> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const promotionUsageData =
      await this.promotionUsageRepository.findOneBy(where);
    if (!promotionUsageData) {
      throw new NotFoundException('PromotionUsage not found');
    }

    const result = await this.promotionUsageRepository.softDelete(where);
    return result;
  }

  async create(dto: CreatePromotionUsageDto) {
    return await this.promotionUsageRepository.save(dto);
  }

  async update(query: Partial<PromotionUsage>, dto: UpdatePromotionUsageDto) {
    const { id } = query;
    const where: FindOptionsWhere<PromotionUsage> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    return await this.promotionUsageRepository.update(where, dto);
  }
}
