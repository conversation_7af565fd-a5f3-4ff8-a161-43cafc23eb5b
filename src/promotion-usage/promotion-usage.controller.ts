import {
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { PromotionUsageService } from './promotion-usage.service';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { QueryPromotionUsageDto } from './dto/query-promotion-usage.dto';

@Controller('promotion-usage')
@UseGuards(RolesGuard)
export class PromotionUsageController {
  constructor(private readonly promotionUsageService: PromotionUsageService) {}

  @Get()
  @AccessRoles(Roles.ADMIN)
  async getAll(@Query() dto: QueryPromotionUsageDto) {
    return await this.promotionUsageService.getAll(dto);
  }

  @Get(':id')
  @AccessRoles(Roles.ADMIN)
  async getById(@Param('id') id: string) {
    return await this.promotionUsageService.getOne({ id });
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  async deleteById(@Param('id') id: string) {
    return await this.promotionUsageService.delete({ id });
  }
}
