import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PromotionUsage } from './entities/promotion-usage.entity';
import { PromotionUsageService } from './promotion-usage.service';
import { PromotionController } from 'src/promotion/promotion.controller';
import { PromotionModule } from 'src/promotion/promotion.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PromotionUsage]),
    forwardRef(() => PromotionModule),
  ],
  providers: [PromotionUsageService],
  controllers: [PromotionController],
  exports: [PromotionUsageService],
})
export class PromotionUsageModule {}
