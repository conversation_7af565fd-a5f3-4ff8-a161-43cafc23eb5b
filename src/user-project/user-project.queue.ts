import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { UserProjectService } from './user-project.service';

@Processor('user_project_queue')
export class UserProjectQueue {
  constructor(private readonly userProjectService: UserProjectService) {}

  @Process({
    name: 'addProjectToAllUserProject',
    concurrency: 1,
  })
  async addProjectToAllUserProject(job: Job<string>) {
    const { data } = job;
    if (!data) {
      return;
    }

    await this.userProjectService.addProjectToAllUserProject(data);
  }
}
