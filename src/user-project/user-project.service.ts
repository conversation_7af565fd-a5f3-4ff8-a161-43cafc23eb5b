import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateManyUserProjectDto,
  CreateUserProjectDto,
} from './dto/create-user-project.dto';
import { UpdateUserProjectDto } from './dto/update-user-project.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UserProject } from './entities/user-project.entity';
import { In, IsNull, Repository } from 'typeorm';
import { Project } from 'src/project/entities/project.entity';
import { QueryUserProjectDto } from './dto/query-user-project.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { isUUID } from 'class-validator';
import { RedisService } from 'src/redis/redis.service';
import { Section } from 'src/section/entities/section.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { Feature } from 'src/feature/entities/feature.entity';
import { EFeature } from 'src/feature/feature.enum';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class UserProjectService {
  constructor(
    @InjectRepository(UserProject)
    private readonly userProjectRepo: Repository<UserProject>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    private readonly redisService: RedisService,

    @InjectRepository(Section)
    private readonly sectionRepo: Repository<Section>,

    @InjectRepository(SectionProject)
    private readonly sectionProjectRepo: Repository<SectionProject>,

    @InjectRepository(Feature)
    private readonly featureRepo: Repository<Feature>,

    @InjectRepository(UserFeature)
    private readonly userFeatureRepo: Repository<UserFeature>,

    @InjectQueue('user_project_queue') private readonly userProjectQueue: Queue,
  ) {}

  async create(
    userId: string,
    createUserProjectDto: CreateUserProjectDto,
    shouldThrowError = true,
  ) {
    const existed = await this.userProjectRepo.findOne({
      where: { userId, projectId: createUserProjectDto.projectId },
    });

    if (existed) {
      if (shouldThrowError) {
        throw new BadRequestException('Project already added to your site');
      } else {
        console.log('Project already added to your site');
        return;
      }
    }

    const project = await this.projectRepo.findOne({
      where: { id: createUserProjectDto.projectId },
      relations: ['location'],
    });

    if (project.location) {
      const section = await this.sectionRepo.findOne({
        where: {
          location: { id: project.location.id },
        },
      });

      if (section) {
        await this.sectionProjectRepo.save({
          project: { id: createUserProjectDto.projectId },
          section: { id: section.id },
        });
      }
    }

    await this.redisService.deletePattern('user-project@*');

    return this.userProjectRepo.save({
      ...createUserProjectDto,
      upcomingLaunch:
        createUserProjectDto.upcomingLaunch || project.upcomingLaunch,
      featured: createUserProjectDto.featured || project.featured,
      promotion: createUserProjectDto.promotion || project.promotion,
      userId,
    });
  }

  async addAllProjects(userId: string) {
    const projects = await this.projectRepo.find();

    const projectIds = projects.map((project) => project.id);
    const result = await Promise.all(
      projectIds.map((projectId) => this.create(userId, { projectId }, false)),
    );

    console.log('result', result);
    return 'All projects added to your site';
  }

  async createMany(userId: string, body: CreateManyUserProjectDto) {
    if (!Array.isArray(body.data) || body.data.length === 0) {
      throw new BadRequestException(`'data' must be a not empty array`);
    }

    const projectIds = body.data.map((item) => item.projectId);

    const projects = await this.projectRepo.find({
      where: { id: In(projectIds) },
    });

    if (projectIds.length !== projects.length) {
      throw new BadRequestException('Have one or many invalid project id');
    }

    const userProjects = await this.userProjectRepo.find({
      where: {
        projectId: In(body.data.map((item) => item.projectId)),
        userId,
      },
    });

    const data = body.data.map((item) => {
      const existed = userProjects.find((p) => p.projectId === item.projectId);
      return Object.assign({}, existed, item, { userId });
    });

    await this.redisService.deletePattern('user-project@*');

    return await this.userProjectRepo.save(data);
  }

  async listing(userId: string, query: QueryUserProjectDto) {
    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.projectRepo
      .createQueryBuilder('project')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.userProjects', 'userProjects')
      .leftJoin('project.floorPlans', 'floorPlans')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .addSelect('COUNT(floorPlans.id)', 'floorPlanCount')
      .where('userProjects.userId = :userId', { userId })
      .groupBy('project.id')
      .addGroupBy('location.id')
      .addGroupBy('userProjects.id')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        `regexp_replace(${createMultilingualSelect('project', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :name`,
        {
          name: `%${query.search.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%`,
        },
      );
    }

    if (query.locationIds && query.locationIds.length > 0) {
      queryBuilder.andWhere('location.id IN (:...locationIds)', {
        locationIds: query.locationIds,
      });
    }

    if (query.filter) {
      query.filter.forEach((filter) => {
        if (filter.tenure) {
          queryBuilder.andWhere('project.tenure IN (:...tenure)', {
            tenure: filter.tenure,
          });
        }
        if (filter.marketSegment) {
          queryBuilder.andWhere(
            'project.marketSegment IN (:...marketSegment)',
            { marketSegment: filter.marketSegment },
          );
        }
        if (filter.category) {
          queryBuilder.andWhere('project.categoryId IN (:...category)', {
            category: filter.category,
          });
        }

        if (filter.featured) {
          queryBuilder.andWhere('userProjects.featured = :featured', {
            featured: filter.featured,
          });
        }

        if (filter.promotion) {
          queryBuilder.andWhere('userProjects.promotion = :promotion', {
            promotion: filter.promotion,
          });
        }
        if (filter.upcomingLaunch) {
          queryBuilder.andWhere(
            '(userProjects.upcomingLaunch = :upcomingLaunch OR project.upcomingLaunch = :upcomingLaunch )',
            {
              upcomingLaunch: filter.upcomingLaunch,
            },
          );
        }
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`project.${key}`, order);
      });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    const projects = data.entities.map((project) => {
      const raw = data.raw.find((i) => i.project_id === project.id);
      project['floorPlanCount'] = +raw.floorPlanCount || 0;
      return project;
    });

    return createPaginationResponse(projects, total, pagination);
  }

  async update(userId: string, projectId: string, body: UpdateUserProjectDto) {
    if (!isUUID(projectId)) {
      throw new BadRequestException('projectId must be an uuid');
    }
    const item = await this.userProjectRepo.findOneBy({ userId, projectId });
    if (!item) {
      throw new NotFoundException('This project is not added before');
    }

    await this.redisService.deletePattern('user-project@*');

    return await this.userProjectRepo.save(Object.assign(item, body));
  }

  async remove(userId: string, projectId: string) {
    return await this.userProjectRepo.delete({
      userId,
      projectId,
    });
  }

  async addProjectToAllUserProjectInQueue(projectId: string) {
    this.userProjectQueue.add('addProjectToAllUserProject', projectId);
    return null;
  }

  async addProjectToAllUserProject(projectId: string) {
    const featureAutoAdd = await this.featureRepo.findOneBy({
      type: EFeature.AutoAddNewProject,
    });
    if (!featureAutoAdd) {
      throw new NotFoundException('Feature not found');
    }
    const userFeatureData = await this.userFeatureRepo.find({
      where: {
        deletedAt: IsNull(),
        featureId: featureAutoAdd.id,
        enabled: true,
      },
    });

    for await (const userFeature of userFeatureData) {
      const newUserProject = new CreateUserProjectDto();
      newUserProject.projectId = projectId;
      await this.create(userFeature.userId, newUserProject);
    }
  }
}
