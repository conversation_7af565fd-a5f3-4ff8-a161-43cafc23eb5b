import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  Patch,
  UseInterceptors,
} from '@nestjs/common';
import { UserProjectService } from './user-project.service';
import {
  CreateManyUserProjectDto,
  CreateUserProjectDto,
} from './dto/create-user-project.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { QueryUserProjectDto } from './dto/query-user-project.dto';
import { UpdateUserProjectDto } from './dto/update-user-project.dto';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';

@Controller('user-project')
@CacheOption('user-project')
export class UserProjectController {
  constructor(private readonly userProjectService: UserProjectService) {}

  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createUserProjectDto: CreateUserProjectDto,
  ) {
    return this.userProjectService.create(userId, createUserProjectDto);
  }

  @Post('many')
  createMany(
    @ActiveUser('sub') userId: string,
    @Body() body: CreateManyUserProjectDto,
  ) {
    return this.userProjectService.createMany(userId, body);
  }

  @Post('add-all-projects')
  addAllProjects(@ActiveUser('sub') userId: string) {
    return this.userProjectService.addAllProjects(userId);
  }

  @Get()
  listing(
    @ActiveUser('sub') userId: string,
    @Query() query: QueryUserProjectDto,
  ) {
    return this.userProjectService.listing(userId, query);
  }

  @Patch(':projectId')
  update(
    @ActiveUser('sub') userId: string,
    @Param('projectId') projectId: string,
    @Body() body: UpdateUserProjectDto,
  ) {
    return this.userProjectService.update(userId, projectId, body);
  }

  @Delete(':projectId')
  remove(
    @ActiveUser('sub') userId: string,
    @Param('projectId') projectId: string,
  ) {
    return this.userProjectService.remove(userId, projectId);
  }
}
