import {
  IsA<PERSON>y,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EMarketSegment } from 'src/project/enums/market-segment.enum';
import { ETenure } from 'src/project/enums/tenure.enum';

export class QueryUserProjectDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  search?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  filter: Filter[];

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  locationIds?: string[];
}

export class Filter {
  @IsOptional()
  @IsEnum(ETenure, { each: true })
  tenure: ETenure[];

  @IsOptional()
  @IsEnum(EMarketSegment, { each: true })
  marketSegment: EMarketSegment[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  category: string[];

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  upcomingLaunch?: boolean;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  featured?: boolean;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  promotion?: boolean;

  @ParseOptionalBoolean()
  @IsBoolean()
  @IsOptional()
  isShow?: boolean;
}
