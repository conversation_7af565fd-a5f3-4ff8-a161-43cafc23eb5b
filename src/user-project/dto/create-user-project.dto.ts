import {
  IsBoolean,
  IsInt,
  IsOptional,
  IsPositive,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class CreateUserProjectDto {
  @IsUUID()
  projectId: string;

  @IsInt()
  @IsPositive()
  @IsOptional()
  weight?: number;

  @ParseOptionalBoolean()
  @IsBoolean()
  @IsOptional()
  featured?: boolean;

  @ParseOptionalBoolean()
  @IsBoolean()
  @IsOptional()
  promotion?: boolean;

  @ParseOptionalBoolean()
  @IsBoolean()
  @IsOptional()
  upcomingLaunch?: boolean;

  @ParseOptionalBoolean()
  @IsBoolean()
  @IsOptional()
  isShow?: boolean;
}

export class CreateManyUserProjectDto {
  @ValidateNested({ each: true })
  data: CreateUserProjectDto[];
}
