import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class UserProject extends CrudEntity {
  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'integer', default: 0 })
  weight: number;

  @Column({ nullable: true, default: false })
  featured?: boolean;

  @Column({ nullable: true, default: false })
  promotion?: boolean;

  @Column({ nullable: true })
  upcomingLaunch?: boolean;

  @Column({ nullable: true, default: true })
  isShow?: boolean;
}
