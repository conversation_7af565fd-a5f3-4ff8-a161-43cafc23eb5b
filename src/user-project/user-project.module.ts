import { Module } from '@nestjs/common';
import { UserProjectService } from './user-project.service';
import { UserProjectController } from './user-project.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProject } from './entities/user-project.entity';
import { Project } from 'src/project/entities/project.entity';
import { RedisModule } from 'src/redis/redis.module';
import { Section } from 'src/section/entities/section.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { Feature } from 'src/feature/entities/feature.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProject,
      Project,
      Section,
      SectionProject,
      Feature,
      UserFeature,
    ]),
    RedisModule,
    BullModule.registerQueue({
      name: 'user_project_queue',
    }),
  ],
  controllers: [UserProjectController],
  providers: [UserProjectService],
  exports: [UserProjectService],
})
export class UserProjectModule {}
