import { Modu<PERSON> } from '@nestjs/common';
import { UnitTransactionService } from './unit-transaction.service';
import { UnitTransactionController } from './unit-transaction.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Project } from 'src/project/entities/project.entity';
import { UnitTransaction } from './entities/unit-transaction.entity';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { RedisModule } from 'src/redis/redis.module';
import { AnalyticModule } from 'src/analytic/analytic.module';
import { FloorPlanModule } from 'src/floor-plan/floor-plan.module';
import { Unit } from 'src/unit/entities/unit.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Project, UnitTransaction, Unit]),
    UnitTypeModule,
    RedisModule,
    AnalyticModule,
    FloorPlanModule,
  ],
  controllers: [UnitTransactionController],
  providers: [UnitTransactionService],
  exports: [UnitTransactionService],
})
export class UnitTransactionModule {}
