import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { EUnitStatus } from '../enums/unit-status.enum.dto';

export class CreateUnitTransactionDto {
  @IsUUID()
  projectId: string;

  @IsUUID()
  @IsOptional()
  unitTypeId?: string;

  @IsDate()
  @Type(() => Date)
  timestamp: Date;

  @IsEnum(EUnitStatus)
  status: EUnitStatus;

  @IsString()
  unitName: string;
}
