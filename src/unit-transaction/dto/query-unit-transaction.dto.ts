import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EUnitTransactionSortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  timestamp = 'timestamp',
  unitType = 'unitType',
  unitName = 'unitName',
}

export class QueryUnitTransactionDto extends PaginationQueryDto {
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @IsString()
  projectSlug: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EUnitTransactionSortBy)
  @IsOptional()
  sortBy?: EUnitTransactionSortBy;
}
