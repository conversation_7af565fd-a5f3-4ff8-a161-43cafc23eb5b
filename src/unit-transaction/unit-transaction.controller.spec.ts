import { Test, TestingModule } from '@nestjs/testing';
import { UnitTransactionController } from './unit-transaction.controller';
import { UnitTransactionService } from './unit-transaction.service';

describe('UnitTransactionController', () => {
  let controller: UnitTransactionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UnitTransactionController],
      providers: [UnitTransactionService],
    }).compile();

    controller = module.get<UnitTransactionController>(UnitTransactionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
