import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateUnitTransactionDto } from './dto/create-unit-transaction.dto';
import { UpdateUnitTransactionDto } from './dto/update-unit-transaction.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UnitTransaction } from './entities/unit-transaction.entity';
import { Repository } from 'typeorm';
import { Project } from 'src/project/entities/project.entity';
import {
  EUnitTransactionSortBy,
  QueryUnitTransactionDto,
} from './dto/query-unit-transaction.dto';
import {
  createCombinedUnitTransactionResponse,
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { isUUID } from 'class-validator';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { ELangCode } from 'src/core/enums/lang.enum';
import { EOrderType } from 'src/core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';
import { EUnitStatus } from './enums/unit-status.enum.dto';
import { AnalyticService } from 'src/analytic/analytic.service';
import { FloorPlanService } from 'src/floor-plan/floor-plan.service';
import { Unit } from 'src/unit/entities/unit.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { uniqBy } from 'lodash';
import { getLangValue } from 'src/core/utils/multi-language.ulti';

@Injectable()
export class UnitTransactionService {
  constructor(
    @InjectRepository(UnitTransaction)
    private readonly unitTransactionRepo: Repository<UnitTransaction>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    private readonly unitTypeService: UnitTypeService,
    private readonly redisService: RedisService,
    private readonly analyticService: AnalyticService,

    private readonly floorPlanService: FloorPlanService,

    @InjectRepository(Unit)
    private readonly unitRepo: Repository<Unit>,
  ) {}
  async create(body: CreateUnitTransactionDto) {
    const project = await this.projectRepository.findOneBy({
      id: body.projectId,
    });

    if (!project) {
      throw new NotFoundException('Project is not found');
    }

    await this.unitTypeService.getDetail(body.unitTypeId);

    await this.redisService.deletePattern('unit-transaction@*');

    const res = await this.unitTransactionRepo.save(body);
    if (body.status === EUnitStatus.Sold) {
      await this.analyticService.incrementProjectUnitsSoldCount(
        project.id,
        res.timestamp,
      );
    }

    return res;
  }

  async listing2(query: QueryUnitTransactionDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const sortBy = query.sortBy || EUnitTransactionSortBy.createdAt;
    const order = query.sort || EOrderType.ASC;
    const lang = query.lang || ELangCode.en;

    const pagination =
      query.limit && query.page ? getPaginationOption(query) : null;

    const queryBuilder = this.unitRepo
      .createQueryBuilder('unit')
      .leftJoinAndSelect('unit.project', 'project')
      .leftJoinAndSelect('unit.floorPlan', 'floorPlan')
      .leftJoinAndSelect('floorPlan.unitType', 'unitType')
      .where('project.slug = :projectSlug', { projectSlug: query.projectSlug })
      .andWhere('unit.status = :status', { status: EUnitStatus.Sold })
      .orderBy(`unit.${sortBy === 'timestamp' ? 'updatedAt' : sortBy}`, order);

    if (pagination) {
      queryBuilder.limit(pagination.limit).offset(pagination.offset);
    }

    if (query.startDate) {
      queryBuilder.andWhere('unit.updatedAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('unit.updatedAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(
      data.map((item) => this.formatResponse2(item, lang)),
      total,
      pagination || { limit: total, page: 1, offset: 0 },
    );
  }

  async listing2Raw(query: QueryUnitTransactionDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const sortBy = query.sortBy || EUnitTransactionSortBy.createdAt;
    const order = query.sort || EOrderType.ASC;

    const pagination =
      query.limit && query.page ? getPaginationOption(query) : null;

    const baseParams = [query.projectSlug, EUnitStatus.Sold];

    const dateParams = [
      ...(query.startDate ? [query.startDate] : []),
      ...(query.endDate ? [query.endDate] : []),
    ];

    const paginationParams = pagination
      ? [pagination.limit, pagination.offset]
      : [];

    const params = [...baseParams, ...dateParams, ...paginationParams];

    const dataQuery = `
      SELECT 
        unit.*
      FROM "unit" unit
      INNER JOIN "project" project ON project.id = unit."projectId"
      WHERE project.slug = $1
        AND unit.status = $2
        AND unit."deletedAt" IS NULL
        ${query.startDate ? `AND unit."updatedAt" >= $${baseParams.length + 1}` : ''}
        ${query.endDate ? `AND unit."updatedAt" <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
      ORDER BY unit.${sortBy === 'timestamp' ? '"updatedAt"' : `"${sortBy}"`} ${order}
      ${pagination ? `LIMIT $${params.length - 1} OFFSET $${params.length}` : ''}
    `;

    const totalQuery = `
      SELECT COUNT(*) AS total
      FROM "unit" unit
      INNER JOIN "project" project ON project.id = unit."projectId"
      WHERE project.slug = $1
        AND unit.status = $2
        AND unit."deletedAt" IS NULL
        ${query.startDate ? `AND unit."updatedAt" >= $${baseParams.length + 1}` : ''}
        ${query.endDate ? `AND unit."updatedAt" <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
    `;

    const connection = this.unitRepo.manager.connection;

    const [data, totalResult] = await Promise.all([
      connection.query(dataQuery, params),
      connection.query(totalQuery, [...baseParams, ...dateParams]),
    ]);

    const total =
      totalResult.length > 0 ? parseInt(totalResult[0].total, 10) : 0;

    const dataWithUnitType = await Promise.all(
      data.map(async (item) => {
        const unit = await this.unitRepo.findOne({
          where: { id: item.id },
          relations: ['floorPlan', 'floorPlan.unitType'],
        });

        return {
          ...item,
          floorPlan: unit?.floorPlan,
          unitType: unit?.floorPlan?.unitType,
        };
      }),
    );

    return createPaginationResponse(
      dataWithUnitType.map((item) => this.formatResponse2(item, query.lang)),
      total,
      pagination || { limit: total, page: 1, offset: 0 },
    );
  }

  async listing(query: QueryUnitTransactionDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const sortBy = query.sortBy || EUnitTransactionSortBy.createdAt;
    const order = query.sort || EOrderType.ASC;
    const lang = query.lang || ELangCode.en;

    const pagination =
      query.limit && query.page ? getPaginationOption(query) : null;

    const queryBuilder = this.unitTransactionRepo
      .createQueryBuilder('unitTransaction')
      .leftJoinAndSelect('unitTransaction.project', 'project')
      .where('project.slug = :projectSlug', { projectSlug: query.projectSlug })
      .andWhere('unitTransaction.status = :status', {
        status: EUnitStatus.Sold,
      })
      .orderBy(`unitTransaction.${sortBy}`, order);

    if (pagination) {
      queryBuilder.limit(pagination.limit).offset(pagination.offset);
    }

    if (query.startDate) {
      queryBuilder.andWhere('unitTransaction.timestamp >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('unitTransaction.timestamp <= :endDate', {
        endDate: query.endDate,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    const dataWithUnitType = await Promise.all(
      data.map(async (item) => {
        const unit = await this.unitRepo.findOne({
          where: { name: item.unitName, project: { id: item.projectId } },
          relations: ['floorPlan', 'floorPlan.unitType'],
        });

        return { ...item, unitType: unit?.floorPlan?.unitType };
      }),
    );

    return createPaginationResponse(
      dataWithUnitType.map((item) => this.formatResponse(item, lang)),
      total,
      pagination || { limit: total, page: 1, offset: 0 },
    );
  }

  async listingRaw(query: QueryUnitTransactionDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const sortBy = query.sortBy || EUnitTransactionSortBy.createdAt;
    const order = query.sort || EOrderType.ASC;

    const pagination =
      query.limit && query.page ? getPaginationOption(query) : null;

    const rawQuery = `
      SELECT unit_transaction.*, COUNT(*) OVER() AS total
      FROM "unit_transaction" unit_transaction
      LEFT JOIN "project" project ON project.id = unit_transaction."projectId"
      WHERE project.slug = $1
        AND unit_transaction.status = $2
        AND unit_transaction."deletedAt" IS NULL
        ${query.startDate ? `AND unit_transaction.timestamp >= $3` : ''}
        ${query.endDate ? `AND unit_transaction.timestamp <= $4` : ''}
      ORDER BY unit_transaction.${sortBy} ${order}
      ${pagination ? `LIMIT $${query.startDate && query.endDate ? 5 : 3} OFFSET $${query.startDate && query.endDate ? 6 : 4}` : ''}
    `;

    const params = [
      query.projectSlug,
      EUnitStatus.Sold,
      ...(query.startDate ? [query.startDate] : []),
      ...(query.endDate ? [query.endDate] : []),
      ...(pagination ? [pagination.limit, pagination.offset] : []),
    ];

    const connection = this.unitRepo.manager.connection;
    const result = await connection.query(rawQuery, params);

    const data = result;
    const total = result.length > 0 ? parseInt(result[0].total, 10) : 0;

    const dataWithUnitType = await Promise.all(
      data.map(async (item) => {
        const unit = await this.unitRepo.findOne({
          where: { name: item.unitName, project: { id: item.projectId } },
          relations: ['floorPlan', 'floorPlan.unitType'],
        });

        return {
          ...item,
          unitType: unit?.floorPlan?.unitType,
        };
      }),
    );

    return createPaginationResponse(
      dataWithUnitType,
      total,
      pagination || { limit: total, page: 1, offset: 0 },
    );
  }

  async listingCombined(query: QueryUnitTransactionDto) {
    const [listingData, listing2Data] = await Promise.all([
      this.listing({ ...query, limit: undefined, page: undefined }),
      this.listing2({ ...query, limit: undefined, page: undefined }),
    ]);

    const listingMap = new Map(
      listingData.items.map((item) => [item.unitName, item]),
    );

    const updatedListing2Data = listing2Data.items.map((item) => {
      const matchedItem = listingMap.get(item.unitName);
      if (matchedItem) {
        return {
          ...item,
          timestamp: matchedItem.timestamp,
        };
      }
      return item;
    });

    updatedListing2Data.sort((a, b) => {
      const timestampA = new Date(a.timestamp || '').getTime();
      const timestampB = new Date(b.timestamp || '').getTime();
      return timestampB - timestampA;
    });

    if (!query.page || !query.limit) {
      return {
        items: updatedListing2Data,
        totalPage: 1,
        currentPage: 1,
        limit: updatedListing2Data.length,
        availableUnitCount: updatedListing2Data.length,
      };
    }

    const page = query.page ? Number(query.page) : 1;
    const limit = query.limit ? Number(query.limit) : 50;
    const offset = (page - 1) * limit;
    const paginatedData = updatedListing2Data.slice(offset, offset + limit);

    const total = updatedListing2Data.length;
    const totalPages = Math.ceil(total / limit);

    return {
      items: paginatedData,
      totalPage: totalPages,
      currentPage: page,
      limit,
      total,
    };
  }

  async listingCombinedRaw(query: QueryUnitTransactionDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const sortBy = query.sortBy || 'timestamp';
    const order = query.sort || EOrderType.DESC;
    const lang = query.lang || ELangCode.en;

    const pagination =
      query.limit && query.page ? getPaginationOption(query) : null;

    const baseParams = [query.projectSlug, EUnitStatus.Sold];
    const dateParams = [
      ...(query.startDate ? [query.startDate] : []),
      ...(query.endDate ? [query.endDate] : []),
    ];
    const paginationParams = pagination
      ? [pagination.limit, pagination.offset]
      : [];
    const params = [...baseParams, ...dateParams, ...paginationParams];

    const connection = this.unitRepo.manager.connection;

    const dataQuery = `SELECT *
FROM (
  SELECT DISTINCT ON (combined."unitName", combined."projectSlug")
    combined."unitName",
    combined."timestamp",
    combined."projectSlug",
    combined."unitTypeTitle",
    combined."unitTypeSlug",
    combined."bedRoomCount",
    combined."hasStudyRoom",
    combined."isDeluxe",
    combined."isPremium",
    combined."isPrivateLift",
    combined."isCompact",
    combined."isCompactPlus",
    combined."isDuplex",
    combined."isPenthouse",
    combined."isUtility",
    combined."hasGuest",
    combined."isStudio"
  FROM (
    SELECT 
      unit_transaction."unitName" AS "unitName",
      unit_transaction.timestamp AS "timestamp",
      project.slug AS "projectSlug",
      unit_type.title AS "unitTypeTitle",
      unit_type.slug AS "unitTypeSlug",
      unit_type."bedRoomCount" AS "bedRoomCount",
      unit_type."hasStudyRoom" AS "hasStudyRoom",
      unit_type."isDeluxe" AS "isDeluxe",
      unit_type."isPremium" AS "isPremium",
      unit_type."isPrivateLift" AS "isPrivateLift",
      unit_type."isCompact" AS "isCompact",
      unit_type."isCompactPlus" AS "isCompactPlus",
      unit_type."isDuplex" AS "isDuplex",
      unit_type."isPenthouse" AS "isPenthouse",
      unit_type."isUtility" AS "isUtility",
      unit_type."hasGuest" AS "hasGuest",
      unit_type."isStudio" AS "isStudio",
      1 AS "priority"
    FROM "unit_transaction" unit_transaction
    LEFT JOIN "project" project ON project.id = unit_transaction."projectId"
    LEFT JOIN "unit" unit ON unit.name = unit_transaction."unitName" AND unit."projectId" = unit_transaction."projectId"
    LEFT JOIN "floor_plan" floor_plan ON floor_plan.id = unit."floorPlanId"
    LEFT JOIN "unit_type" unit_type ON unit_type.id = floor_plan."unitTypeId"
    WHERE project.slug = $1
      AND unit_transaction.status = $2
      AND unit_transaction."deletedAt" IS NULL
      ${query.startDate ? `AND unit_transaction.timestamp >= $${baseParams.length + 1}` : ''}
      ${query.endDate ? `AND unit_transaction.timestamp <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
      AND NOT EXISTS (
        SELECT 1
        FROM "unit" u
        WHERE u.name = unit_transaction."unitName"
          AND u."projectId" = project.id
          AND u.status != $2::unit_status_enum
      )
      
    UNION ALL

    SELECT 
      unit.name AS "unitName",
      unit."updatedAt" AS "timestamp",
      project.slug AS "projectSlug",
      unit_type.title AS "unitTypeTitle",
      unit_type.slug AS "unitTypeSlug",
      unit_type."bedRoomCount" AS "bedRoomCount",
      unit_type."hasStudyRoom" AS "hasStudyRoom",
      unit_type."isDeluxe" AS "isDeluxe",
      unit_type."isPremium" AS "isPremium",
      unit_type."isPrivateLift" AS "isPrivateLift",
      unit_type."isCompact" AS "isCompact",
      unit_type."isCompactPlus" AS "isCompactPlus",
      unit_type."isDuplex" AS "isDuplex",
      unit_type."isPenthouse" AS "isPenthouse",
      unit_type."isUtility" AS "isUtility",
      unit_type."hasGuest" AS "hasGuest",
      unit_type."isStudio" AS "isStudio",
      2 AS "priority"
    FROM "unit" unit
    LEFT JOIN "project" project ON project.id = unit."projectId"
    LEFT JOIN "floor_plan" floor_plan ON floor_plan.id = unit."floorPlanId"
    LEFT JOIN "unit_type" unit_type ON unit_type.id = floor_plan."unitTypeId"
    WHERE project.slug = $1
      AND unit.status = $2::unit_status_enum
      AND unit."deletedAt" IS NULL
      ${query.startDate ? `AND unit."updatedAt" >= $${baseParams.length + 1}` : ''}
      ${query.endDate ? `AND unit."updatedAt" <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
  ) combined
  ORDER BY combined."unitName", combined."projectSlug", combined."priority", combined."timestamp" DESC
) subquery
ORDER BY "timestamp" DESC
${pagination ? `LIMIT $${params.length - 1} OFFSET $${params.length}` : ''}
`;

    const totalQuery = `
      SELECT COUNT(*) AS total
FROM (
  SELECT DISTINCT ON (combined."unitName", combined."projectSlug")
    combined."unitName",
    combined."projectSlug"
  FROM (
    SELECT 
      unit_transaction."unitName" AS "unitName",
      project.slug AS "projectSlug"
    FROM "unit_transaction" unit_transaction
    INNER JOIN "project" project ON project.id = unit_transaction."projectId"
    WHERE project.slug = $1
      AND unit_transaction.status = $2
      AND unit_transaction."deletedAt" IS NULL
      ${query.startDate ? `AND unit_transaction.timestamp >= $${baseParams.length + 1}` : ''}
      ${query.endDate ? `AND unit_transaction.timestamp <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
      AND NOT EXISTS (
        SELECT 1
        FROM "unit" u
        WHERE u.name = unit_transaction."unitName"
          AND u."projectId" = project.id
          AND u.status != $2::unit_status_enum
      )

    UNION

    SELECT 
      unit.name AS "unitName",
      project.slug AS "projectSlug"
    FROM "unit" unit
    INNER JOIN "project" project ON project.id = unit."projectId"
    WHERE project.slug = $1
      AND unit.status = $2::unit_status_enum
      AND unit."deletedAt" IS NULL
      ${query.startDate ? `AND unit."updatedAt" >= $${baseParams.length + 1}` : ''}
      ${query.endDate ? `AND unit."updatedAt" <= $${baseParams.length + (query.startDate ? 2 : 1)}` : ''}
  ) combined
) totalTable
    `;

    const [dataResult, totalResult] = await Promise.all([
      connection.query(dataQuery, params),
      connection.query(totalQuery, [...baseParams, ...dateParams]),
    ]);

    const total =
      totalResult.length > 0 ? parseInt(totalResult[0].total, 10) : 0;

    const dataWithFormattedUnitType = dataResult.map((item) => {
      const unitTypeTitle = item.unitTypeTitle || null;
      const title = unitTypeTitle ? getLangValue(lang, unitTypeTitle) : null;

      return {
        unitName: item.unitName,
        timestamp: item.timestamp,
        unitType: item.unitTypeTitle
          ? {
              id: item.unitTypeSlug,
              title,
              bedRoomCount: item.bedRoomCount,
              hasStudyRoom: item.hasStudyRoom,
              isDeluxe: item.isDeluxe,
              isPremium: item.isPremium,
              isPrivateLift: item.isPrivateLift,
              isCompact: item.isCompact,
              isCompactPlus: item.isCompactPlus,
              isDuplex: item.isDuplex,
              isPenthouse: item.isPenthouse,
              isUtility: item.isUtility,
              hasGuest: item.hasGuest,
              isStudio: item.isStudio,
            }
          : null,
      };
    });

    const totalPages = Math.ceil(total / (pagination?.limit || total));
    const currentPage = pagination?.page || 1;

    return {
      totalPage: totalPages,
      currentPage: currentPage,
      limit: pagination?.limit || total,
      total,
      items: dataWithFormattedUnitType.sort((a, b) => {
        const timestampA = new Date(a.timestamp || '').getTime();
        const timestampB = new Date(b.timestamp || '').getTime();
        return timestampB - timestampA;
      }),
    };
  }

  async findOne(id: string) {
    const item = await this.unitTransactionRepo.findOneBy({ id });

    if (!item) throw new NotFoundException('Item is not found');

    return item;
  }

  async update(id: string, body: UpdateUnitTransactionDto) {
    const item = await this.findOne(id);

    await this.redisService.deletePattern('unit-transaction@*');

    const res = await this.unitTransactionRepo.save({
      ...item,
      ...body,
    });

    if (res.status !== item.status) {
      await this.analyticService.incrementProjectUnitsSoldCount(
        item.projectId,
        res.timestamp,
        res.status === EUnitStatus.Sold ? 1 : -1,
      );
    }

    if (body.status === EUnitStatus.Sold) {
      await this.unitTransactionRepo.update(
        { id: item.id },
        { timestamp: body.timestamp },
      );
    }

    return res;
  }

  async remove(id: string) {
    const item = await this.findOne(id);
    if (item.status === EUnitStatus.Sold) {
      await this.analyticService.incrementProjectUnitsSoldCount(
        item.projectId,
        item.timestamp,
        -1,
      );
    }
    await this.redisService.deletePattern('unit-transaction@*');

    return await this.unitTransactionRepo.softRemove(item);
  }

  formatResponse(
    item: UnitTransaction,
    lang: ELangCode,
    fallback = ELangCode.en,
  ) {
    return {
      ...item,
      unitType: item?.unitType
        ? this.unitTypeService.formatResponse(item?.unitType, lang, fallback)
        : item?.unitType,
    };
  }

  formatResponse2(item: any, lang: ELangCode): any {
    return {
      unitType: {
        id: item.id,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        deletedAt: item.deletedAt,
        title: item.title?.[lang] || item.title?.en || null,
        slug: item.slug,
        bedRoomCount: item.bedRoomCount,
        hasStudyRoom: item.hasStudyRoom,
        isDeluxe: item.isDeluxe,
        isPremium: item.isPremium,
        isPrivateLift: item.isPrivateLift,
        isCompact: item.isCompact,
        isCompactPlus: item.isCompactPlus,
        isDuplex: item.isDuplex,
        isPenthouse: item.isPenthouse,
        isUtility: item.isUtility,
        hasGuest: item.hasGuest,
        isStudio: item.isStudio,
      },
      unitName: item.name,
      timestamp: item.updatedAt,
    };
  }

  formatResponseCombined(
    item: UnitTransaction | Unit,
    lang: ELangCode,
    fallback = ELangCode.en,
    isUnitTransaction: boolean,
  ) {
    return isUnitTransaction
      ? {
          ...item,
          unitType: (item as UnitTransaction)?.unitType
            ? this.unitTypeService.formatResponse(
                (item as UnitTransaction)?.unitType,
                lang,
                fallback,
              )
            : (item as UnitTransaction)?.unitType,
        }
      : {
          unitType: (item as Unit)?.floorPlan?.unitType
            ? this.unitTypeService.formatResponse(
                (item as Unit)?.floorPlan?.unitType,
                lang,
                fallback,
              )
            : (item as Unit)?.floorPlan?.unitType,
          unitName: (item as Unit).name,
          timestamp: (item as Unit).updatedAt,
        };
  }

  async findByName(name: string, projectId: string) {
    return await this.unitTransactionRepo.findOne({
      where: {
        unitName: name,
        projectId,
      },
    });
  }

  async countUnits(unitId: string) {
    try {
      const unit = await this.unitTransactionRepo.findOne({
        where: { id: unitId },
        relations: ['project'],
      });

      if (!unit) return;

      await this.floorPlanService.updateSoldAndAvailableUnitsByProjectId(
        unit.project?.id,
      );
    } catch (error) {
      console.log(error);
    }
  }
}
