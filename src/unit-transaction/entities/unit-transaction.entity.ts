import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { EUnitStatus } from '../enums/unit-status.enum.dto';

@Entity()
export class UnitTransaction extends CrudEntity {
  @Column({ type: 'uuid', name: 'projectId' })
  projectId: string;

  @ManyToOne(() => Project, (project) => project.transactions)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid', nullable: true })
  unitTypeId?: string;

  @ManyToOne(() => UnitType)
  @JoinColumn({ name: 'unitTypeId' })
  unitType?: UnitType;

  @Column()
  timestamp: Date;

  @Column()
  unitName: string;

  @Column()
  status: EUnitStatus;
}
