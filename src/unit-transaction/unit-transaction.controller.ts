import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UnitTransactionService } from './unit-transaction.service';
import { CreateUnitTransactionDto } from './dto/create-unit-transaction.dto';
import { UpdateUnitTransactionDto } from './dto/update-unit-transaction.dto';
import { QueryUnitTransactionDto } from './dto/query-unit-transaction.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';

@Controller('unit-transaction')
export class UnitTransactionController {
  constructor(
    private readonly unitTransactionService: UnitTransactionService,
  ) {}

  @Post()
  create(@Body() createUnitTransactionDto: CreateUnitTransactionDto) {
    this.unitTransactionService.create(createUnitTransactionDto);
  }

  @Get()
  @Public()
  async findAll(@Query() query: QueryUnitTransactionDto) {
    return this.unitTransactionService.listingCombinedRaw(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.unitTransactionService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUnitTransactionDto: UpdateUnitTransactionDto,
  ) {
    return this.unitTransactionService.update(id, updateUnitTransactionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.unitTransactionService.remove(id);
  }
}
