import { forwardRef, Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { IamModule } from '../iam/iam.module';
import { HashPasswordFactory } from '../core/decorators/hash-password.decorator';
import { ModuleRef } from '@nestjs/core';
import { AssetModule } from 'src/asset/asset.module';
import { MailerModule } from '../mailer/mailer.module';
import { Template } from '../template/entities/template.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UserFeatureModule } from 'src/user-feature/user-feature.module';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { UmamiModule } from 'src/umami/umami.module';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { Feature } from 'src/feature/entities/feature.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Template,
      UserConfig,
      UserProject,
      UmamiMapping,
      Feature,
    ]),
    IamModule,
    AssetModule,
    MailerModule,
    UserFeatureModule,
    UmamiModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    {
      provide: 'HashPassword',
      useFactory: HashPasswordFactory,
      inject: [ModuleRef],
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
