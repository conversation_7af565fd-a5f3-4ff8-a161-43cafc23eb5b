import { Exclude, instanceTo<PERSON><PERSON> } from 'class-transformer';
import { Asset } from 'src/asset/entities/asset.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { TimeSlot } from 'src/round-robin/entities/time-slot.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { UserPackage } from 'src/user-package/entities/user-package.entity';
import { UserProjectLandingPage } from 'src/user-project-landing-page/entities/user-project-landing-page.entity';
import {
  Entity,
  Column,
  OneToOne,
  JoinColumn,
  OneToMany,
  ManyToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}
export enum Roles {
  ADMIN = 'admin',
  AGENCY = 'agency',
  USER = 'user',
}

@Entity()
export class User extends CrudEntity {
  @Column({ unique: true })
  email: string;

  @Exclude()
  @Column()
  password: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({
    type: 'enum',
    enum: Roles,
    default: Roles.USER,
  })
  role: Roles;

  @Column({ length: 30 })
  firstName: string;

  @Column({ length: 30 })
  lastName: string;

  @Column({ nullable: true, length: 15 })
  phone?: string;

  @Column({ nullable: true, length: 15 })
  whatsapp?: string;

  @Column({ nullable: true })
  resetPasswordToken: string;

  @OneToOne(() => UserConfig, (config) => config.user)
  config?: UserConfig;

  @Column({ nullable: true })
  measuringId?: string;

  @OneToMany(
    () => ContactSaleSubmission,
    (contactSaleSubmission) => contactSaleSubmission.user,
  )
  contactSaleSubmission: ContactSaleSubmission[];

  @OneToMany(() => UserFeature, (userFeature) => userFeature.user)
  userFeature: UserFeature[];

  @Column({
    type: 'uuid',
    nullable: true,
  })
  photoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo?: Asset;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  agencyLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'agencyLogoId' })
  agencyLogo?: Asset;

  @Column({
    nullable: true,
  })
  company?: string;

  @Column({
    type: 'varchar',
    array: true,
    nullable: true,
  })
  additionalEmail?: string[];

  @Column({ type: 'uuid', nullable: true })
  ownerId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'ownerId' })
  owner: User;

  @ManyToMany(() => TimeSlot, (timeslot) => timeslot.users)
  @JoinTable({
    name: 'users_timeslots',
    joinColumn: {
      name: 'user_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'time_slot_id',
      referencedColumnName: 'id',
    },
  })
  timeslots: TimeSlot[];

  @Column({
    nullable: true,
  })
  changePasswordLogMessage?: string;

  @Column({
    nullable: true,
    default: false,
  })
  isFlowSignUp: boolean;

  @Column({
    nullable: true,
  })
  pricingPlans?: string;

  @Column({
    default: 'inactive',
  })
  statusAccount?: 'active' | 'inactive';

  @Column({
    nullable: true,
  })
  adRerportUrl?: string;

  @Column({
    nullable: true,
  })
  note?: string;

  @OneToMany(
    () => UserProjectLandingPage,
    (userProjectLandingPage) => userProjectLandingPage.user,
  )
  userProjectLandingPage: UserProjectLandingPage[];

  @OneToMany(() => UserPackage, (userPackage) => userPackage.user)
  userPackage: UserPackage[];

  toJSON() {
    return instanceToPlain(this);
  }
}
