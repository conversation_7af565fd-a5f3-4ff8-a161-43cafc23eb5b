import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { Public } from '../iam/authentication/decorators/auth.decorator';
import {
  ChangePasswordDTO,
  ForgotPasswordDTO,
  NewPasswordDTO,
  VerifyResetPasswordDTO,
} from './dto/forgot-pass.dto';
import { UpdateMeDto } from './dto/update-me.dto';
import * as _ from 'lodash';
import { SetPasswordDTO } from './dto/set-password.dto';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from './entities/user.entity';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  async create(
    @Body() createUserDto: CreateUserDto,
    @ActiveUser('sub') userId: string,
  ) {
    return this.usersService.create(createUserDto, userId);
  }

  @Post('/change-password')
  async changePassword(
    @Body() body: ChangePasswordDTO,
    @ActiveUser('sub') userId: string,
  ): Promise<any> {
    await this.usersService.changePassword(body, userId);
    return true;
  }

  @Post('/new-password')
  @Public()
  async newPassword(@Body() body: NewPasswordDTO): Promise<any> {
    await this.usersService.newPassword(body);
    return true;
  }

  @AccessRoles(Roles.ADMIN)
  @Post('/set-password')
  async setPassword(@Body() setPasswordDTO: SetPasswordDTO): Promise<boolean> {
    await this.usersService.setPassword(setPasswordDTO);
    return true;
  }

  @Post('/forgot-password')
  @Public()
  async forgotPassword(@Body() body: ForgotPasswordDTO): Promise<any> {
    await this.usersService.forgotPassword(body);
    return true;
  }

  @Get('/forgot-password/verify')
  @Public()
  async verifyResetPassword(
    @Query() body: VerifyResetPasswordDTO,
  ): Promise<any> {
    return this.usersService.verifyResetPassword(body);
  }

  @Get()
  findAll(
    @Query() queryUserDto: QueryUserDto,
    @ActiveUser('role') role: string,
  ) {
    const isOnlyProjectId =
      _.isEmpty(_.omit(queryUserDto, ['projectId'])) &&
      queryUserDto.projectId !== undefined;

    if (_.isEmpty(queryUserDto) || isOnlyProjectId) {
      return this.usersService.findAllSelection(role, queryUserDto);
    } else {
      return this.usersService.findAll(queryUserDto, role);
    }
  }

  @Get('get-my-users')
  getMyUsers(
    @Query() queryUserDto: QueryUserDto,
    @ActiveUser('sub') userId: string,
  ) {
    return this.usersService.getMyUsers(queryUserDto, userId);
  }

  @Get('me')
  async getProfile(@ActiveUser('sub') userId: string) {
    return this.usersService.findOne(userId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @Patch('/me')
  async updateMe(
    @ActiveUser('sub') userId: string,
    @Body() updateMeDto: UpdateMeDto,
  ) {
    return this.usersService.updateMe(userId, updateMeDto);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.usersService.remove(id);
  }

  @AccessRoles(Roles.ADMIN)
  @Post('/enable-directory-feature')
  async enableDirectoryFeature() {
    return this.usersService.enableDirectoryFeature();
  }
}
