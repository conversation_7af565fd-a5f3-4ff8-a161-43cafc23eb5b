import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Roles, User, UserStatus } from './entities/user.entity';
import { IsNull, Repository } from 'typeorm';
import { QueryUserDto } from './dto/query-user.dto';
import { HashingService } from 'src/iam/hashing/hashing.service';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import {
  ChangePasswordDTO,
  ForgotPasswordDTO,
  NewPasswordDTO,
  VerifyResetPasswordDTO,
} from './dto/forgot-pass.dto';
import { decodeToken, encodeToken } from '../core/utils/encrypt';
import { MailerService } from '../mailer/mailer.service';
import { EMAIL_TEMPLATE } from '../mailer/enums/mailer.enum';
import { Template } from '../template/entities/template.entity';
import { UpdateMeDto } from './dto/update-me.dto';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UserFeatureService } from 'src/user-feature/user-feature.service';
import { EFeature } from 'src/feature/feature.enum';
import { EDynamicLayoutMode } from 'src/user-feature/entities/user-feature.entity';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { SetPasswordDTO } from './dto/set-password.dto';
import { UmamiService } from 'src/umami/umami.service';
import { IUmamiCreateUsersResponse } from 'src/umami/types/users-response.interface';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { Not } from 'typeorm';
import { Feature } from 'src/feature/entities/feature.entity';
import { isLessThanCurrentTime } from 'src/core/utils/date.ultil';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    private readonly hashService: HashingService,
    private readonly assetService: AssetService,
    private readonly mailerService: MailerService,
    @InjectRepository(Template)
    private readonly templateRepo: Repository<Template>,

    private readonly userFeatureService: UserFeatureService,

    @InjectRepository(UserProject)
    private readonly userProjectRepository: Repository<UserProject>,

    private readonly umamiService: UmamiService,

    @InjectRepository(UmamiMapping)
    private readonly umamiMappingRepo: Repository<UmamiMapping>,

    @InjectRepository(Feature)
    private readonly featureRepository: Repository<Feature>,
  ) {}

  async onModuleInit() {
    await this.seedAdmin();
  }

  async seedAdmin() {
    const adminEmail = '<EMAIL>';
    const adminPassword = 'prj.admin@123';
    const user = await this.userRepository.findOneBy({ email: adminEmail });

    if (!user) {
      // this.logger.info('Begin seed admin with email', adminEmail);
      await this.create({
        email: adminEmail,
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'Super',
        role: Roles.ADMIN,
      });
    }
  }

  async create(
    createUserDto: CreateUserDto,
    createrId?: string,
  ): Promise<User> {
    const exist = await this.userRepository.findOneBy({
      email: createUserDto.email,
    });
    if (exist) {
      throw new UnprocessableEntityException(
        `User with email '${createUserDto.email}' already exists`,
      );
    }
    const user = await this.userRepository.save({
      ...createUserDto,
      ownerId: createrId,
      password: await this.hashService.hash(createUserDto.password),
      adRerportUrl: 'https://paywallblur.vercel.app/',
    });

    // const createdUmamiUser =
    //   await this.umamiService.request<IUmamiCreateUsersResponse>(
    //     'post',
    //     `${this.umamiService.umamiHost}/api/users`,
    //     {
    //       payload: {
    //         username: createUserDto.email,
    //         password: createUserDto.password,
    //         role: 'user',
    //       },
    //     },
    //   );

    // await this.umamiMappingRepo.save({
    //   user,
    //   umamiUserId: createdUmamiUser.id,
    //   umamiUserName: createUserDto.email,
    //   umamiPassword: createUserDto.password,
    // });

    try {
      if (createUserDto.photoId) {
        await this.assetService.addAssetRelation(
          createUserDto.photoId,
          EAssetRelation.User,
          user.id,
        );
      }
    } catch {}

    await this.userFeatureService.initAutoAddNewProjectFeature(user.id);
    return user;
  }

  async getMyUsers(query: QueryUserDto, ownerId: string) {
    const { filter } = query;
    const pagination = getPaginationOption(query);

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.photo', 'photo')
      .leftJoinAndSelect('user.config', 'config')
      .leftJoinAndSelect('config.domains', 'domains')
      .andWhere('user.role = :role', { role: Roles.USER })
      .andWhere('user.ownerId = :ownerId', { ownerId })
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.email)
        queryBuilder.andWhere('user.email = :email', { email: filter.email });
      if (filter.status)
        queryBuilder.andWhere('user.status = :status', {
          status: filter.status,
        });
      if (filter.firstName)
        queryBuilder.andWhere('user.firstName = :firstName', {
          firstName: filter.firstName,
        });
      if (filter.lastName)
        queryBuilder.andWhere('user.lastName = :lastName', {
          lastName: filter.lastName,
        });
      if (filter.phone)
        queryBuilder.andWhere('user.phone = :phone', { phone: filter.phone });
    }

    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      queryBuilder.andWhere(
        "LOWER(user.firstName) || ' ' || LOWER(user.lastName) LIKE :search",
        { search: `%${searchTerm}%` },
      );
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`user.${key}`, order);
      });
    }

    const [users, total] = await queryBuilder.getManyAndCount();

    const result = users?.map((user) => {
      const { config, ...rest } = user;
      return {
        ...rest,
        domains: user?.config?.domains,
      };
    });

    return createPaginationResponse(result, total, pagination);
  }

  async findAllSelection(role: string, query?: QueryUserDto) {
    if (query && query.projectId) {
      const userProject = await this.userProjectRepository.findOne({
        where: { project: { id: query.projectId } },
        relations: ['user'],
      });

      const userQuery = this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.config', 'config')
        .leftJoinAndSelect('config.domains', 'domains')
        .select([
          'domains.name as domainName',
          'user.firstName as firstName',
          'user.lastName as lastName',
          'user.id as id',
        ])
        .where('user.id = :userId', { userId: userProject?.user?.id });

      if (role === Roles.ADMIN) {
        userQuery.andWhere('user.role = :role', { role: Roles.AGENCY });
      }

      return await userQuery.getRawMany();
    } else {
      const userQuery = this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.config', 'config')
        .leftJoinAndSelect('config.domains', 'domains')
        .select([
          'domains.name as domainName',
          'user.firstName as firstName',
          'user.lastName as lastName',
          'user.id as id',
        ]);

      if (role === Roles.ADMIN) {
        userQuery.where('user.role = :role', { role: Roles.AGENCY });
      }

      return await userQuery.getRawMany();
    }
  }

  async findAll(query: QueryUserDto, role?: string) {
    const { filter } = query;
    const pagination = getPaginationOption(query);

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.photo', 'photo')
      .leftJoinAndSelect('user.config', 'config')
      .leftJoinAndSelect('user.userFeature', 'userFeatures')
      .leftJoinAndSelect('userFeatures.feature', 'feature')
      .leftJoinAndSelect('config.domains', 'domains')
      .leftJoinAndSelect(
        'user.userProjectLandingPage',
        'userProjectLandingPage',
        'userProjectLandingPage.deletedAt IS NULL',
      )
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.email)
        queryBuilder.andWhere('user.email = :email', { email: filter.email });
      if (filter.status)
        queryBuilder.andWhere('user.status = :status', {
          status: filter.status,
        });
      if (filter.firstName)
        queryBuilder.andWhere('user.firstName = :firstName', {
          firstName: filter.firstName,
        });
      if (filter.lastName)
        queryBuilder.andWhere('user.lastName = :lastName', {
          lastName: filter.lastName,
        });
      if (filter.phone)
        queryBuilder.andWhere('user.phone = :phone', { phone: filter.phone });
    }

    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      queryBuilder.andWhere(
        "LOWER(user.firstName) || ' ' || LOWER(user.lastName) LIKE :search",
        { search: `%${searchTerm}%` },
      );
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`user.${key}`, order);
      });
    }

    if (role === Roles.ADMIN) {
      queryBuilder.andWhere('user.role = :role', { role: Roles.AGENCY });
    }

    const [users, total] = await queryBuilder.getManyAndCount();

    const result = users?.map((user) => {
      const { config, ...rest } = user;
      return {
        ...rest,
        userProjectLandingPage: undefined,
        landingPagesCount: (user.userProjectLandingPage || []).length,
        domains: user?.config?.domains,
      };
    });

    return createPaginationResponse(result, total, pagination);
  }

  async findOne(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: {
        photo: true,
        config: {
          domains: true,
        },
        agencyLogo: true,
        userPackage: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User #${id} not found`);
    }

    const userFeatures = await this.userFeatureService.findAllByUserId(id);

    const userFeaturesResponse = await Promise.all(
      userFeatures.map((userFeature) =>
        this.userFeatureService.buildUserFeatureResponse(userFeature),
      ),
    );

    user['userFeatures'] = userFeaturesResponse;

    const dynamicLayout = userFeatures.find(
      (userFeature) => userFeature?.feature?.type === EFeature.DynamicLayout,
    );

    user['dynamicLayoutMode'] = dynamicLayout?.enabled
      ? dynamicLayout?.dynamicLayoutMode
      : EDynamicLayoutMode.NORMAL;

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const existed = await this.findOne(id);

    if (updateUserDto.password) {
      updateUserDto.password = await this.hashService.hash(
        updateUserDto.password,
      );
    }

    try {
      if (
        existed.photoId !== null &&
        existed.photoId !== updateUserDto.photoId
      ) {
        if (existed.photoId) {
          await this.assetService.remove(existed.photoId);
        }
        if (updateUserDto.photoId) {
          await this.assetService.addAssetRelation(
            updateUserDto.photoId,
            EAssetRelation.User,
            existed.id,
          );
        }
      }
    } catch (error) {
      console.error(error);
    }

    if (updateUserDto?.measuringId) {
      const userConfig = await this.userConfigRepository.findOne({
        where: {
          userId: id,
        },
      });

      if (userConfig) {
        await this.userConfigRepository.save({
          id: userConfig.id,
          measuringId: updateUserDto.measuringId,
        });
      }
    }

    return await this.userRepository.save({ ...existed, ...updateUserDto });
  }

  async remove(id: string) {
    await this.findOne(id);

    // Update the user's status or any other fields before soft deleting
    await this.userRepository.update(id, {
      status: UserStatus.INACTIVE,
      deletedAt: new Date(),
    });
  }

  async forgotPassword({ email, callbackURL }: ForgotPasswordDTO) {
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User is not exist');
    }

    const tokenDate = new Date();
    const tokenTimestamp = Math.floor(tokenDate.getTime() / 1000);
    const token = encodeToken(`?userId=${user.id}&timestamp=${tokenTimestamp}`);
    await this.mailerService.sendMail(
      {
        to: email,
        subject: 'Request to reset password',
        html: EMAIL_TEMPLATE.FORGOT_PASSWORD,
      },
      {
        fullName: `${user.firstName} ${user.lastName}`,
        resetPassLink: `${callbackURL}?token=${token}`,
      },
    );
    return this.userRepository.save({ id: user.id, resetPasswordToken: token });
  }

  async verifyResetPassword({ token }: VerifyResetPasswordDTO) {
    const getToken = decodeToken(token);
    if (!getToken) {
      throw new BadRequestException('This token is not accepted');
    }
    const params = new URLSearchParams(getToken);
    const user = await this.userRepository.findOne({
      where: {
        id: params.get('userId'),
        resetPasswordToken: token,
      },
    });
    if (!user) {
      throw new BadRequestException('This token is not accepted');
    }
    return true;
  }

  async newPassword({ token, password }: NewPasswordDTO) {
    const getToken = decodeToken(token);
    if (!getToken) {
      throw new BadRequestException('This token is not accepted');
    }
    const params = new URLSearchParams(getToken);
    const user = await this.userRepository.findOne({
      where: {
        id: params.get('userId'),
        resetPasswordToken: token,
      },
    });
    if (!user) {
      throw new BadRequestException('This token is not accepted');
    }
    password = await this.hashService.hash(password);
    return this.userRepository.save({
      id: user.id,
      resetPasswordToken: null,
      password,
    });
  }

  async setPassword(setPasswordDTO: SetPasswordDTO) {
    // const time = this.convertUtcToSingaporeTime(new Date());

    const password = await this.hashService.hash(setPasswordDTO.password);
    // const message = `IP ${setPasswordDTO.ip} set password at ${time.toString()}, from ${setPasswordDTO.country} ${setPasswordDTO.countryCode}, via ${setPasswordDTO.browser}.`;

    return this.userRepository.save({
      id: setPasswordDTO.userId,
      password,
      // changePasswordLogMessage: message,
    });
  }

  private convertUtcToSingaporeTime(utcDate: Date) {
    const date = new Date(utcDate);

    const singaporeTimeZone = 'Asia/Singapore';

    const options: Intl.DateTimeFormatOptions = {
      timeZone: singaporeTimeZone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    };

    const formatter = new Intl.DateTimeFormat('en-US', options);
    return formatter.format(date);
  }

  async changePassword(body: ChangePasswordDTO, id: string) {
    const item = await this.userRepository.findOne({
      where: {
        id,
      },
    });
    const checkOldPassword = await this.hashService.compare(
      body.oldPassword,
      item.password,
    );

    if (!checkOldPassword) {
      throw new BadRequestException('Old password is incorrect');
    }

    body.password = await this.hashService.hash(body.password);
    return this.userRepository.save({ id, password: body.password });
  }

  async updateMe(userId: string, updateMeDto: UpdateMeDto) {
    const data = await this.findOne(userId);
    try {
      if (
        data.photoId !== null &&
        updateMeDto.photoId &&
        data.photoId !== updateMeDto.photoId
      ) {
        if (data.photoId) {
          await this.assetService.remove(data.photoId);
        }
        if (updateMeDto.photoId) {
          await this.assetService.addAssetRelation(
            updateMeDto.photoId,
            EAssetRelation.User,
            data.id,
          );
        }
      }
    } catch (error) {
      console.error(error);
    }

    if (updateMeDto?.measuringId) {
      const userConfig = await this.userConfigRepository.findOne({
        where: {
          userId: userId,
        },
      });
      if (userConfig) {
        await this.userConfigRepository.save({
          id: userConfig.id,
          measuringId: updateMeDto.measuringId,
        });
      }
    }

    await this.userRepository.save({ id: userId, ...updateMeDto });

    if (typeof updateMeDto?.enabledAutoAddNewProject === 'boolean') {
      await this.userFeatureService.updateAutoAddNewProjectFeature(
        userId,
        updateMeDto.enabledAutoAddNewProject,
      );
    }

    return await this.findOne(userId);
  }

  async enableDirectoryFeature() {
    try {
      // Get all users without NAVISVIP pricing plan
      const users = await this.userRepository.find({
        where: {
          pricingPlans: Not('NAVISVIP'),
        },
      });

      // Get directory feature
      const directoryFeature = await this.featureRepository.findOne({
        where: { type: EFeature.Directory },
      });

      if (!directoryFeature) {
        throw new NotFoundException('Directory feature not found');
      }

      // Enable directory feature for each user
      const updates = users.map(async (user) => {
        await this.userFeatureService.create({
          userId: user.id,
          featureId: directoryFeature.id,
          enabled: true,
        });
      });

      await Promise.all(updates);

      return {
        message: 'Directory feature enabled successfully',
        updatedCount: users.length,
      };
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async getUserAdmin() {
    const result = await this.userRepository.findOneBy({
      deletedAt: IsNull(),
      role: Roles.ADMIN,
    });
    if (!result) {
      throw new NotFoundException('User Admin not found');
    }
    return result;
  }

  async updateStatusAccount(
    userId: string,
    statusAccount: 'active' | 'inactive',
  ) {
    const userData = await this.userRepository.findOneBy({
      id: userId,
      deletedAt: IsNull(),
    });
    if (!userData) {
      throw new NotFoundException('User not found');
    }
    const result = await this.userRepository.update(
      { id: userData.id },
      { statusAccount },
    );
    return result;
  }
}
