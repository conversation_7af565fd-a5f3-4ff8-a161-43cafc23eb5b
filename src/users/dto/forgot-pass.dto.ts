import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>S<PERSON>, IsUrl, Min<PERSON>ength } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

export class ForgotPasswordDTO {
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string;

  @IsString()
  @IsNotEmpty()
  @IsUrl({ require_tld: false, require_protocol: true })
  callbackURL: string;
}

export class VerifyResetPasswordDTO {
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class NewPasswordDTO {
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsNotEmpty()
  token: string;
}

export class ChangePasswordDTO {
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @IsNotEmpty()
  password: string;

  @IsNotEmpty()
  oldPassword: string;
}