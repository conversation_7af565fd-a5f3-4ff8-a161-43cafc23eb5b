import { UserStatus } from '../entities/user.entity';
import { IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

class UserFilters {
  email?: string;
  status?: UserStatus;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateCreated?: Date;
}

export enum SortableUserFields {
  Email = 'email',
  Status = 'status',
  DateCreated = 'dateCreated',
  FirstName = 'firstName',
  LastName = 'lastName',
  Phone = 'phone',
}

export class QueryUserDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => UserFilters)
  filter?: UserFilters;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;

  @IsOptional()
  @IsUUID()
  projectId?: string;
}
