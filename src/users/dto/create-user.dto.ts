import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { Roles } from '../entities/user.entity';

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEnum(Roles)
  role: Roles;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  whatsapp?: string;

  @IsUUID()
  @IsOptional()
  photoId?: string;

  @IsString()
  @IsOptional()
  measuringId?: string;

  @IsString()
  @IsOptional()
  company?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  additionalEmail?: string[];

  @IsBoolean()
  @IsOptional()
  isFlowSignUp?: boolean;

  @IsOptional()
  @IsString()
  pricingPlans?: string;

  @IsOptional()
  @IsString()
  statusAccount?: 'active' | 'inactive';

  @IsOptional()
  @IsString()
  adRerportUrl?: string;

  @IsOptional()
  @IsString()
  note?: string;
}
