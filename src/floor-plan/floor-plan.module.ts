import { Module } from '@nestjs/common';
import { FloorPlanService } from './floor-plan.service';
import { FloorPlanController } from './floor-plan.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FloorPlan } from './entities/floor-plan.entity';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { AssetModule } from 'src/asset/asset.module';
import { RedisModule } from 'src/redis/redis.module';
import { Unit } from 'src/unit/entities/unit.entity';
import { Project } from 'src/project/entities/project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([FloorPlan, Unit, Project]),
    UnitTypeModule,
    AssetModule,
    RedisModule,
  ],
  controllers: [FloorPlanController],
  providers: [FloorPlanService],
  exports: [FloorPlanService],
})
export class FloorPlanModule {}
