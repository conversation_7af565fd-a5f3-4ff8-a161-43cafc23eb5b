import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';

@Entity()
export class FloorPlan extends CrudEntity {
  @Column({ type: 'jsonb' })
  name?: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @Column({ type: 'float' })
  area: number;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project, (project) => project.floorPlans)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid', nullable: true })
  photoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo?: Asset;

  @Column({ type: 'uuid' })
  unitTypeId: string;

  @ManyToOne(() => UnitType, (unitType) => unitType.floorPlans)
  @JoinColumn({ name: 'unitTypeId' })
  unitType: UnitType;

  @Column({ default: 0 })
  availableUnits: number;

  @Column({ default: 0 })
  totalUnits: number;

  @Column({ default: 0, type: 'float' })
  minPrice: number;

  @Column({ default: 0, type: 'float' })
  maxPrice: number;

  @OneToMany(() => Unit, (unit) => unit.floorPlan)
  units: Unit[];
}
