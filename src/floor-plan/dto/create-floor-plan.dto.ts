import {
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';

export class CreateFloorPlanDto {
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsPositive()
  area: number;

  @IsUUID()
  projectId: string;

  @IsUUID()
  @IsOptional()
  photoId?: string;

  @IsUUID()
  unitTypeId: string;

  @IsNumber()
  minPrice?: number;

  @IsNumber()
  maxPrice?: number;

  @IsNumber()
  availableUnits?: number;

  @IsNumber()
  totalUnits?: number;
}
