import { OmitType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EFloorPlanSortBy {
  name = 'name',
  area = 'area',
  availableUnits = 'availableUnits',
  totalUnits = 'totalUnits',
  minPrice = 'minPrice',
  maxPrice = 'maxPrice',
  unitTypeId = 'unitTypeId',
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
}

export class QueryFloorPlanDto extends OmitType(PaginationQueryDto, ['lang']) {
  @IsUUID()
  projectId: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EFloorPlanSortBy)
  @IsOptional()
  sortBy?: EFloorPlanSortBy;
}
