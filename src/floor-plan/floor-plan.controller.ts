import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { FloorPlanService } from './floor-plan.service';
import { CreateFloorPlanDto } from './dto/create-floor-plan.dto';
import { UpdateFloorPlanDto } from './dto/update-floor-plan.dto';
import { QueryFloorPlanDto } from './dto/query-floor-plan.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';

@Controller('floor-plan')
export class FloorPlanController {
  constructor(private readonly floorPlanService: FloorPlanService) {}
  @Post()
  create(@Body() createFloorPlanDto: CreateFloorPlanDto) {
    return this.floorPlanService.create(createFloorPlanDto);
  }

  @Get()
  listing(@Query() query: QueryFloorPlanDto) {
    return this.floorPlanService.listing(query);
  }

  @Get(':id')
  getDetail(@Param('id') id: string) {
    return this.floorPlanService.getDetail(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateFloorPlanDto: UpdateFloorPlanDto,
  ) {
    return this.floorPlanService.update(id, updateFloorPlanDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.floorPlanService.delete(id);
  }

  @Public()
  @Post('calculate-unit-slots')
  calculateUnitSlots() {
    return this.floorPlanService.calculateUnitSlots();
  }
}
