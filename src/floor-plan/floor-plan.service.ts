import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import { generateSlug } from 'src/core/utils/slug.util';
import { FloorPlan } from './entities/floor-plan.entity';
import { CreateFloorPlanDto } from './dto/create-floor-plan.dto';
import { UpdateFloorPlanDto } from './dto/update-floor-plan.dto';
import { beautifyObject } from 'src/core/utils/common.util';
import {
  EFloorPlanSortBy,
  QueryFloorPlanDto,
} from './dto/query-floor-plan.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { EOrderType } from 'src/core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';
import { Unit } from 'src/unit/entities/unit.entity';
import { EUnitStatus } from 'src/unit/enums/unit.enum';
import { Project } from 'src/project/entities/project.entity';

@Injectable()
export class FloorPlanService {
  constructor(
    @InjectRepository(FloorPlan)
    private readonly floorPlanRepo: Repository<FloorPlan>,
    private readonly unitTypeService: UnitTypeService,
    private readonly assetService: AssetService,
    private readonly redisService: RedisService,

    @InjectRepository(Unit)
    private readonly unitRepo: Repository<Unit>,

    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
  ) {}

  async create(body: CreateFloorPlanDto) {
    if (body.slug) {
      const existed = await this.floorPlanRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug =
      body.slug ?? (await generateSlug(body.name, this.floorPlanRepo));

    if (body.photoId) {
      const asset = await this.assetService.findOne(body.photoId);
      if (!asset || asset.relationId) {
        throw new UnprocessableEntityException(
          `Asset with id '${asset.id}' already used by other`,
        );
      }
    }

    const data: Partial<FloorPlan> = {
      ...body,
      slug,
      name: { [ELangCode.en]: body.name },
    };

    const item = await this.floorPlanRepo.save(data);

    if (item.photoId) {
      await this.assetService.addAssetRelation(
        item.photoId,
        EAssetRelation.FloorPlan,
        item.id,
      );
    }

    await this.redisService.deletePattern('floor-plan@*');

    return item;
  }

  async getDetail(id: string) {
    const item = await this.floorPlanRepo.findOne({
      where: { id },
      relations: {
        photo: true,
        project: true,
        unitType: true,
      },
    });

    if (!item) {
      throw new NotFoundException('Floor plan is not found');
    }

    return item;
  }

  async listing(query: QueryFloorPlanDto) {
    const pagination = getPaginationOption(query);

    const orderBy = query.sortBy || EFloorPlanSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.floorPlanRepo
      .createQueryBuilder('floorPlan')
      .leftJoinAndSelect('floorPlan.photo', 'photo')
      .leftJoinAndSelect('floorPlan.project', 'project')
      .leftJoinAndSelect('floorPlan.unitType', 'unitType')
      .orderBy(`floorPlan.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.projectId) {
      queryBuilder.andWhere('floorPlan.projectId = :projectId', {
        projectId: query.projectId,
      });
    }

    if (query.name) {
      queryBuilder.andWhere(
        `"floorPlan"."name"->>'${ELangCode.en}' ILIKE :name`,
        { name: `%${query.name}%` },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async update(id: string, body: UpdateFloorPlanDto) {
    const item = await this.floorPlanRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('FloorPlan is not found');
    }

    const langCode = body.lang ?? ELangCode.en;

    if (body.slug && body.slug !== item.slug) {
      const existed = await this.floorPlanRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== item.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    let needRemovePhoto = false;

    if (body.photoId && body.photoId !== item.photoId) {
      await this.assetService.addAssetRelation(
        body.photoId,
        EAssetRelation.FloorPlan,
        body.photoId,
      );
      needRemovePhoto = true;
    }

    const data: Partial<FloorPlan> = {
      ...item,
      ...body,
      name: {
        ...item.name,
        [langCode]: body.name ?? item.name?.[langCode],
      },
    };

    const res = await this.floorPlanRepo.save(data);

    if (needRemovePhoto) {
      await this.assetService.remove(item.photoId).catch(console.error);
    }
    await this.redisService.deletePattern('floor-plan@*');

    return res;
  }

  async delete(id: string) {
    const item = await this.floorPlanRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Floor plan is not found');
    }
    await this.redisService.deletePattern('floor-plan@*');

    return await this.floorPlanRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  _beautify(raw: any) {
    return beautifyObject(raw, ['photo', 'unitType', 'project']);
  }

  formatResponse(item: FloorPlan, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      unitType: this.unitTypeService.formatResponse(
        item.unitType,
        lang,
        fallback,
      ),
    };
  }

  async findByName(projectId: string, unitTypeId: string, name: string) {
    return await this.redisService.cacheWrapper(
      `floor-plan@name:${name}`,
      async () => {
        return await this.floorPlanRepo.findOne({
          where: {
            projectId,
            unitTypeId,
            name: {
              en: name,
            },
          },
        });
      },
    );
  }

  async updateSoldAndAvailableUnits(floorPlanId: string) {
    try {
      Promise.all([
        await this.unitRepo.count({
          where: {
            floorPlanId,
            status: EUnitStatus.Available,
            deletedAt: IsNull(),
          },
        }),
        await this.unitRepo.count({
          where: {
            floorPlanId,
            deletedAt: IsNull(),
          },
        }),
      ]).then(async ([availableUnits, totalUnits]) => {
        await this.floorPlanRepo.update(
          {
            id: floorPlanId,
          },
          { availableUnits, totalUnits },
        );
      });
    } catch (error) {
      console.log(error);
    }
  }

  async updateSoldAndAvailableUnitsByProjectId(projectId: string) {
    try {
      const project = await this.projectRepo.findOne({
        where: { id: projectId },
        relations: ['floorPlans'],
      });

      await Promise.all(
        project?.floorPlans.map(async (floorPlan) => {
          await this.updateSoldAndAvailableUnits(floorPlan.id);
        }),
      );
    } catch (error) {
      console.log(error);
    }
  }

  async calculateUnitSlots() {
    const floorPlans = await this.floorPlanRepo.find();

    Promise.all(
      floorPlans.map(async (floorPlan) => {
        return await this.updateSoldAndAvailableUnits(floorPlan.id);
      }),
    );
  }
}
