import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from './users/users.module';
import appConfig from './core/configs/app.config';
import { IamModule } from './iam/iam.module';
import jwtConfig from './iam/config/jwt.config';
import typeormConfig from './core/configs/typeorm.config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { ResponseFormatInterceptor } from './core/interceptors/response-format.interceptor';
import { ProjectModule } from './project/project.module';
import { CategoryModule } from './category/category.module';
import { DeveloperModule } from './developer/developer.module';
import { AssetModule } from './asset/asset.module';
import { UnitTypeModule } from './unit-type/unit-type.module';
import { SitePlanModule } from './site-plan/site-plan.module';
import { FloorPlanModule } from './floor-plan/floor-plan.module';
import { UnitTransactionModule } from './unit-transaction/unit-transaction.module';
import { ProjectAmenityModule } from './project-amenity/project-amenity.module';
import { UserConfigModule } from './user-config/user-config.module';
import { ContactSaleSubmissionModule } from './contact-sale-submission/contact-sale-submission.module';
import { LocationModule } from './location/location.module';
import { UserProjectModule } from './user-project/user-project.module';
import { PropertyModule } from './property/property.module';
import { DomainsModule } from './domains/domains.module';
import { UserConfigLocationModule } from './user-config-location/user-config-location.module';
import awsConfig from './core/configs/aws.config';
import { RequireHeaderGuard } from './core/guards/require-header.guard';
import { SiteContentModule } from './site-content/site-content.module';
import { PromotionBannerModule } from './promotion-banner/promotion-banner.module';
import { VirtualTourModule } from './virtual-tour/virtual-tour.module';
import { UserPreferenceModule } from './user-preference/user-preference.module';
import { ImportDataModule } from './import-data/import-data.module';
import { SectionModule } from './section/section.module';
import { MailerModule } from './mailer/mailer.module';
import { TemplateModule } from './template/template.module';
import { RedisModule } from './redis/redis.module';
import { MortgageModule } from './mortgage/mortgage.module';
import { AnalyticModule } from './analytic/analytic.module';
import { CommonModule } from './common/common.module';
import domainConfig from './core/configs/domain.config';
import { NotificationModule } from './notification/notification.module';
import { UrlModule } from './url/url.module';
import { EmailTemplateModule } from './email-template/email-template.module';
import { TelegramModule } from './telegram/telegram.module';
import { BotModule } from './bot/bot.module';
import { IpTrackingModule } from './ip-tracking/ip-tracking.module';
import { UnitModule } from './unit/unit.module';
import { LoginActivityModule } from './login-activity/login-activity.module';
import { FeatureModule } from './feature/feature.module';
import { UserFeatureModule } from './user-feature/user-feature.module';
import { ScheduleModule } from '@nestjs/schedule';
import { TelegramJob } from './cron-job/telegram/telegram.job';
import { TelegramJobModule } from './cron-job/telegram/telegram.module';
import { UserProjectLandingPageModule } from './user-project-landing-page/user-project-landing-page.module';
import { MarketingModule } from './marketing/marketing.module';
import { RoundRobinModule } from './round-robin/round-robin.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BullModule } from '@nestjs/bull';
import { UmamiModule } from './umami/umami.module';
import { UserSuspendModule } from './user-suspend/user-suspend.module';
import { XeroPaymentModule } from './xero-payment/xero-payment.module';
import { PackageModule } from './package/package.module';
import { UserPackageModule } from './user-package/user-package.module';
import { LandingpagesControlModule } from './landingpages-control/landingpages-control.module';
import { InvoiceModule } from './invoice/invoice.module';
import { PromotionModule } from './promotion/promotion.module';
import { PromotionUsageModule } from './promotion-usage/promotion-usage.module';
import { XeroPaymentHistoryModule } from './xero-payment-history/xero-payment-history.module';
import { ApiKeyModule } from './api-key/api-key.module';
import { LoggerAppModule } from './logger-app/logger-app.module';
import { UserFlagModule } from './user-flag/user-flag.module';
import { ScriptModule } from './script/script.module';
import { HCaptchaAppModule } from './hcaptcha-app/hcaptcha-app.module';
import { NotificationAppModule } from './notification-app/notification-app.module';
import { ExportDataModule } from './export-data/export-data.module';

@Module({
  imports: [
    BullModule.forRoot({
      redis: process.env.REDIS_URI,
    }),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, typeormConfig, jwtConfig, awsConfig, domainConfig],
    }),
    DatabaseModule,
    UsersModule,
    IamModule,
    ProjectModule,
    CategoryModule,
    DeveloperModule,
    AssetModule,
    UnitTypeModule,
    SitePlanModule,
    FloorPlanModule,
    UnitTransactionModule,
    ProjectAmenityModule,
    UserConfigModule,
    ContactSaleSubmissionModule,
    LocationModule,
    UserProjectModule,
    PropertyModule,
    DomainsModule,
    UserConfigLocationModule,
    SiteContentModule,
    PromotionBannerModule,
    VirtualTourModule,
    UserPreferenceModule,
    ImportDataModule,
    SectionModule,
    MailerModule,
    TemplateModule,
    RedisModule,
    MortgageModule,
    AnalyticModule,
    CommonModule,
    NotificationModule,
    UrlModule,
    EmailTemplateModule,
    TelegramModule,
    BotModule,
    IpTrackingModule,
    UnitModule,
    LoginActivityModule,
    FeatureModule,
    UserFeatureModule,
    TelegramJobModule,
    UserProjectLandingPageModule,
    MarketingModule,
    RoundRobinModule,
    UmamiModule,
    UserSuspendModule,
    XeroPaymentModule,
    PackageModule,
    UserPackageModule,
    LandingpagesControlModule,
    InvoiceModule,
    PromotionModule,
    PromotionUsageModule,
    XeroPaymentHistoryModule,
    ApiKeyModule,
    LoggerAppModule,
    UserFlagModule,
    ScriptModule,
    HCaptchaAppModule,
    NotificationAppModule,
    ExportDataModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseFormatInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: RequireHeaderGuard,
    },
    AppService,
    TelegramJob,
  ],
})
export class AppModule {}
