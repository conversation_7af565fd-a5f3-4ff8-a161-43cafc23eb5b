import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MortgageService } from './mortgage.service';
import { MortgageController } from './mortgage.controller';
import { Mortgage } from './entities/mortgage.entity';
import { AssetModule } from '../asset/asset.module';

@Module({
  imports: [TypeOrmModule.forFeature([Mortgage]), AssetModule],
  controllers: [MortgageController],
  providers: [MortgageService],
  exports: [MortgageService],
})
export class MortgageModule {}
