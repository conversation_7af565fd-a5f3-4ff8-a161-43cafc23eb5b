import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mortgage } from './entities/mortgage.entity';
import { ELangCode } from '../core/enums/lang.enum';
import { EAssetRelation } from '../asset/enums/asset.enum';
import { CreateMortgageDto } from './dto/create-mortgage.dto';
import { AssetService } from '../asset/asset.service';
import { EDeveloperSortBy } from '../developer/dto/developer-query.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from '../core/utils/pagination.util';
import { EOrderType } from '../core/enums/sort.enum';
import { ListingMortgageQueryDto } from './dto/mortgage-query.dto';
import { UpdateMortgageDto } from './dto/update-mortgage.dto';
import { EMortgageType } from './enums/mortgage.enum';
import { forEach } from 'lodash';

@Injectable()
export class MortgageService {
  constructor(
    @InjectRepository(Mortgage)
    private readonly mortgageRepo: Repository<Mortgage>,
    private readonly assetService: AssetService,
  ) {}

  async create(body: CreateMortgageDto) {
    const data = {
      ...body,
      type: body.mortgageType,
    };
    const mortgage = await this.mortgageRepo.save(data);
    try {
      if (body.bankLogoId) {
        await this.assetService.addAssetRelation(
          body.bankLogoId,
          EAssetRelation.MortgageBank,
          mortgage.id,
        );
      }
    } catch (e) {
      console.log('update mortgage bank logo failed cause', e);
    }

    return mortgage;
  }

  async update(id: string, body: UpdateMortgageDto) {
    const langCode = body.lang ?? ELangCode.en;
    const mortgage = await this.mortgageRepo.findOneBy({ id });

    if (!mortgage) {
      throw new NotFoundException('Mortgage is not found');
    }

    const data = {
      ...body,
      type: body.mortgageType,
    };

    if (
      data.bankLogoId !== undefined &&
      data.bankLogoId !== mortgage.bankLogoId
    ) {
      if (mortgage.bankLogoId) {
        await this.assetService.remove(mortgage.bankLogoId);
      }
      if (data.bankLogoId) {
        await this.assetService.addAssetRelation(
          data.bankLogoId,
          EAssetRelation.MortgageBank,
          mortgage.id,
        );
      }
    }

    return await this.mortgageRepo.save({
      ...mortgage,
      ...data,
    });
  }

  async delete(id: string) {
    const item = await this.mortgageRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Mortgage is not found');
    }
    return await this.mortgageRepo.update({ id }, { deletedAt: new Date() });
  }

  async listing(query: ListingMortgageQueryDto) {
    const pagination = getPaginationOption(query);
    const orderBy = query.sortBy || EDeveloperSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;
    const queryBuilder = this.mortgageRepo
      .createQueryBuilder('mortgage')
      .leftJoinAndSelect('mortgage.bankLogo', 'bankLogo');

    if (query.type) {
      queryBuilder.where('mortgage.type = :type', { type: query.type });
    }

    queryBuilder.orderBy(`mortgage.${orderBy}`, sorting);
    queryBuilder.offset(pagination.offset);
    queryBuilder.limit(pagination.limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    return createPaginationResponse(data, total, pagination);
  }

  async countType() {
    return await this.mortgageRepo
      .createQueryBuilder('mortgage')
      .select('mortgage.type', 'type')
      .addSelect('COUNT(*)::int', 'count')
      .groupBy('mortgage.type')
      .orderBy(
        `array_position(array['${EMortgageType.HDB}', '${EMortgageType.Condo}', '${EMortgageType.BUC}'], mortgage.type::varchar)`,
      )
      .getRawMany();
  }

  async findMortgageToImport(name: string, pkg: string, type: EMortgageType) {
    return await this.mortgageRepo
      .createQueryBuilder('mortgage')
      .where(
        `mortgage.bankName ILIKE :name`,
        { name: `%${name}%` },
      )
      .andWhere(
        `mortgage.package ILIKE :package`,
        { package: `%${pkg}%` },
      )
      .andWhere('mortgage.type = :type', { type })
      .getOne();
  }

  async deleteByArrayId(ids: Array<string>) {
    forEach(ids, async (id) => {
      const item = await this.mortgageRepo.findOneBy({ id });
      if (!item) {
        throw new NotFoundException('Mortgage is not found');
      }
      if (item.bankLogoId) {
        await this.assetService.remove(item.bankLogoId);
      }
    });
    return await this.mortgageRepo
      .createQueryBuilder('mortgage')
      .delete()
      .where('id IN (:...ids)', { ids })
      .execute();
  }

  async getIds() {
    const ids = await this.mortgageRepo
      .createQueryBuilder('mortgage')
      .select('mortgage.id')
      .getMany();
    return ids.map((id) => id.id);
  }

  async findBankToGetBankLogo(name: string) {
    return await this.mortgageRepo
      .createQueryBuilder('mortgage')
      .leftJoinAndSelect('mortgage.bankLogo', 'bankLogo')
      .where(
        `mortgage.bankName ILIKE :name`,
        { name: `%${name}%` },
      )
      .andWhere('mortgage.bankLogoId IS NOT NULL')
      .getOne();
  }
}
