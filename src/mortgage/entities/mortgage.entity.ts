import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, JoinColumn, OneToOne } from 'typeorm';
import { EMortgageType } from '../enums/mortgage.enum';
import { RateDto } from '../dto/rate.dto';

@Entity()
export class Mortgage extends CrudEntity {
  @Column({
    type: 'enum',
    enum: EMortgageType,
    default: EMortgageType.HDB,
  })
  type: EMortgageType;

  @Column()
  bankName: string;

  @Column({ nullable: true, type: 'uuid' })
  bankLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'bankLogoId' })
  bankLogo?: Asset;

  @Column()
  @Column({ type: 'jsonb', nullable: true })
  firstYear?: RateDto;

  @Column()
  @Column({ type: 'jsonb', nullable: true })
  secondYear?: RateDto;

  @Column()
  @Column({ type: 'jsonb', nullable: true })
  threeYear?: RateDto;

  @Column()
  @Column({ type: 'jsonb', nullable: true })
  thereAfter?: RateDto;

  @Column({ nullable: true })
  package?: string;

  @Column({ nullable: true })
  applyDate?: Date;

  @Column({ nullable: true, type: 'text' })
  remarks?: string;

}
