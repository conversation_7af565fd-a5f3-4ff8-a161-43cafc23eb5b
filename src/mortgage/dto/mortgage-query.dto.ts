import { IsEnum, IsOptional } from 'class-validator';
import { OmitType } from '@nestjs/mapped-types';
import { PaginationQueryDto } from '../../core/dto/pagination-query.dto';
import { EOrderType } from '../../core/enums/sort.enum';
import { EMortgageType } from '../enums/mortgage.enum';

export enum EMortgageSortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  name = 'name',
}

export class ListingMortgageQueryDto extends OmitType(PaginationQueryDto, [
  'lang',
]) {
  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EMortgageSortBy)
  @IsOptional()
  sortBy?: EMortgageSortBy;

  @IsEnum(EMortgageType)
  @IsOptional()
  type?: EMortgageType;
}
