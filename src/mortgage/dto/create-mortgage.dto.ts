import { IsDate, IsEnum, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { EMortgageType } from '../enums/mortgage.enum';
import { RateDto } from './rate.dto';

export class CreateMortgageDto {
  @IsString()
  @IsOptional()
  bankName?: string;

  @IsUUID()
  @IsOptional()
  bankLogoId?: string;

  @IsEnum(EMortgageType)
  mortgageType: EMortgageType;

  @IsOptional()
  @ValidateNested()
  firstYear?: RateDto;

  @ValidateNested()
  @IsOptional()
  secondYear?: RateDto;

  @ValidateNested()
  @IsOptional()
  threeYear?: RateDto;
  
  @ValidateNested()
  @IsOptional()
  thereAfter?: RateDto;

  @IsString()
  @IsOptional()
  package?: string;

  @IsDate()
  @IsOptional()
  applyDate?: Date;

  @IsString()
  @IsOptional()
  remarks?: string;

}
