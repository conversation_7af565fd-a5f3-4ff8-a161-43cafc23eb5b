import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { MortgageService } from './mortgage.service';
import { RolesGuard } from '../iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from '../iam/authentication/decorators/role.decorator';
import { Roles } from '../users/entities/user.entity';
import { CreateMortgageDto } from './dto/create-mortgage.dto';
import { ListingMortgageQueryDto } from './dto/mortgage-query.dto';
import { Public } from '../iam/authentication/decorators/auth.decorator';
import { UpdateMortgageDto } from './dto/update-mortgage.dto';

@Controller('mortgage')
@UseGuards(RolesGuard)
export class MortgageController {
  constructor(private readonly mortgageService: MortgageService) {}

  @Post()
  @AccessRoles(Roles.ADMIN)
  create(@Body() body: CreateMortgageDto) {
    return this.mortgageService.create(body);
  }

  @Get('listing')
  @Public()
  listing(@Query() query: ListingMortgageQueryDto) {
    return this.mortgageService.listing(query);
  }

  @Patch(':id')
  @AccessRoles(Roles.ADMIN)
  update(@Param('id') id: string, @Body() body: UpdateMortgageDto) {
    return this.mortgageService.update(id, body);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  remove(@Param('id') id: string) {
    return this.mortgageService.delete(id);
  }

  @Get('count-type')
  @Public()
  countType() {
    return this.mortgageService.countType();
  }
}
