import CrudEntity from 'src/core/entities/crud.entity';
import { Colum<PERSON>, En<PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { ETemplateType } from '../enums/template.enum';
import { User } from '../../users/entities/user.entity';

@Entity()
export class Template extends CrudEntity {
  @ManyToOne(() => User)
  user: User;

  @Column()
  subject: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: ETemplateType,
    default: ETemplateType.WhatsApp,
  })
  type: ETemplateType;

  @Column({
    default: false,
  })
  isAdmin: boolean;
}
