import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { TemplateService } from './template.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { RolesGuard } from '../iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from '../iam/authentication/decorators/role.decorator';
import { Roles } from '../users/entities/user.entity';
import { ActiveUser } from '../iam/authentication/decorators/active-user.decorator';
import { QueryTemplatePreferenceDto } from './dto/query-template-preference.dto';

@Controller('template')
@UseGuards(RolesGuard)
export class TemplateController {
  constructor(private readonly templateService: TemplateService) {}

  @Post()
  @AccessRoles(Roles.ADMIN)
  createOne(
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: string,
    @Body() body: CreateTemplateDto,
  ) {
    return this.templateService.createOne(body, userId, role);
  }

  @Post('by-agency')
  @AccessRoles(Roles.AGENCY)
  createByAgency(
    @ActiveUser('sub') userId: string,
    @Body() body: CreateTemplateDto,
  ) {
    return this.templateService.createByAgency(body, userId);
  }

  @Get('by-admin')
  @AccessRoles(Roles.ADMIN)
  findAllByAdmin(@Query() query: QueryTemplatePreferenceDto) {
    return this.templateService.findAllByAdmin(query);
  }

  @Get('by-agency')
  @AccessRoles(Roles.AGENCY)
  listingByAgency(
    @ActiveUser('sub') userId: string,
    @Query() query: QueryTemplatePreferenceDto,
  ) {
    return this.templateService.listingByAgency(userId, query);
  }

  @Get('all')
  @AccessRoles(Roles.AGENCY)
  allByAgency(@ActiveUser('sub') userId: string) {
    return this.templateService.agencyGetAll(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.templateService.findOne(id);
  }

  @Patch(':id')
  @AccessRoles(Roles.ADMIN)
  update(@Param('id') id: string, @Body() body: UpdateTemplateDto) {
    return this.templateService.update(id, body);
  }

  @Patch(':id/by-agency')
  @AccessRoles(Roles.AGENCY)
  updateByAgency(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() body: UpdateTemplateDto,
  ) {
    return this.templateService.updateByAgency(id, body, userId);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  remove(@Param('id') id: string) {
    return this.templateService.remove(id);
  }

  @Delete(':id/by-agency')
  @AccessRoles(Roles.AGENCY)
  removeByAgency(@ActiveUser('sub') userId: string, @Param('id') id: string) {
    return this.templateService.removeByAgency(id, userId);
  }
}
