import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { Template } from './entities/template.entity';
import { Roles } from '../users/entities/user.entity';
import { QueryTemplatePreferenceDto } from './dto/query-template-preference.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from '../core/utils/pagination.util';

@Injectable()
export class TemplateService {
  constructor(
    @InjectRepository(Template)
    private readonly templateRepo: Repository<Template>,
  ) {}

  async createOne(body: CreateTemplateDto, userId: string, role: string) {
    await this.templateRepo.save({
      subject: body.subject,
      type: body.type,
      content: body.content,
      user: {
        id: userId,
      },
      isAdmin: role === Roles.ADMIN,
    });
    return 'This action adds a new template';
  }

  async createByAgency(body: CreateTemplateDto, userId: string) {
    await this.templateRepo.save({
      subject: body.subject,
      type: body.type,
      content: body.content,
      user: {
        id: userId,
      },
    });
    return 'This action adds a new template';
  }

  async agencyGetAll(userId: string) {
    const queryBuilder = this.templateRepo
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.user', 'user')
      .where('user.id = :userId', { userId })
      .orWhere('template.isAdmin = :isAdmin', { isAdmin: true });

    return await queryBuilder.getMany();
  }

  findOne(id: string) {
    return this.templateRepo.findOne({ where: { id } });
  }

  async findAllByAdmin(query: QueryTemplatePreferenceDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.templateRepo
      .createQueryBuilder('template')
      .where('template.isAdmin = :isAdmin', { isAdmin: true })
    if (query.sortBy) {
      queryBuilder.orderBy(`template.${query.sortBy}`, query.sort || 'ASC');
    }
    queryBuilder.limit(pagination.limit)
    queryBuilder.offset(pagination.offset);

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async listingByAgency(userId: string, query: QueryTemplatePreferenceDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.templateRepo
      .createQueryBuilder('template')
      .leftJoinAndSelect('template.user', 'user')
      .where('user.id = :userId', { userId })
      .orWhere('template.isAdmin = :isAdmin', { isAdmin: true })
      if (query.sortBy) {
        queryBuilder.orderBy(`template.${query.sortBy}`, query.sort || 'ASC');
      }
      queryBuilder.limit(pagination.limit)
      queryBuilder.offset(pagination.offset);

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async update(id: string, body: UpdateTemplateDto) {
    const item = await this.templateRepo.findOne({ where: { id } });
    if (!item) {
      throw new NotFoundException('Template is not found');
    }

    return this.templateRepo.save({ ...item, ...body });
  }

  async updateByAgency(id: string, body: UpdateTemplateDto, userId: string) {
    const item = await this.templateRepo.findOne({
      where: {
        id,
        user: {
          id: userId,
        },
      },
    });
    if (!item) {
      throw new NotFoundException('Template is not found');
    }

    return this.templateRepo.save({ ...item, ...body });
  }

  async remove(id: string) {
    const item = await this.templateRepo.findOne({
      where: { id, isAdmin: true },
    });
    if (!item) {
      throw new NotFoundException('Template is not found');
    }
    return this.templateRepo.softRemove(item);
  }

  async removeByAgency(id: string, userId: string) {
    const item = await this.templateRepo.findOne({
      where: {
        id,
        user: {
          id: userId,
        },
      },
    });
    if (!item) {
      throw new NotFoundException('Template is not found');
    }
    return this.templateRepo.softRemove(item);
  }
}
