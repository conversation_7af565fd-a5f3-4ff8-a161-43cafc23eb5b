import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { LandingPageOptionMapping } from '../layout-options/entities/landing_page_option_mapping.entity';

export enum EUserProjectLandingPageDomainType {
  SUB_DOMAIN = 'subdomain',
  CUSTOM_DOMAIN = 'custom',
}

@Entity()
export class UserProjectLandingPage extends CrudEntity {
  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'text' }) // Active Domain: subDomain | cnamTargetDomain
  domain: string;

  @Column({ type: 'text', nullable: true }) // Sub domain : *.directbooking.sg
  subDomain: string;

  @Column({ type: 'text', nullable: true }) // CNAME Target Domain: [unique-sub-domain].directbooking.sg - This one can be changed with each record
  cNameTargetDomain: string;

  @Column({ type: 'text', nullable: true }) // Save user's custom domain to display in the UI, use the cNameTargetDomain for the actual domain
  customDomain: string;

  @Column({ type: 'text', nullable: true })
  name: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  layout: Record<string, string>;

  @Column({ type: 'text', nullable: true })
  type: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  layoutOption: Record<string, string>;

  @OneToOne(() => UmamiWebsites)
  umamiWebsites?: UmamiWebsites;

  @OneToMany(() => LandingPageOptionMapping, (mapping) => mapping.landingPage)
  optionMappings: LandingPageOptionMapping[];

  @Column({ nullable: true })
  headerScript?: string;

  @Column({ nullable: true })
  bodyScript?: string;

  @Column({ nullable: true })
  enquiryScript?: string;

  @Column({ default: true })
  useEnquiryScript?: boolean;

  @Column({ type: 'json', nullable: true })
  dataScript?: Record<string, any>;

  @Column({ nullable: true })
  pixelID?: string;
}
