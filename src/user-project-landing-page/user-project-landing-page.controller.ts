import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { UserProjectLandingPageService } from './user-project-landing-page.service';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import {
  CreateUserProjectLandingPageDto,
  GenerateUniqueDomainDto,
  UpdateUserProjectLandingPageDto,
} from './dto/create-user-project-landing-page.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { QueryUserProjectLandingPage } from './dto/query-user-project-landing-page.dto';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { TriggerDefaultLandingPageOptionDto } from './dto/trigger-default-landing-page-option.dto';
import { QueryExistsUserProjectLandingPage } from './dto/query-exists-user-project-landing-page.dto';

@Controller('user-project-landing-page')
@CacheOption('user-project-landing-page')
export class UserProjectLandingPageController {
  constructor(
    private readonly userProjectLandingPageService: UserProjectLandingPageService,
  ) {}

  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createUserProjectLandingPageDto: CreateUserProjectLandingPageDto,
  ) {
    return this.userProjectLandingPageService.create(
      userId,
      createUserProjectLandingPageDto,
    );
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.userProjectLandingPageService.delete(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() body: UpdateUserProjectLandingPageDto,
  ) {
    return this.userProjectLandingPageService.update(id, body);
  }

  @Public()
  @Get('get-by-domain/:domain')
  getByDomain(@Param('domain') domain: string) {
    return this.userProjectLandingPageService.getByDomain(domain);
  }

  @Get('get-by-project/:projectId')
  getByProjectId(
    @ActiveUser('sub') userId: string,
    @Param('projectId') projectId: string,
    @Query() query: QueryUserProjectLandingPage,
  ) {
    return this.userProjectLandingPageService.getByProjectId(
      userId,
      projectId,
      query,
    );
  }

  @Get()
  listing(@Query() query: QueryUserProjectLandingPage) {
    return this.userProjectLandingPageService.listing(query);
  }

  @Post('unique-domain')
  async generateUniqueDomain(
    @ActiveUser('sub') userId: string,
    @Body() body: GenerateUniqueDomainDto,
  ): Promise<string> {
    return await this.userProjectLandingPageService.generateUniqueDomain(
      userId,
      body,
    );
  }

  @AccessRoles(Roles.ADMIN)
  @Post('trigger-default-option')
  async triggerDefaultOption(@Body() body: TriggerDefaultLandingPageOptionDto) {
    return await this.userProjectLandingPageService.triggerDefaultOption(body);
  }

  @Get('exists')
  async getExists(
    @Query() query: QueryExistsUserProjectLandingPage,
  ): Promise<boolean> {
    return await this.userProjectLandingPageService.getExists(query);
  }

  @Post('unique-cname-target-domain')
  async generateUniqueCnameTargetDomain(
    @ActiveUser('sub') userId: string,
    @Body() body: GenerateUniqueDomainDto,
  ) {
    return await this.userProjectLandingPageService.generateCnameTargetDomain({
      ...body,
      userId: body?.userId || userId, // Admin can generate cname target domain for other user, otherwise get id from current user
    });
  }

  @AccessRoles(Roles.ADMIN)
  @Post('auto-generate-cname-target-domain')
  async autoGenerateCnameTargetDomainIfNotExist() {
    return await this.userProjectLandingPageService.autoGenerateCnameTargetDomainIfNotExist();
  }

  @Post('check-cname-connection')
  async checkCnameConnection(@Body() body: { domain: string }) {
    return await this.userProjectLandingPageService.checkCnameConnection(
      body.domain,
    );
  }

  @AccessRoles(Roles.ADMIN)
  @Post('script-auto-fill-subdomain-or-custom-domain')
  async scriptAutoFillSubdomainOrCustomDomain() {
    return await this.userProjectLandingPageService.autoFillSubdomainOrCustomDomain();
  }
}
