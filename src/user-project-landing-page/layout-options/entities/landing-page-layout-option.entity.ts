import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { LandingPageOptionMapping } from './landing_page_option_mapping.entity';

@Entity()
export class LandingPageLayoutOption extends CrudEntity {
  @Column({ type: 'text' })
  name: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  option: Record<string, string>;

  @OneToMany(
    () => LandingPageOptionMapping,
    (mapping) => mapping.landingPageOption,
  )
  landingPageMappings: LandingPageOptionMapping[];
}
