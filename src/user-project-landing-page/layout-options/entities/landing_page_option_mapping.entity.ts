import CrudEntity from 'src/core/entities/crud.entity';
import { UserProjectLandingPage } from 'src/user-project-landing-page/entities/user-project-landing-page.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { LandingPageLayoutOption } from './landing-page-layout-option.entity';

@Entity()
export class LandingPageOptionMapping extends CrudEntity {
  @ManyToOne(
    () => UserProjectLandingPage,
    (landingPage) => landingPage.optionMappings,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'landingPageId' })
  landingPage: UserProjectLandingPage;

  @ManyToOne(
    () => LandingPageLayoutOption,
    (option) => option.landingPageMappings,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'landingPageOptionId' })
  landingPageOption: LandingPageLayoutOption;

  @Column({ default: false })
  active: boolean;
}
