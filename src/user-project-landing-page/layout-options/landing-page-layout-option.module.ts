import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LandingPageOptionMapping } from './entities/landing_page_option_mapping.entity';
import { LandingPageLayoutOption } from './entities/landing-page-layout-option.entity';
import { LandingPageLayoutOptionService } from './landing-page-layout-option.service';
import { LandingPageLayoutOptionController } from './landing-page-layout-option.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LandingPageOptionMapping,
      LandingPageLayoutOption,
    ]),
  ],
  controllers: [LandingPageLayoutOptionController],
  providers: [LandingPageLayoutOptionService],
  exports: [LandingPageLayoutOptionService],
})
export class LandingPageLayoutOptionModule {}
