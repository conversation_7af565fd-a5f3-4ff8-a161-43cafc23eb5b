import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { LandingPageLayoutOptionService } from './landing-page-layout-option.service';
import {
  AttachLandingPageOptionDto,
  CreateLandingPageLayoutOptionDto,
  QueryLandingPageLayoutOption,
} from './dto/landing-page-layout-option.dto';

@Controller('landing-page-layout-option')
export class LandingPageLayoutOptionController {
  constructor(
    private readonly landingPageLayoutOptionService: LandingPageLayoutOptionService,
  ) {}

  @Post()
  createLayoutOption(
    @Body() createLandingPageLayoutOptionDto: CreateLandingPageLayoutOptionDto,
  ) {
    return this.landingPageLayoutOptionService.createLayoutOption(
      createLandingPageLayoutOptionDto,
    );
  }

  @Delete(':id')
  deleteLayoutOption(@Param('id') id: string) {
    return this.landingPageLayoutOptionService.deleteLayoutOption(id);
  }

  @Get()
  getLayoutOption(@Query() query: QueryLandingPageLayoutOption) {
    return this.landingPageLayoutOptionService.getLayoutOption(query);
  }

  @Post('attach-landing-page-option')
  attachLandingPageOption(
    @Body() attachLandingPageOptionDto: AttachLandingPageOptionDto,
  ) {
    return this.landingPageLayoutOptionService.attachLandingPageOption(
      attachLandingPageOptionDto,
    );
  }

  @Post('detach-landing-page-option')
  detachLandingPageOption(
    @Body() attachLandingPageOptionDto: AttachLandingPageOptionDto,
  ) {
    return this.landingPageLayoutOptionService.detachLandingPageOption(
      attachLandingPageOptionDto,
    );
  }

  @Post('change-landing-page-option')
  changeLandingPageOption(
    @Body() attachLandingPageOptionDto: AttachLandingPageOptionDto,
  ) {
    return this.landingPageLayoutOptionService.changeLandingPageOption(
      attachLandingPageOptionDto,
    );
  }
}
