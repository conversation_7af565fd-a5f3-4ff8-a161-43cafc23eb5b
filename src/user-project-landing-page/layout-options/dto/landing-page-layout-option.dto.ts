import {
  IsBoolean,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class CreateLandingPageLayoutOptionDto {
  @IsString()
  name: string;

  @IsNotEmpty()
  option: Record<string, string>;
}

export class QueryLandingPageLayoutOption extends PaginationQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };
}

export class AttachLandingPageOptionDto {
  @IsUUID()
  landingPageId: string;

  @IsUUID()
  layoutOptionId: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;
}
