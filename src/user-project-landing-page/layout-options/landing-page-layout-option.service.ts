import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LandingPageLayoutOption } from './entities/landing-page-layout-option.entity';
import { LandingPageOptionMapping } from './entities/landing_page_option_mapping.entity';
import {
  AttachLandingPageOptionDto,
  CreateLandingPageLayoutOptionDto,
  QueryLandingPageLayoutOption,
} from './dto/landing-page-layout-option.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';

@Injectable()
export class LandingPageLayoutOptionService {
  constructor(
    @InjectRepository(LandingPageLayoutOption)
    private readonly optionRepo: Repository<LandingPageLayoutOption>,

    @InjectRepository(LandingPageOptionMapping)
    private readonly landingPageOptionMappingRepo: Repository<LandingPageOptionMapping>,
  ) {}

  async createLayoutOption(body: CreateLandingPageLayoutOptionDto) {
    return await this.optionRepo.save(body);
  }

  async deleteLayoutOption(id: string) {
    return await this.optionRepo.delete(id);
  }

  async getLayoutOption(query: QueryLandingPageLayoutOption) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.optionRepo
      .createQueryBuilder('layoutOption')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere('layoutOption.name = :name', {
        name: query.search,
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`layoutOption.${key}`, order);
      });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(data.entities, total, pagination);
  }

  async attachLandingPageOption(body: AttachLandingPageOptionDto) {
    const exist = await this.landingPageOptionMappingRepo.findOne({
      where: {
        landingPage: { id: body.landingPageId },
        landingPageOption: { id: body.layoutOptionId },
      },
    });

    if (exist) {
      return exist;
    }

    return await this.landingPageOptionMappingRepo.save({
      landingPage: { id: body.landingPageId },
      landingPageOption: { id: body.layoutOptionId },
      active: body.active,
    });
  }

  async detachLandingPageOption(body: AttachLandingPageOptionDto) {
    const mapping = await this.landingPageOptionMappingRepo.findOne({
      where: {
        landingPage: { id: body.landingPageId },
        landingPageOption: { id: body.layoutOptionId },
      },
    });

    if (!mapping) {
      throw new NotFoundException('Mapping record not found');
    }

    return await this.landingPageOptionMappingRepo.delete(mapping.id);
  }

  async changeLandingPageOption(body: AttachLandingPageOptionDto) {
    const mapping = await this.landingPageOptionMappingRepo.findOne({
      where: {
        landingPage: { id: body.landingPageId },
        landingPageOption: { id: body.layoutOptionId },
      },
    });

    if (!mapping) {
      throw new NotFoundException('Mapping record not found');
    }

    const existActive = await this.landingPageOptionMappingRepo.find({
      where: {
        landingPage: { id: body.landingPageId },
        active: true,
      },
    });

    await Promise.all(
      existActive.map(async (record) => {
        await this.landingPageOptionMappingRepo.update(
          { id: record.id },
          { active: false },
        );
      }),
    );

    mapping.active = body.active || mapping.active;

    return await this.landingPageOptionMappingRepo.save(mapping);
  }
}
