import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  EUserProjectLandingPageDomainType,
  UserProjectLandingPage,
} from './entities/user-project-landing-page.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Like, Repository } from 'typeorm';
import {
  CreateUserProjectLandingPageDto,
  GenerateUniqueDomainDto,
  UpdateUserProjectLandingPageDto,
} from './dto/create-user-project-landing-page.dto';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { QueryUserProjectLandingPage } from './dto/query-user-project-landing-page.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { Domain } from 'src/domains/entities/domain.entity';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { Roles, User } from 'src/users/entities/user.entity';
import { UserConfigService } from 'src/user-config/user-config.service';
import { PropertyService } from 'src/property/property.service';
import * as crypto from 'crypto';
import { Project } from 'src/project/entities/project.entity';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { UmamiService } from 'src/umami/umami.service';
import { LandingPageLayoutOption } from './layout-options/entities/landing-page-layout-option.entity';
import { LandingPageOptionMapping } from './layout-options/entities/landing_page_option_mapping.entity';
import { TriggerDefaultLandingPageOptionDto } from './dto/trigger-default-landing-page-option.dto';
import { Asset } from 'src/asset/entities/asset.entity';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';
import { QueryExistsUserProjectLandingPage } from './dto/query-exists-user-project-landing-page.dto';
import { DomainsService } from 'src/domains/domains.service';

@Injectable()
export class UserProjectLandingPageService {
  constructor(
    @InjectRepository(UserProjectLandingPage)
    private readonly repo: Repository<UserProjectLandingPage>,

    @InjectRepository(LandingPageLayoutOption)
    private readonly landingPageLayoutOptionRepo: Repository<LandingPageLayoutOption>,

    @InjectRepository(LandingPageOptionMapping)
    private readonly landingPageOptionMappingRepo: Repository<LandingPageOptionMapping>,

    @InjectRepository(UserProject)
    private readonly userProjectRepo: Repository<UserProject>,

    @InjectRepository(Domain)
    private readonly domainRepo: Repository<Domain>,

    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    private readonly userConfigService: UserConfigService,

    private readonly propertyService: PropertyService,

    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,

    @InjectRepository(UmamiMapping)
    private readonly umamiMappingRepo: Repository<UmamiMapping>,

    private readonly umamiService: UmamiService,

    @InjectRepository(UmamiWebsites)
    private readonly umamiWebsitesRepo: Repository<UmamiWebsites>,

    @InjectRepository(LandingpagesControl)
    private readonly landingPagesControlRepo: Repository<LandingpagesControl>,

    private readonly assetService: AssetService,

    private readonly domainsService: DomainsService,
  ) {}

  async checkIsValidDomain(
    {
      type,
      subDomain,
      customDomain,
      cNameTargetDomain,
    }: {
      type: EUserProjectLandingPageDomainType;
      subDomain?: string;
      customDomain?: string;
      cNameTargetDomain?: string;
    },
    excludeId?: string,
  ) {
    if (!type) {
      throw new BadRequestException('Type is required');
    }

    const isSubDomain = type === EUserProjectLandingPageDomainType.SUB_DOMAIN;

    const isValidSubDomain = isSubDomain && subDomain;

    const isValidCustomDomain = !isSubDomain && customDomain;

    if (!isValidSubDomain && !isValidCustomDomain) {
      if (isSubDomain) {
        throw new BadRequestException('Sub domain is required');
      } else {
        throw new BadRequestException('Custom domain is required');
      }
    }

    if (!isSubDomain) {
      // Check custom domain existence, excluding the current record if excludeId is provided
      const customDomainQuery = this.repo
        .createQueryBuilder('lp')
        .where('lp.customDomain = :customDomain', {
          customDomain: customDomain.toLowerCase(),
        });

      if (excludeId) {
        customDomainQuery.andWhere('lp.id != :excludeId', { excludeId });
      }

      const existedCustomDomain = await customDomainQuery.getOne();

      if (existedCustomDomain) {
        throw new UnprocessableEntityException(
          'Custom domain is already existed',
        );
      }
    }

    const activeDomain = isSubDomain
      ? subDomain.toLowerCase()
      : customDomain.toLowerCase();

    // Check active domain (subDomain or cNameTargetDomain) existence in landing pages, excluding the current record if excludeId is provided
    const domainQuery = this.repo
      .createQueryBuilder('lp')
      .where(
        '(lp.subDomain = :domain OR lp.customDomain = :domain OR lp.cNameTargetDomain = :domain OR lp.domain = :domain)',
        {
          domain: activeDomain,
        },
      );

    if (excludeId) {
      domainQuery.andWhere('lp.id != :excludeId', { excludeId });
    }

    const existedDomain = await domainQuery.getExists();

    const existedDomainInsystem = await this.domainRepo.findOneBy({
      name: activeDomain,
    });

    if (existedDomain || existedDomainInsystem) {
      throw new UnprocessableEntityException('Domain is already existed');
    }

    return activeDomain;
  }

  async create(
    userId: string,
    createUserProjectLandingPageDto: CreateUserProjectLandingPageDto,
  ) {
    try {
      let cNameTargetDomain =
        createUserProjectLandingPageDto?.cNameTargetDomain;
      if (!cNameTargetDomain) {
        // Should generate cname target domain when record is created
        cNameTargetDomain = await this.generateCnameTargetDomain({
          projectId: createUserProjectLandingPageDto.projectId,
          userId: userId,
        });
      }

      const activeDomain = await this.checkIsValidDomain({
        type: createUserProjectLandingPageDto?.type,
        subDomain: createUserProjectLandingPageDto?.subDomain,
        customDomain: createUserProjectLandingPageDto?.customDomain,
        cNameTargetDomain,
      });

      const existedName = await this.repo.findOneBy({
        name: createUserProjectLandingPageDto.name,
        user: { id: userId },
      });

      if (existedName) {
        throw new UnprocessableEntityException(
          'Name is already existed in your list',
        );
      }

      const user = await this.userRepo.findOneBy({ id: userId });

      const isExists = await this.getExists({
        userId:
          user.role === Roles.ADMIN
            ? createUserProjectLandingPageDto.userDomainId
            : userId,
        projectId: createUserProjectLandingPageDto.projectId,
      });

      if (isExists) {
        throw new UnprocessableEntityException(
          'Landing Page is already existed in your list',
        );
      }

      const result = await this.repo.save({
        user: {
          id:
            user.role === Roles.ADMIN
              ? createUserProjectLandingPageDto.userDomainId
              : userId,
        },
        project: { id: createUserProjectLandingPageDto.projectId },
        domain: activeDomain,
        name: createUserProjectLandingPageDto.name,
        type: createUserProjectLandingPageDto.type,
        layout: createUserProjectLandingPageDto.layout,
        headerScript: createUserProjectLandingPageDto.headerScript,
        bodyScript: createUserProjectLandingPageDto.bodyScript,
        dataScript: createUserProjectLandingPageDto.dataScript,
        subDomain: createUserProjectLandingPageDto?.subDomain,
        customDomain: createUserProjectLandingPageDto?.customDomain,
        cNameTargetDomain: cNameTargetDomain || null,
      });

      return result;
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async delete(id: string) {
    const record = await this.repo.findOne({
      where: { id },
    });

    if (!record) {
      throw new NotFoundException('Landing page not found');
    }

    try {
      return await this.repo.softDelete(id);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async update(id: string, param: UpdateUserProjectLandingPageDto) {
    const record = await this.repo.findOneBy({ id });

    if (!record) {
      throw new NotFoundException('Landing page not found');
    }

    try {
      if (!!param.customDomain || !!param.subDomain) {
        // User update customDomain or subDomain

        const currentType = param?.type || record?.type;

        // Recheck unique domain exclude current record
        const activeDomain = await this.checkIsValidDomain(
          {
            type: currentType as EUserProjectLandingPageDomainType,
            subDomain: param?.subDomain,
            customDomain: param?.customDomain,
            cNameTargetDomain: param?.cNameTargetDomain,
          },
          record.id,
        );

        record.domain = activeDomain;
      } else if (param.type && param.type !== record.type) {
        // User only update type
        record.domain =
          param.type === EUserProjectLandingPageDomainType.SUB_DOMAIN
            ? param.subDomain
            : param.cNameTargetDomain;
      }

      record.type = param?.type ? param.type : record.type;

      record.customDomain = param?.customDomain
        ? param.customDomain
        : record.customDomain;

      record.subDomain = param?.subDomain ? param.subDomain : record.subDomain;

      if (param.layout !== undefined) {
        record.layout = param.layout;
      }

      if (param.headerScript !== undefined) {
        record.headerScript = param.headerScript;
      }

      if (param.bodyScript !== undefined) {
        record.bodyScript = param.bodyScript;
      }

      if (param.enquiryScript !== undefined) {
        record.enquiryScript = param.enquiryScript;
      }

      if (param.useEnquiryScript !== undefined) {
        record.useEnquiryScript = param.useEnquiryScript;
      }

      if (param.dataScript !== undefined) {
        record.dataScript = param.dataScript;
      }

      return await this.repo.save(record);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async getByDomain(domain: string) {
    try {
      const domainLower = domain.toLowerCase();

      const result = await this.repo.findOne({
        where: { domain: domainLower },
        relations: [
          'user',
          'user.config',
          'user.userFeature',
          'user.userFeature.feature',
          'project',
          'optionMappings',
          'optionMappings.landingPage',
          'optionMappings.landingPageOption',
          'project.mobileLogo',
          'project.desktopLogo',
          'project.siteplanImages',
        ],
      });

      // console.log(result);
      if (!result) {
        return result;
      }

      const originalDomain = await this.domainRepo.findOne({
        where: { config: { id: result?.user?.config?.id } },
      });

      const userConfig = await this.userConfigService.findOneByDomain(
        originalDomain.name,
      );

      const project = await this.propertyService.detail(
        originalDomain.name,
        result?.project?.slug,
        {},
      );

      if (project?.amenityHtml && typeof project.amenityHtml === 'object') {
        (project as any).amenityHtml = project.amenityHtml['en'] || null;
      }

      return {
        ...userConfig,
        project,
        originalDomain: originalDomain.name,
        layoutOption: result.layoutOption,
        optionMappings: result.optionMappings,
        dataScript: result.dataScript,
      };
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async getByProjectId(
    userId: string,
    projectId: string,
    query: QueryUserProjectLandingPage,
  ) {
    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.repo
      .createQueryBuilder('landingPage')
      .leftJoinAndSelect('landingPage.user', 'user')
      .leftJoinAndSelect('user.config', 'userConfig')
      .leftJoinAndSelect('userConfig.domains', 'domains')
      .leftJoinAndSelect('landingPage.project', 'project')
      .leftJoinAndSelect('landingPage.optionMappings', 'optionMappings')
      .leftJoinAndSelect(
        'optionMappings.landingPage',
        'optionMappingsLandingPage',
      )
      .leftJoinAndSelect(
        'optionMappings.landingPageOption',
        'optionMappingsLandingPageOption',
      )
      .addSelect(
        createMultilingualSelect('project', 'name', lang),
        'project_name',
      )
      .addSelect(
        createMultilingualSelect('project', 'description', lang),
        'project_description',
      )
      .where('user.id = :userId', { userId })
      .andWhere('project.id = :projectId', { projectId })
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`landingPage.${key}`, order);
      });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    const result = data.entities?.map((landingPage) => {
      const domains = landingPage?.user?.config?.domains;

      landingPage.user['domains'] = domains;

      return landingPage;
    });

    return createPaginationResponse(result, total, pagination);
  }

  async listing(query: QueryUserProjectLandingPage) {
    const pagination = getPaginationOption(query);
    const lang = query.lang || ELangCode.en;

    const queryBuilder = this.repo
      .createQueryBuilder('landingPage')
      .leftJoinAndSelect('landingPage.user', 'user')
      .leftJoinAndSelect('user.config', 'userConfig')
      .leftJoinAndSelect('userConfig.domains', 'domains')
      .leftJoinAndSelect('landingPage.project', 'project')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoinAndMapMany(
        'project.medias',
        Asset,
        'medias',
        'medias.relationId = project.id AND medias.relation = :relation',
        { relation: EAssetRelation.Project },
      )
      .leftJoinAndSelect('landingPage.optionMappings', 'optionMappings')
      .leftJoinAndSelect(
        'optionMappings.landingPage',
        'optionMappingsLandingPage',
      )
      .leftJoinAndSelect(
        'optionMappings.landingPageOption',
        'optionMappingsLandingPageOption',
      )
      .addSelect(
        createMultilingualSelect('project', 'name', lang),
        'project_name',
      )
      .addSelect(
        createMultilingualSelect('project', 'description', lang),
        'project_description',
      )
      .orderBy('landingPage.createdAt', 'DESC')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (query.search) {
      queryBuilder.andWhere(
        'landingPage.customDomain ILIKE :search OR landingPage.subDomain ILIKE :search',
        {
          search: `%${query.search}%`,
        },
      );
    }

    if (query.filter) {
      query.filter.forEach((filter) => {
        if (filter.userId) {
          queryBuilder.andWhere('user.id = :userId', {
            userId: filter.userId,
          });
        }
        if (filter.projectId) {
          queryBuilder.andWhere('project.id = :projectId', {
            projectId: filter.projectId,
          });
        }
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`landingPage.${key}`, order);
      });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      queryBuilder.getCount(),
    ]);

    const result = await Promise.all(
      data.entities?.map(async (landingPage) => {
        const domains = landingPage?.user?.config?.domains;

        landingPage?.user && landingPage?.user?.config
          ? (landingPage.user['domains'] = domains || [])
          : (landingPage.user = null);

        // Get project medias
        if (landingPage.project) {
          const excludeAssetIds = [
            landingPage.project.seoImageId,
            landingPage.project.sitePlanImageId,
            landingPage.project.elevationChartId,
          ].filter(Boolean);

          const medias = await this.assetService.getByRelatedId(
            landingPage.project.id,
            excludeAssetIds,
          );

          landingPage.project['medias'] = medias;
        }

        const ldpControl = await this.landingPagesControlRepo.findOneBy({
          userId: landingPage?.user?.id,
        });

        landingPage['useProjectSgTrackingId'] =
          !!ldpControl?.useProjectSgTrackingId;

        return landingPage;
      }),
    );

    return createPaginationResponse(result, total, pagination);
  }

  private slugify(text: string): string {
    return text
      .toString()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  }

  async generateUniqueDomain(
    userId: string,
    body: GenerateUniqueDomainDto,
  ): Promise<string> {
    const user = await this.userRepo.findOneBy({ id: userId });
    const project = await this.projectRepo.findOneBy({ id: body.projectId });

    const userName =
      user?.lastName && user?.firstName
        ? this.slugify(`${user.firstName} ${user.lastName}`)
        : Math.random().toString(36).substring(7);

    const projectName =
      project?.name?.en || Math.random().toString(36).substring(7);

    const userPart = this.slugify(userName);
    const projectPart = this.slugify(projectName);

    const hash = crypto
      .createHash('md5')
      .update(userId + projectName)
      .digest('hex')
      .slice(0, 5);

    let generatedDomain = `${userPart}-${projectPart}-${hash}`;

    let isUnique = false;
    let attempt = 0;
    const maxAttempts = 5;

    while (!isUnique && attempt < maxAttempts) {
      const existedDomain = await this.repo.findOne({
        where: { domain: Like(`%${generatedDomain}%`) },
      });

      if (!existedDomain) {
        isUnique = true;
      } else {
        const extraHash = crypto.randomBytes(2).toString('hex');
        generatedDomain = `${userPart}-${projectPart}-${extraHash}`;
      }
      attempt++;
    }

    if (!isUnique) {
      throw new Error('Unable to generate a unique domain');
    }

    return generatedDomain;
  }

  async triggerDefaultOption(body: TriggerDefaultLandingPageOptionDto) {
    try {
      const option = await this.landingPageLayoutOptionRepo.findOne({
        where: { id: body.optionId },
      });

      if (!option) {
        throw new NotFoundException(`Option with ${body.optionId} not found`);
      }

      const defaultOptionId = option.id;

      const landingPagesWithoutOption = await this.repo
        .createQueryBuilder('lp')
        .leftJoinAndSelect('lp.optionMappings', 'pivot')
        .where('pivot.id IS NULL')
        .getMany();

      if (landingPagesWithoutOption.length > 0) {
        const pivotData = landingPagesWithoutOption.map((lp) => ({
          landingPage: { id: lp.id },
          landingPageOption: { id: defaultOptionId },
          active: true,
        }));

        await this.landingPageOptionMappingRepo.save(pivotData);
      }
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async getExists(query: QueryExistsUserProjectLandingPage): Promise<boolean> {
    try {
      const { projectId, userId } = query;
      const landingPageData = await this.repo.findOneBy({
        userId,
        projectId,
      });
      return Boolean(landingPageData);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async generateCnameTargetDomain(body: GenerateUniqueDomainDto) {
    const isProduction = process.env.NODE_ENV === 'production';

    const suffixDomain = isProduction
      ? '.directbooking.sg'
      : '.staging.directbooking.sg';

    const userId = body.userId;
    const randomText = Math.random().toString(36).substring(7);
    const prefixCName = `cname-${randomText}`;
    const uniqueDomain = await this.generateUniqueDomain(userId, body);
    return `${prefixCName}-${uniqueDomain}${suffixDomain}`;
  }

  async autoGenerateCnameTargetDomainIfNotExist() {
    const allLDPsNotHaveCnameTargetDomain = await this.repo.find({
      where: {
        cNameTargetDomain: IsNull(),
      },
    });

    if (allLDPsNotHaveCnameTargetDomain.length > 0) {
      await Promise.all(
        allLDPsNotHaveCnameTargetDomain.map(async (ldp) => {
          const cnameTargetDomain = await this.generateCnameTargetDomain({
            projectId: ldp.projectId,
            userId: ldp.userId,
          });

          await this.repo.update(ldp.id, {
            cNameTargetDomain: cnameTargetDomain,
          });
        }),
      );
    }
  }

  async checkCnameConnection(domain: string) {
    const ldp = await this.repo.findOneBy({
      customDomain: domain,
    });

    if (!ldp) {
      throw new NotFoundException('Domain mismatch with any landing page');
    }

    if (ldp.domain !== domain) {
      // Because we only check cname connection for custom domain, domain field is active domain
      throw new NotFoundException('Invalid active type');
    }

    const cname = await this.domainsService.checkCNAMEFromTerminal(domain);

    if (!cname) {
      throw new NotFoundException('Not CNAME found');
    }

    if (cname !== ldp.cNameTargetDomain) {
      throw new NotFoundException('CNAME mismatch');
    }

    return true;
  }

  async autoFillSubdomainOrCustomDomain() {
    /**
     * Auto fill subdomain or custom domain if it is null
     * field "domain" is the active domain, so we can use it to fill the subdomain or custom domain
     */
    const ldps = await this.repo
      .createQueryBuilder('ldp')
      .where('(ldp.type = :sub AND ldp.subDomain IS NULL)', {
        sub: EUserProjectLandingPageDomainType.SUB_DOMAIN,
      })
      .orWhere('(ldp.type = :custom AND ldp.customDomain IS NULL)', {
        custom: EUserProjectLandingPageDomainType.CUSTOM_DOMAIN,
      })
      .getMany();

    for (const ldp of ldps) {
      if (ldp.type === EUserProjectLandingPageDomainType.SUB_DOMAIN) {
        ldp.subDomain = ldp.domain;
      } else if (ldp.type === EUserProjectLandingPageDomainType.CUSTOM_DOMAIN) {
        ldp.customDomain = ldp.domain;
      }
    }

    await this.repo.save(ldps);
  }
}
