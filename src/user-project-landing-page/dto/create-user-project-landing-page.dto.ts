import {
  IsBoolean,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUUI<PERSON>,
} from 'class-validator';
import { EUserProjectLandingPageDomainType } from '../entities/user-project-landing-page.entity';

export class CreateUserProjectLandingPageDto {
  @IsUUID()
  projectId: string;

  @IsOptional()
  @IsUUID()
  userDomainId: string;

  // @IsString()
  // domain: string; // This will automatically be filled in following the logic in the service file

  @IsString()
  name: string;

  @IsString()
  @IsEnum(EUserProjectLandingPageDomainType)
  type?: EUserProjectLandingPageDomainType;

  @IsOptional()
  layout?: Record<string, string>;

  @IsString()
  @IsOptional()
  headerScript: string;

  @IsString()
  @IsOptional()
  bodyScript: string;

  @IsString()
  @IsOptional()
  enquiryScript: string;

  @IsBoolean()
  @IsOptional()
  useEnquiryScript: boolean;

  @IsOptional()
  @IsString()
  customDomain: string;

  @IsOptional()
  @IsString()
  cNameTargetDomain: string;

  @IsOptional()
  @IsString()
  subDomain: string;

  @IsObject()
  @IsOptional()
  dataScript?: Record<string, any>;

  @IsString()
  @IsOptional()
  pixelID?: string;
}

export class UpdateUserProjectLandingPageDto {
  // @IsOptional()
  // @IsString()
  // domain: string;

  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  @IsEnum(EUserProjectLandingPageDomainType)
  type?: EUserProjectLandingPageDomainType;

  @IsOptional()
  layout?: Record<string, string>;

  @IsString()
  @IsOptional()
  headerScript: string;

  @IsString()
  @IsOptional()
  bodyScript: string;

  @IsString()
  @IsOptional()
  enquiryScript: string;

  @IsBoolean()
  @IsOptional()
  useEnquiryScript: boolean;

  @IsOptional()
  @IsString()
  customDomain: string;

  @IsOptional()
  @IsString()
  cNameTargetDomain: string;

  @IsOptional()
  @IsString()
  subDomain: string;

  @IsObject()
  @IsOptional()
  dataScript?: Record<string, any>;

  @IsString()
  @IsOptional()
  pixelID?: string;
}

export class GenerateUniqueDomainDto {
  @IsUUID()
  projectId: string;

  @IsUUID()
  @IsOptional()
  userId: string;
}
