import {
  IsArray,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class QueryUserProjectLandingPage extends PaginationQueryDto {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  filter: Filter[];

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };
}

class Filter {
  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsUUID()
  projectId?: string;
}
