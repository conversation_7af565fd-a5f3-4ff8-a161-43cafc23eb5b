import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProjectLandingPage } from './entities/user-project-landing-page.entity';
import { UserProjectLandingPageController } from './user-project-landing-page.controller';
import { UserProjectLandingPageService } from './user-project-landing-page.service';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { Domain } from 'src/domains/entities/domain.entity';
import { User } from 'src/users/entities/user.entity';
import { UserConfigModule } from 'src/user-config/user-config.module';
import { PropertyModule } from 'src/property/property.module';
import { Project } from 'src/project/entities/project.entity';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { UmamiModule } from 'src/umami/umami.module';
import { LandingPageLayoutOptionModule } from './layout-options/landing-page-layout-option.module';
import { LandingPageLayoutOption } from './layout-options/entities/landing-page-layout-option.entity';
import { LandingPageOptionMapping } from './layout-options/entities/landing_page_option_mapping.entity';
import { AssetModule } from 'src/asset/asset.module';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';
import { DomainsModule } from 'src/domains/domains.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProjectLandingPage,
      UserProject,
      Domain,
      User,
      Project,
      UmamiMapping,
      UmamiWebsites,
      LandingPageLayoutOption,
      LandingPageOptionMapping,
      LandingpagesControl,
    ]),
    UserConfigModule,
    PropertyModule,
    UmamiModule,
    LandingPageLayoutOptionModule,
    AssetModule,
    DomainsModule,
  ],
  controllers: [UserProjectLandingPageController],
  providers: [UserProjectLandingPageService],
  exports: [UserProjectLandingPageService],
})
export class UserProjectLandingPageModule {}
