import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EmailTemplate } from './entities/email-template.entity';
import { Repository } from 'typeorm';
import { UpdateEmailTemplateDto } from './dto/update-email-template.dto';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import { QueryEmailTemplateDto } from './dto/query-email-template.dto';

@Injectable()
export class EmailTemplateService {
  constructor(
    @InjectRepository(EmailTemplate)
    private readonly emailTemplateRepo: Repository<EmailTemplate>,
  ) {}

  async create(emailTemplate: CreateEmailTemplateDto): Promise<EmailTemplate> {
    return await this.emailTemplateRepo.save(emailTemplate);
  }

  async findAll(query: QueryEmailTemplateDto): Promise<EmailTemplate[]> {
    const queryBuilder =
      this.emailTemplateRepo.createQueryBuilder('emailTemplate');

    if (query.name) {
      queryBuilder.andWhere('emailTemplate.name = :name', {
        name: query.name,
      });
    }

    if (query.id) {
      queryBuilder.andWhere('emailTemplate.id = :id', {
        id: query.id,
      });
    }

    if (query.subject) {
      queryBuilder.andWhere('emailTemplate.subject = :subject', {
        subject: query.subject,
      });
    }

    return await queryBuilder.getMany();
  }

  async findFirst(query: QueryEmailTemplateDto): Promise<EmailTemplate> {
    const queryBuilder =
      this.emailTemplateRepo.createQueryBuilder('emailTemplate');

    if (query.name) {
      queryBuilder.andWhere('emailTemplate.name = :name', {
        name: query.name,
      });
    }

    if (query.id) {
      queryBuilder.andWhere('emailTemplate.id = :id', {
        id: query.id,
      });
    }

    if (query.subject) {
      queryBuilder.andWhere('emailTemplate.subject = :subject', {
        subject: query.subject,
      });
    }

    return await queryBuilder.getOne();
  }

  async update(
    id: string,
    emailTemplate: UpdateEmailTemplateDto,
  ): Promise<EmailTemplate> {
    await this.emailTemplateRepo.update(id, emailTemplate);
    return await this.emailTemplateRepo.findOne({ where: { id } });
  }
}
