import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { UrlService } from './url.service';
import { CreateUrlDto } from './dto/create.url.dto';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { QueryUrlDto } from './dto/query.url.dto';

@Controller('url')
@UseGuards(RolesGuard)
export class UrlController {
  constructor(private readonly urlService: UrlService) {}

  @Post()
  @AccessRoles(Roles.ADMIN)
  create(@Body() createUrlDto: CreateUrlDto) {
    return this.urlService.create(createUrlDto);
  }

  @Get()
  @AccessRoles(Roles.ADMIN)
  findAll(@Query() queryUrlDto: QueryUrlDto) {
    return this.urlService.findAll(queryUrlDto);
  }
}
