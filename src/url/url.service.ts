import { BadRequestException, Injectable } from '@nestjs/common';
import { Url } from './entities/url.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUrlDto } from './dto/create.url.dto';
import * as crypto from 'crypto';
import { QueryUrlDto } from './dto/query.url.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';

@Injectable()
export class UrlService {
  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
  ) {}

  async create(createUrlDto: CreateUrlDto): Promise<Url[]> {
    const { data } = createUrlDto;

    const urlSet = Array.from(new Set(data));
    const urls = await this.urlRepository.find({
      where: {
        type: createUrlDto.type,
      },
    });

    const urlsToRemove = urls?.filter((url) => !urlSet?.includes(url.name));

    const urlsToAdd = urlSet?.filter(
      (url) => !urls.some((u) => u.name === url),
    );

    const urlDto = urlsToAdd?.map((url) => {
      const secretKey = crypto.createHash('sha1').update(url).digest('hex');

      return {
        name: url,
        secretKey,
        type: createUrlDto.type,
      };
    });

    if (urlDto.length > 0) {
      await this.urlRepository.save(urlDto);
    }

    if (urlsToRemove.length > 0) {
      await this.urlRepository.remove(urlsToRemove);
    }

    return await this.urlRepository.find({
      where: {
        type: createUrlDto.type,
      },
    });
  }

  async findAll(queryUrlDto: QueryUrlDto) {
    const pagination = getPaginationOption(queryUrlDto);

    const queryBuilder = this.urlRepository
      .createQueryBuilder('url')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (queryUrlDto.type) {
      queryBuilder.andWhere('url.type = :type', { type: queryUrlDto.type });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }
}
