import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { UserFeatureService } from './user-feature.service';
import { CreateUserFeatureDto } from './dto/create-user-feature.dto';
import { QueryUserFeatureDto } from './dto/query-user-feature.dto';

@Controller('user-feature')
export class UserFeatureController {
  constructor(private readonly userFeatureService: UserFeatureService) {}

  @Post()
  create(@Body() createUserFeatureDto: CreateUserFeatureDto) {
    return this.userFeatureService.create(createUserFeatureDto);
  }

  // just to for testing purposes
  @Post('add-feature-to-users/:featureId')
  addFeatureToAllUsers(@Param('featureId') featureId: string) {
    return this.userFeatureService.addFeatureToUsers(featureId);
  }

  @Get()
  findAll(@Query() query: QueryUserFeatureDto) {
    return this.userFeatureService.findAll(query);
  }

  @Get('user/:id')
  findAllByUserId(@Param('id') id: string) {
    return this.userFeatureService.findAllByUserId(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUserFeatureDto: QueryUserFeatureDto,
  ) {
    return this.userFeatureService.update(id, updateUserFeatureDto);
  }
}
