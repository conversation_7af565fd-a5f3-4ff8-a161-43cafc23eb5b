import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EDynamicLayoutMode } from '../entities/user-feature.entity';

export class CreateUserFeatureDto {
  @IsUUID()
  userId: string;

  @IsUUID()
  featureId: string;

  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @IsDate()
  @IsOptional()
  expiredAt?: Date;

  @IsOptional()
  @IsString()
  activationKey?: string;

  @IsOptional()
  @IsString()
  chatId?: string;

  @IsOptional()
  @IsEnum(EDynamicLayoutMode)
  dynamicLayoutMode?: EDynamicLayoutMode;

  // For extend landingPages feature
  @IsOptional()
  @IsBoolean()
  useProjectSgTrackingId?: boolean;

  @IsOptional()
  @IsNumber()
  limitCreation?: number | null;
}
