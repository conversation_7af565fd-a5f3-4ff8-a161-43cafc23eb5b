import { IsBoolean, IsDate, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EDynamicLayoutMode } from '../entities/user-feature.entity';

export class QueryUserFeatureDto extends PaginationQueryDto {
  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsUUID()
  featureId?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @IsOptional()
  @IsDate()
  expiredAt?: Date;

  @IsOptional()
  @IsEnum(EDynamicLayoutMode)
  dynamicLayoutMode?: EDynamicLayoutMode;
}
