import CrudEntity from 'src/core/entities/crud.entity';
import { Feature } from 'src/feature/entities/feature.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

export enum EDynamicLayoutMode {
  NORMAL = 'normal',
  ADVANCE = 'advance',
}

@Entity()
export class UserFeature extends CrudEntity {
  @Column()
  userId: string;

  @JoinColumn()
  @ManyToOne(() => User, (user) => user.userFeature)
  user: User;

  @Column()
  featureId: string;

  @JoinColumn()
  @ManyToOne(() => Feature, (feature) => feature.userFeature)
  feature: Feature;

  @Column({ default: false })
  enabled: boolean;

  @Column({ type: 'timestamp', default: null })
  expiredAt: Date;

  @Column({ nullable: true })
  activationKey: string;

  @Column({
    type: 'enum',
    enum: EDynamicLayoutMode,
    default: EDynamicLayoutMode.NORMAL,
  })
  dynamicLayoutMode: EDynamicLayoutMode;
}
