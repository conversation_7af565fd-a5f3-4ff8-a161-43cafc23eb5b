import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserFeature } from './entities/user-feature.entity';
import { UserFeatureController } from './user-feature.controller';
import { UserFeatureService } from './user-feature.service';
import { Feature } from 'src/feature/entities/feature.entity';
import { User } from 'src/users/entities/user.entity';
import { Bot } from 'src/bot/entities/bot.entity';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserFeature,
      Feature,
      User,
      Bot,
      LandingpagesControl,
    ]),
  ],
  controllers: [UserFeatureController],
  providers: [UserFeatureService],
  exports: [UserFeatureService],
})
export class UserFeatureModule {}
