import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Bot } from 'src/bot/entities/bot.entity';
import { Feature } from 'src/feature/entities/feature.entity';
import { EFeature } from 'src/feature/feature.enum';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';
import { User } from 'src/users/entities/user.entity';
import { IsNull, Repository } from 'typeorm';
import { CreateUserFeatureDto } from './dto/create-user-feature.dto';
import { QueryUserFeatureDto } from './dto/query-user-feature.dto';
import { UserFeature } from './entities/user-feature.entity';

@Injectable()
export class UserFeatureService {
  constructor(
    @InjectRepository(UserFeature)
    private readonly userFeatureRepository: Repository<UserFeature>,
    @InjectRepository(Feature)
    private readonly featureRepository: Repository<Feature>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Bot)
    private readonly botRepository: Repository<Bot>,
    @InjectRepository(LandingpagesControl)
    private readonly landingPagesControlRepository: Repository<LandingpagesControl>,
  ) {}

  private async handleIpAccessTrackerWithTelegramAlerts(
    chatId: string,
    user: User,
  ) {
    const bot = await this.botRepository.findOne({
      where: { userId: user.id },
    });

    let botName = 'property_development_tele_bot';
    if (process.env.NODE_ENV === 'production') {
      botName = 'property_prod_bot,property_prod_2_bot';
    }

    if (!bot) {
      await this.botRepository.save({
        name: botName,
        userId: user.id,
        email: user.email,
        chatId,
      });
    } else {
      await this.botRepository.update(bot.id, { chatId });
    }
  }

  private async handleLandingPageFeature(
    useProjectSgTrackingId: boolean,
    limitCreation: number | null,
    user: User,
  ) {
    const landingPageFeatureControl =
      await this.landingPagesControlRepository.findOneBy({
        userId: user.id,
      });

    if (landingPageFeatureControl) {
      await this.landingPagesControlRepository.update(
        landingPageFeatureControl.id,
        {
          useProjectSgTrackingId,
          limitCreation,
        },
      );
    } else {
      await this.landingPagesControlRepository.save({
        useProjectSgTrackingId,
        userId: user.id,
        limitCreation,
      });
    }
  }

  private async featureInputMapping(
    type: EFeature,
    params: {
      chatId?: string;
      user?: User;
      useProjectSgTrackingId?: boolean;
      limitCreation?: number | null;
    },
  ) {
    const { chatId, user, useProjectSgTrackingId, limitCreation } = params;

    switch (type) {
      case EFeature.IPAccessTrackerWithTelegramAlerts:
        await this.handleIpAccessTrackerWithTelegramAlerts(chatId, user);
        break;
      case EFeature.ProjectLandingPage:
        await this.handleLandingPageFeature(
          useProjectSgTrackingId,
          limitCreation,
          user,
        );
        break;
      default:
        return;
    }
  }

  async create(createUserFeatureDto: CreateUserFeatureDto) {
    const feature = await this.featureRepository.findOne({
      where: { id: createUserFeatureDto.featureId },
    });

    if (!feature) {
      throw new NotFoundException('Feature not found');
    }

    const { type } = feature;

    let userFeature = await this.userFeatureRepository.findOne({
      where: {
        userId: createUserFeatureDto.userId,
        featureId: createUserFeatureDto.featureId,
      },
      relations: ['user', 'feature'],
    });

    if (userFeature) {
      await this.userFeatureRepository.update(userFeature.id, {
        enabled: createUserFeatureDto.enabled,
      });
    } else {
      userFeature = await this.userFeatureRepository.save({
        userId: createUserFeatureDto.userId,
        featureId: createUserFeatureDto.featureId,
        enabled: createUserFeatureDto.enabled,
      });

      if (type == EFeature.TeamAccount) {
        await this.createRoundRobinFeature(createUserFeatureDto.userId);
      }
    }

    const updatedUserFeature = await this.userFeatureRepository.findOne({
      where: { id: userFeature.id, userId: createUserFeatureDto.userId },
      relations: ['user', 'feature'],
    });

    await this.featureInputMapping(type, {
      chatId: createUserFeatureDto.chatId,
      user: updatedUserFeature.user,
      useProjectSgTrackingId: createUserFeatureDto.useProjectSgTrackingId,
      limitCreation: createUserFeatureDto.limitCreation,
    });

    return await this.userFeatureRepository.findOneBy({
      userId: createUserFeatureDto.userId,
      featureId: createUserFeatureDto.featureId,
    });
  }

  async createLandingPageFeature(userId: string) {
    const landingPageFeature = await this.featureRepository.findOneBy({
      type: EFeature.ProjectLandingPage,
    });
    if (landingPageFeature) {
      await this.userFeatureRepository.save({
        userId: userId,
        featureId: landingPageFeature.id,
        enabled: true,
      });
    }
  }

  async createRoundRobinFeature(userId: string) {
    let roundRobinFeature = await this.featureRepository.findOneBy({
      type: EFeature.RoundRobin,
    });
    if (roundRobinFeature) {
      await this.userFeatureRepository.save({
        userId: userId,
        featureId: roundRobinFeature.id,
        enabled: false,
      });
    }
  }

  async addFeatureToUsers(featureId: string) {
    // const users = await this.userRepository.find({
    //   where: { role: Not(Roles.ADMIN) },
    // });

    const bots = await this.botRepository.find();
    const userProjects = await this.userFeatureRepository.find();

    const filteredBots = bots.filter((bot) => {
      return !userProjects.find(
        (userProject) =>
          userProject.userId === bot.userId &&
          userProject.featureId === featureId,
      );
    });

    console.log('filteredBots', filteredBots);

    for (const bot of filteredBots) {
      await this.create({
        userId: bot.userId,
        featureId,
        enabled: true,
        chatId: bot.chatId,
      });
    }

    return 'Feature added to all users';
  }

  async findAll(query: QueryUserFeatureDto) {
    const queryBuilder = this.userFeatureRepository
      .createQueryBuilder('userFeature')
      .leftJoinAndSelect('userFeature.user', 'user')
      .leftJoinAndSelect('userFeature.feature', 'feature');

    return await queryBuilder.getMany();
  }

  public async buildUserFeatureResponse(userFeature: UserFeature) {
    const { type } = userFeature.feature;

    switch (type) {
      case EFeature.IPAccessTrackerWithTelegramAlerts:
        const bot = await this.botRepository.findOne({
          where: { userId: userFeature.userId },
        });
        return {
          ...userFeature,
          chatId: bot?.chatId,
        };
      case EFeature.ProjectLandingPage:
        const landingPageFeatureControl =
          await this.landingPagesControlRepository.findOneBy({
            userId: userFeature.userId,
          });
        return {
          ...userFeature,
          useProjectSgTrackingId:
            !!landingPageFeatureControl?.useProjectSgTrackingId,
          limitCreation: landingPageFeatureControl?.limitCreation,
        };
      default:
        return userFeature;
    }
  }

  async findAllByUserId(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const userFeature = await this.userFeatureRepository.find({
      where: { userId, enabled: true },
      relations: ['feature'],
    });

    if (userFeature.length === 0 || !userFeature) {
      return [];
    }

    const result = [];

    for (const feature of userFeature) {
      result.push(await this.buildUserFeatureResponse(feature));
    }

    return result;
  }

  async update(id: string, updateUserFeatureDto: QueryUserFeatureDto) {
    const userFeature = await this.userFeatureRepository.findOneBy({ id });

    if (!userFeature) {
      throw new NotFoundException('UserFeature not found');
    }

    return await this.userFeatureRepository.update(id, updateUserFeatureDto);
  }

  async initAutoAddNewProjectFeature(userId: string, enabled: boolean = true) {
    const featureData = await this.featureRepository.findOne({
      where: { deletedAt: IsNull(), type: EFeature.AutoAddNewProject },
    });
    if (!featureData) {
      return;
    }

    const newUserFeature = new CreateUserFeatureDto();
    newUserFeature.enabled = enabled;
    newUserFeature.featureId = featureData.id;
    newUserFeature.userId = userId;
    await this.create(newUserFeature);
  }

  async getAutoAddNewProjectFeature(userId: string, enabled: boolean = true) {
    const autoAddNewProjectFeature = await this.featureRepository.findOne({
      where: {
        deletedAt: IsNull(),
        type: EFeature.AutoAddNewProject,
      },
    });
    if (!autoAddNewProjectFeature) {
      throw new NotFoundException(`${EFeature.AutoAddNewProject} is not found`);
    }

    const userFeatureData = await this.userFeatureRepository.findOne({
      where: {
        deletedAt: IsNull(),
        enabled,
        featureId: autoAddNewProjectFeature.id,
        userId,
      },
    });

    return userFeatureData;
  }

  async updateAutoAddNewProjectFeature(userId: string, enabled: boolean) {
    const autoAddNewProjectFeature = await this.featureRepository.findOne({
      where: {
        deletedAt: IsNull(),
        type: EFeature.AutoAddNewProject,
      },
    });
    if (!autoAddNewProjectFeature) {
      throw new NotFoundException(`${EFeature.AutoAddNewProject} is not found`);
    }

    const createUserFeature = new CreateUserFeatureDto();
    createUserFeature.featureId = autoAddNewProjectFeature.id;
    createUserFeature.userId = userId;
    createUserFeature.enabled = enabled;

    const result = await this.create(createUserFeature);
    return result;
  }
}
