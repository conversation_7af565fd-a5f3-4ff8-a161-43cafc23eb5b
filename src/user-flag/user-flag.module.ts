import { Module } from '@nestjs/common';
import { UserFlag } from './entities/user-flag.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserFlagService } from './user-flag.service';
import { UserFlagController } from './user-flag.controller';

@Module({
  imports: [TypeOrmModule.forFeature([UserFlag])],
  controllers: [UserFlagController],
  providers: [UserFlagService],
  exports: [UserFlagService],
})
export class UserFlagModule {}
