import {
  IsEnum,
  <PERSON>Optional,
  <PERSON>String,
  IsU<PERSON><PERSON>,
  Val<PERSON>te,
  ValidateNested,
} from 'class-validator';
import { UserFlag } from '../entities/user-flag.entity';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { EUserFlagType } from '../enums/user-flag-type.enum';

class UserFlagFilter implements Partial<Pick<UserFlag, 'userId' | 'type'>> {
  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsEnum(EUserFlagType)
  type?: EUserFlagType;
}

export class QueryUserFlagsDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => UserFlagFilter)
  filter?: UserFlagFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
