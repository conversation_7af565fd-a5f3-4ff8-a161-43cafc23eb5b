import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { QueryUserFlagsDto } from './dto/query-user-flags.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UserFlag } from './entities/user-flag.entity';
import { CreateUserFlagDto } from './dto/create-user-flag.dto';
import { UpdateUserFlagDto } from './dto/update-user-flag.dto';
import { GetUserFlagDto } from './dto/get-user-flag.dto';

@Injectable()
export class UserFlagService {
  constructor(
    @InjectRepository(UserFlag)
    private readonly userFlagRepository: Repository<UserFlag>,
  ) {}

  async getMe(dto: GetUserFlagDto) {
    const { userId, type } = dto;
    const where: FindOptionsWhere<UserFlag> = { deletedAt: IsNull() };
    if (userId) {
      where.userId = userId;
    }
    if (type) {
      where.type = type;
    }
    const result = await this.userFlagRepository.findBy(where);
    if (!result) {
      throw new NotFoundException('UserFlag not found');
    }
    return result;
  }

  async getAll(dto: QueryUserFlagsDto) {
    const { filter, sort, search } = dto;
    const pagination = getPaginationOption(dto);

    const queryBuilder = this.userFlagRepository
      .createQueryBuilder('userFlag')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.userId)
        queryBuilder.andWhere('userFlag.userId = :userId', {
          userId: filter.userId,
        });
    }

    if (search) {
      // queryBuilder.andWhere(`userFlag.name LIKE :search`, {
      //   search: `%${search}%`,
      // });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`userFlag.${key}`, order);
      });
    }

    const [apiKeys, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(apiKeys, total, pagination);
  }

  async getOne(query: Partial<UserFlag>) {
    const { id, userId } = query;
    const where: FindOptionsWhere<UserFlag> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (userId) {
      where.userId = userId;
    }
    const result = await this.userFlagRepository.findOneBy(where);
    if (!result) {
      throw new NotFoundException('UserFlag not found');
    }
    return result;
  }

  async create(dto: CreateUserFlagDto) {
    const userFlagData = await this.userFlagRepository.findOneBy({
      userId: dto.userId,
      deletedAt: IsNull(),
      type: dto.type,
    });
    if (userFlagData) {
      return userFlagData;
    }
    const result = await this.userFlagRepository.save(dto);
    return result;
  }

  async update(query: Partial<UserFlag>, dto: UpdateUserFlagDto) {
    const { id } = query;
    const where: FindOptionsWhere<UserFlag> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const result = await this.userFlagRepository.update(where, dto);
    return result;
  }

  async delete(query: Partial<UserFlag>) {
    const { id, type, userId } = query;
    const where: FindOptionsWhere<UserFlag> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (type) {
      where.type = type;
    }
    if (userId) {
      where.userId = userId;
    }
    const userFlagData = await this.userFlagRepository.findOneBy(where);
    if (!userFlagData) {
      throw new NotFoundException('UserFlag not found');
    }
    const result = await this.userFlagRepository.softDelete(where);
    return result;
  }
}
