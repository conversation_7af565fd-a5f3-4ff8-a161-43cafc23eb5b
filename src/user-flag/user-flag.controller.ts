import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { UserFlagService } from './user-flag.service';
import { QueryUserFlagsDto } from './dto/query-user-flags.dto';
import { CreateUserFlagDto } from './dto/create-user-flag.dto';
import { UpdateUserFlagDto } from './dto/update-user-flag.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { GetUserFlagDto } from './dto/get-user-flag.dto';
import { EUserFlagType } from './enums/user-flag-type.enum';

@Controller('user-flag')
@UseGuards(RolesGuard)
export class UserFlagController {
  constructor(private readonly userFlagService: UserFlagService) {}

  @Get()
  async getAll(
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
    @Query() dto: QueryUserFlagsDto,
  ) {
    if (![Roles.ADMIN].includes(role)) dto.filter.userId = userId;
    return await this.userFlagService.getAll(dto);
  }

  @Get('/me')
  async getMe(@ActiveUser('sub') userId: string, @Query() dto: GetUserFlagDto) {
    dto.userId = userId;
    return await this.userFlagService.getMe(dto);
  }

  @Get(':id')
  async getOne(@Param('id') id: string, @ActiveUser('role') role: Roles) {
    if ([Roles.ADMIN].includes(role))
      return await this.userFlagService.getOne({ id });
    else return await this.userFlagService.getOne({ id });
  }

  @Post()
  async create(
    @ActiveUser('sub') userId: string,
    @Body() dto: CreateUserFlagDto,
  ) {
    dto.userId = userId;
    return await this.userFlagService.create(dto);
  }

  @Delete('type/:type')
  async deleteByType(
    @Param('type') type: EUserFlagType,
    @ActiveUser('sub') userId: string,
  ) {
    return await this.userFlagService.delete({ type, userId });
  }

  @Patch(':id')
  async updateById(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
    @Body() dto: UpdateUserFlagDto,
  ) {
    if ([Roles.ADMIN].includes(role))
      return await this.userFlagService.update({ id, userId }, dto);
    else return await this.userFlagService.update({ id, userId }, dto);
  }

  @Delete(':id')
  async deleteById(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
  ) {
    if ([Roles.ADMIN].includes(role))
      return await this.userFlagService.delete({ id });
    else return await this.userFlagService.delete({ id, userId });
  }
}
