import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { EUserFlagType } from '../enums/user-flag-type.enum';

@Entity()
export class UserFlag extends CrudEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  user: User;

  @Column({
    type: 'enum',
    enum: EUserFlagType,
  })
  type: EUserFlagType;

  @Column({ type: 'json', nullable: true })
  data?: Record<string, any>;
}
