import { Optional } from '@nestjs/common';
import { IsString } from 'class-validator';

export class UpdateIpTrackingDto {
  @Optional()
  @IsString()
  ip: string;

  @Optional()
  @IsString()
  projectName: string;

  @Optional()
  @IsString()
  projectId: string;

  @Optional()
  @IsString()
  projectLink: string;

  @Optional()
  @IsString()
  region: string;

  @Optional()
  @IsString()
  browser: string;

  @Optional()
  @IsString()
  domain: string;
}
