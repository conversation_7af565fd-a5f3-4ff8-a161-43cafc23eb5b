import { IsOptional, IsString } from 'class-validator';

export class CreateIpTrackingDto {
  @IsString()
  ip: string;

  @IsString()
  projectName: string;

  @IsString()
  projectId: string;

  @IsString()
  projectLink: string;

  @IsString()
  region: string;

  @IsString()
  browser: string;

  @IsString()
  @IsOptional()
  country: string;

  @IsString()
  @IsOptional()
  countryCode: string;
}
