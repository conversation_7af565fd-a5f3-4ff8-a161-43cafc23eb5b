import { Type } from 'class-transformer';
import { IsDate, IsOptional, IsString } from 'class-validator';

export class QueryIpTrackingDto {
  @IsOptional()
  @IsString()
  projectName?: string;

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsOptional()
  @IsString()
  ip?: string;

  @IsOptional()
  @IsString()
  region?: string;

  @IsOptional()
  @IsString()
  browser?: string;

  @IsOptional()
  @IsString()
  domain?: string;

  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @IsDate()
  @Type(() => Date)
  endDate?: Date;
}
