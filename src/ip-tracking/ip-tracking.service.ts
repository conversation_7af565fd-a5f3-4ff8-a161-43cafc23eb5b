import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { CreateIpTrackingDto } from './dto/create-ip-tracking.dto';
import { IpTracking } from './entities/ip-tracking.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryIpTrackingDto } from './dto/query-ip-tracking.dto';
import { UpdateIpTrackingDto } from './dto/update-ip-tracking.dto';
import { TelegramService } from 'src/telegram/telegram.service';
import { UserConfigService } from 'src/user-config/user-config.service';
import { Bot } from 'src/bot/entities/bot.entity';
import {
  countryFlags,
  ECountryAlpha2Code,
} from 'src/core/enums/countryFlags.enum';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { EFeature } from 'src/feature/feature.enum';
import e, { Request } from 'express';
import * as geoip from 'geoip-lite';
import * as RSS from 'rss';
import * as moment from 'moment-timezone';

@Injectable()
export class IpTrackingService {
  constructor(
    @InjectRepository(IpTracking)
    private readonly ipTrackingRepo: Repository<IpTracking>,
    @InjectRepository(Bot)
    private readonly botRepo: Repository<Bot>,
    @InjectRepository(UserFeature)
    private readonly userFeatureRepo: Repository<UserFeature>,

    private readonly telegramService: TelegramService,
    private readonly userConfigService: UserConfigService,
  ) {}

  async listing(query: QueryIpTrackingDto) {
    const queryBuilder = this.ipTrackingRepo.createQueryBuilder('ipTracking');

    if (query.domain) {
      queryBuilder.andWhere('ipTracking.userDomain = :userDomain', {
        userDomain: query.domain,
      });
    }

    if (query.ip) {
      queryBuilder.andWhere('ipTracking.ip = :ip', { ip: query.ip });
    }

    if (query.projectId) {
      queryBuilder.andWhere('ipTracking.projectId = :projectId', {
        projectId: query.projectId,
      });
    }

    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          'Start date should be less than end date',
        );
      }
    }

    if (query.startDate) {
      queryBuilder.andWhere('ipTracking.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('ipTracking.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    return await queryBuilder.getMany();
  }

  private convertUtcToSingaporeTime(utcDate: Date) {
    const date = new Date(utcDate);

    const singaporeTimeZone = 'Asia/Singapore';

    const options: Intl.DateTimeFormatOptions = {
      timeZone: singaporeTimeZone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    };

    const formatter = new Intl.DateTimeFormat('en-US', options);
    return formatter.format(date);
  }

  async createIpTracking(
    userDomain: string,
    createIpTrackingDto: CreateIpTrackingDto,
    req: Request,
  ) {
    const { ip, projectName, region, browser, projectLink, country } =
      createIpTrackingDto;

    console.log('createIpTrackingDto', createIpTrackingDto);

    const ipAddress = Array.isArray(ip)
      ? ip[0]
      : ip || req.connection.remoteAddress;

    const geo = geoip.lookup(ipAddress);

    const userConfig = await this.userConfigService.findOneByDomain(userDomain);
    // const email = userConfig?.agencyEmail || userConfig.user.email;
    const bot = await this.botRepo.findOne({
      where: { userId: userConfig.user.id },
    });

    const savedIpTracking = await this.ipTrackingRepo.save({
      ...createIpTrackingDto,
      domain: userDomain,
      country: country || ECountryAlpha2Code[geo?.country],
    });

    if (!bot) {
      console.log('Bot not found');
    }

    const isEnableTracking = await this.isEnableTrackingIp(bot?.userId).catch(
      (e) => {
        console.log('Error', e);
      },
    );

    if (bot?.chatId && isEnableTracking && geo?.country === 'SG') {
      const now = new Date(Date.now());
      const time = this.convertUtcToSingaporeTime(now);

      let countryFlag = '';

      if (!country) {
        if (geo?.country) {
          countryFlag = `${ECountryAlpha2Code[geo?.country]} ${countryFlags[ECountryAlpha2Code['SG']]}`;
        }
      } else {
        countryFlag = `${country} ${countryFlags['Singapore']}`;
      }

      const message = `IP ${ip} is accessing project <a href="${projectLink}">${projectName}</a> at ${time.toString()}, from ${countryFlag}, via ${browser}.`;
      await this.telegramService.sendMessage(
        bot.chatId,
        message,
        {
          parse_mode: 'HTML',
        },
        savedIpTracking.id,
      );
    } else {
      console.log(
        `IP ${ipAddress} is from ${geo?.country}, not sending message.`,
      );
    }

    return savedIpTracking;
  }

  private async isEnableTrackingIp(userId: string) {
    const userFeatures = await this.userFeatureRepo.find({
      where: {
        userId,
      },
      relations: ['feature'],
    });

    const ipTrackingFeature = userFeatures?.find((userFeature) => {
      return (
        userFeature?.feature?.type ===
        EFeature.IPAccessTrackerWithTelegramAlerts
      );
    });

    if (!ipTrackingFeature) {
      return true;
    }

    return ipTrackingFeature.enabled;
  }

  async updateIpTracking(id: string, updateIpTrackingDto: UpdateIpTrackingDto) {
    return await this.ipTrackingRepo.update(id, updateIpTrackingDto);
  }

  async getVisitorsRssFeed(domain?: string) {
    try {
      const queryBuilder = this.ipTrackingRepo.createQueryBuilder('ipTracking');

      if (domain) {
        queryBuilder.andWhere('ipTracking.domain = :domain', { domain });
      }

      const startDate = moment().tz('Asia/Singapore').startOf('day').toDate();
      const endDate = moment().tz('Asia/Singapore').endOf('day').toDate();

      queryBuilder.andWhere('ipTracking.createdAt >= :startDate', {
        startDate,
      });
      queryBuilder.andWhere('ipTracking.createdAt <= :endDate', { endDate });

      const data = await queryBuilder
        .andWhere(`ipTracking.countryCode = 'SG'`)
        .andWhere(`ipTracking.country = 'Singapore'`)
        .orderBy('ipTracking.createdAt', 'DESC')
        .getMany();

      return this.generateVisitorRssFeed(data, domain);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  generateVisitorRssFeed(data: IpTracking[], domainName?: string): string {
    const isProduction = process.env.NODE_ENV === 'production';

    let baseUrl;

    if (domainName) {
      baseUrl = isProduction
        ? `https://api.project.sg/ip-tracking/visitors-rss-feed?domain=${domainName}`
        : `https://staging.project.sg/api/ip-tracking/visitors-rss-feed?domain=${domainName}`;
    } else {
      baseUrl = isProduction
        ? `https://api.project.sg/ip-tracking/visitors-rss-feed`
        : `https://staging.project.sg/api/ip-tracking/visitors-rss-feed`;
    }

    const customLastBuildDate = moment()
      .tz('Asia/Singapore')
      .format('ddd, DD MMM YYYY HH:mm:ss [GMT+8]');

    const feed = new RSS({
      title: 'Visitor Activity Feed',
      description: 'RSS feed listing all recent visitors',
      site_url: baseUrl,
      language: 'en',
      custom_elements: [
        { pubDate: customLastBuildDate },
        { lastBuildDate: customLastBuildDate },
      ],
    });

    data.forEach((visitor) => {
      const pubDate = moment(visitor.createdAt)
        .tz('Asia/Singapore')
        .format('ddd, DD MMM YYYY HH:mm:ss [GMT+8]');

      const date = moment(visitor.createdAt)
        .tz('Asia/Singapore')
        .format('DD/MM/YYYY');
      const time = moment(visitor.createdAt)
        .tz('Asia/Singapore')
        .format('hh:mm A');

      const description = `Date: ${date} | Time: ${time} | IP Address: ${visitor.ip} | Country: ${visitor.country || 'Unknown'} | Domain: ${visitor.domain} | Project: ${visitor.projectName} | Browser: ${visitor.browser}`;

      feed.item({
        title: `Live Client Site Visits`,
        description,
        guid: visitor.id,
        custom_elements: [{ pubDate }],
      });
    });

    return feed.xml({ indent: true });
  }
}
