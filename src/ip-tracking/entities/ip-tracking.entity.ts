import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity } from 'typeorm';

@Entity()
export class IpTracking extends CrudEntity {
  @Column()
  projectName: string;

  @Column()
  projectId: string;

  @Column()
  projectLink: string;

  @Column()
  ip: string;

  @Column()
  region: string;

  @Column()
  browser: string;

  @Column()
  domain: string;

  @Column({
    nullable: true,
  })
  country: string;

  @Column({
    nullable: true,
  })
  countryCode: string;

  @Column({
    default: false,
  })
  isSent: boolean;
}
