import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IpTracking } from './entities/ip-tracking.entity';
import { IpTrackingController } from './ip-tracking.controller';
import { IpTrackingService } from './ip-tracking.service';
import { TelegramModule } from 'src/telegram/telegram.module';
import { UserConfigModule } from 'src/user-config/user-config.module';
import { Bot } from 'src/bot/entities/bot.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([IpTracking, Bot, UserFeature]),
    TelegramModule,
    UserConfigModule,
    RedisModule,
  ],
  controllers: [IpTrackingController],
  providers: [IpTrackingService],
  exports: [IpTrackingService],
})
export class IpTrackingModule {}
