import {
  Body,
  Controller,
  Get,
  Header,
  Headers,
  Param,
  Post,
  Query,
  Req,
  UseInterceptors,
} from '@nestjs/common';
import { IpTrackingService } from './ip-tracking.service';
import { CreateIpTrackingDto } from './dto/create-ip-tracking.dto';
import { QueryIpTrackingDto } from './dto/query-ip-tracking.dto';
import { Auth, Public } from 'src/iam/authentication/decorators/auth.decorator';
import { AuthType } from 'src/iam/authentication/enums/auth-type.enum';
import { Request } from 'express';
import { SkipResponseFormat } from 'src/core/decorators/skip-response-format.decorator';

@Auth(AuthType.None)
@Controller('ip-tracking')
export class IpTrackingController {
  constructor(private readonly ipTrackingService: IpTrackingService) {}

  @Get('visitors-rss-feed')
  @SkipResponseFormat()
  @Header('Content-Type', 'application/xml')
  @Public()
  getVisitorsRssFeedWithDomain(@Query('domain') domain: string) {
    return this.ipTrackingService.getVisitorsRssFeed(domain);
  }

  @Get()
  listing(@Query() query: QueryIpTrackingDto) {
    return this.ipTrackingService.listing(query);
  }

  @Post()
  createIpTracking(
    @Req() req: Request,
    @Headers('user-domain') userDomain: string,
    @Body() createIpTrackingDto: CreateIpTrackingDto,
  ) {
    return this.ipTrackingService.createIpTracking(
      userDomain,
      createIpTrackingDto,
      req,
    );
  }
}
