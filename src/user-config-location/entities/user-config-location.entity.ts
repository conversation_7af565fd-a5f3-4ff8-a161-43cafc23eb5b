import CrudEntity from 'src/core/entities/crud.entity';
import { Location } from 'src/location/entities/location.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { ELayout } from '../enums/layout.enum';

@Entity()
export class UserConfigLocation extends CrudEntity {
  @Column({ type: 'uuid' })
  locationId: string;

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'locationId' })
  location: Location;

  @Column({ type: 'uuid' })
  configId: string;

  @ManyToOne(() => UserConfig, (config) => config.userConfigLocations)
  @JoinColumn({ name: 'configId' })
  config: UserConfig;

  @Column({
    type: 'enum',
    enum: ELayout,
    default: ELayout.SectionGalleryThree,
  })
  layout: ELayout;

  @Column()
  background: string;

  @Column({ default: true })
  visible: boolean;
}
