import {
  IsBoolean,
  IsEnum,
  Is<PERSON>ptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ELayout } from '../enums/layout.enum';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class CreateUserConfigLocationDto {
  @IsUUID()
  locationId: string;

  @IsEnum(ELayout)
  layout: ELayout;

  @IsString()
  background: string;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  visible: boolean;
}
