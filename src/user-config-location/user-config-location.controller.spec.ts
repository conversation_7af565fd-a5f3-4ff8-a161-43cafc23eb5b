import { Test, TestingModule } from '@nestjs/testing';
import { UserConfigLocationController } from './user-config-location.controller';
import { UserConfigLocationService } from './user-config-location.service';

describe('UserConfigLocationController', () => {
  let controller: UserConfigLocationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserConfigLocationController],
      providers: [UserConfigLocationService],
    }).compile();

    controller = module.get<UserConfigLocationController>(UserConfigLocationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
