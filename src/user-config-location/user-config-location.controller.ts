import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UserConfigLocationService } from './user-config-location.service';
import { CreateUserConfigLocationDto } from './dto/create-user-config-location.dto';
import { UpdateUserConfigLocationDto } from './dto/update-user-config-location.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { UserConfigLocationQueryDto } from './dto/user-config-location-query.dto';

@Controller('user-config-location')
export class UserConfigLocationController {
  constructor(
    private readonly userConfigLocationService: UserConfigLocationService,
  ) {}

  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createUserConfigLocationDto: CreateUserConfigLocationDto,
  ) {
    return this.userConfigLocationService.create(
      userId,
      createUserConfigLocationDto,
    );
  }

  @Get()
  findAll(
    @ActiveUser('sub') userId: string,
    @Query() query: UserConfigLocationQueryDto,
  ) {
    return this.userConfigLocationService.findAll(userId, query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userConfigLocationService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUserConfigLocationDto: UpdateUserConfigLocationDto,
  ) {
    return this.userConfigLocationService.update(
      id,
      updateUserConfigLocationDto,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.userConfigLocationService.remove(id);
  }
}
