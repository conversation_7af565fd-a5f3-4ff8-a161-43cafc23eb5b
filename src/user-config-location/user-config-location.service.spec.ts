import { Test, TestingModule } from '@nestjs/testing';
import { UserConfigLocationService } from './user-config-location.service';

describe('UserConfigLocationService', () => {
  let service: UserConfigLocationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserConfigLocationService],
    }).compile();

    service = module.get<UserConfigLocationService>(UserConfigLocationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
