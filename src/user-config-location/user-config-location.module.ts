import { Module } from '@nestjs/common';
import { UserConfigLocationService } from './user-config-location.service';
import { UserConfigLocationController } from './user-config-location.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserConfigLocation } from './entities/user-config-location.entity';
import { Location } from 'src/location/entities/location.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserConfigLocation, Location, UserConfig]),
  ],
  controllers: [UserConfigLocationController],
  providers: [UserConfigLocationService],
  exports: [UserConfigLocationService],
})
export class UserConfigLocationModule {}
