import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { CreateUserConfigLocationDto } from './dto/create-user-config-location.dto';
import { UpdateUserConfigLocationDto } from './dto/update-user-config-location.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserConfigLocation } from './entities/user-config-location.entity';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UserConfigLocationQueryDto } from './dto/user-config-location-query.dto';
import { Location } from 'src/location/entities/location.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { ELayout } from './enums/layout.enum';

@Injectable()
export class UserConfigLocationService {
  constructor(
    @InjectRepository(UserConfigLocation)
    private readonly repo: Repository<UserConfigLocation>,
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
  ) {}

  async create(userId: string, body: CreateUserConfigLocationDto) {
    const location = await this.locationRepo.findOneBy({ id: body.locationId });
    if (!location) {
      throw new NotFoundException('Location is not found');
    }

    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const exist = await this.repo.findOneBy({
      configId: userConfig.id,
      locationId: body.locationId,
    });

    if (exist) {
      throw new UnprocessableEntityException('Item is existed');
    }

    return this.repo.save(Object.assign(body, { configId: userConfig.id }));
  }

  async findAll(userId: string, query: UserConfigLocationQueryDto) {
    const pagination = getPaginationOption(query);
    const queryBuilder = this.repo
      .createQueryBuilder('userConfigLocation')
      .leftJoin('userConfigLocation.config', 'config')
      .leftJoinAndSelect('userConfigLocation.location', 'location')
      .where('config.userId = :userId', { userId })
      .take(pagination.limit)
      .skip(pagination.offset);

    if (query.locationId) {
      queryBuilder.andWhere('userConfigLocation.locationId = :locationId', {
        locationId: query.locationId,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async findOne(id: string) {
    const item = await this.repo.findOneBy({
      id,
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    return item;
  }

  async update(
    id: string,
    updateUserConfigLocationDto: UpdateUserConfigLocationDto,
  ) {
    const item = await this.findOne(id);

    return await this.repo.save({ ...item, ...updateUserConfigLocationDto });
  }

  async remove(id: string) {
    const item = await this.findOne(id);
    return await this.repo.softRemove(item);
  }

  async generate(configId: string) {
    const userConfig = await this.userConfigRepo.findOneBy({ id: configId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    // skip if already have one

    const existed = await this.repo.findOneBy({ configId: userConfig.id });
    if (existed) {
      return;
    }

    const locations = await this.locationRepo.find({ take: 3 });

    const layouts = Object.values(ELayout);

    const initData = locations.map((location, idx) => {
      const item: CreateUserConfigLocationDto & { configId: string } = {
        configId,
        locationId: location.id,
        background: 'rgba(0,0,0,0)',
        layout: layouts[idx],
        visible: true,
      };

      return item;
    });

    return await this.repo.save(initData);
  }
}
