import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Bot } from './entities/bot.entity';
import { CreateBotDto } from './dto/create-bot.dto';
import { UpdateBotDto } from './dto/update-bot.dto';

@Injectable()
export class BotService {
  constructor(
    @InjectRepository(Bot)
    private readonly botRepository: Repository<Bot>,
  ) {}

  async create(createBotDto: CreateBotDto): Promise<Bot> {
    const bot = await this.botRepository.findOne({
      where: { email: createBotDto.email },
    });

    if (bot) {
      await this.botRepository.update(bot.id, {
        chatId: createBotDto.chatId,
      });

      return await this.botRepository.findOne({ where: { id: bot.id } });
    }

    return this.botRepository.save(createBotDto);
  }

  async findAllByUser(userId: string): Promise<Bot[]> {
    return this.botRepository.find({ where: { userId } });
  }

  async findAll(): Promise<Bot[]> {
    return this.botRepository.find();
  }

  async findOneById(id: string): Promise<Bot> {
    return this.botRepository.findOne({ where: { id } });
  }

  async update(id: string, bot: UpdateBotDto): Promise<Bot> {
    await this.botRepository.update(id, bot);
    return this.botRepository.findOne({ where: { id } });
  }
}
