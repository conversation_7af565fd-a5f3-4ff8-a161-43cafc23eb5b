import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { EUserSuspendStatus } from '../entities/user-suspend.entity';

export class CreateUserSuspendDto {
  @IsUUID()
  userId: string;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsString()
  date?: string;

  @IsOptional()
  @IsEnum(EUserSuspendStatus)
  status?: EUserSuspendStatus;
}

export enum EUpdateUserSuspend {
  SCHEDULE = 'schedule',
  IMMEDIATE = 'immediate',
}

export class UpdateUserSuspendDto {
  @IsUUID()
  userId: string;

  @IsEnum(EUpdateUserSuspend)
  type: EUpdateUserSuspend;

  @IsOptional()
  @IsString()
  date?: string;

  @IsString()
  reason: string;
}

export class UnsuspendUserDto {
  @IsUUID()
  userId: string;
}
