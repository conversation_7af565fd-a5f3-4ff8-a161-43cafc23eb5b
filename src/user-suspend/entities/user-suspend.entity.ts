import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

export enum EUserSuspendStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
}

@Entity()
export class UserSuspend extends CrudEntity {
  @Column({ type: 'uuid' })
  @Index()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'text', nullable: true })
  reason?: string;

  @Column({ type: 'timestamptz', nullable: true })
  @Index()
  date?: Date;

  @Column({
    type: 'enum',
    enum: EUserSuspendStatus,
    nullable: true,
  })
  status?: EUserSuspendStatus;
}
