import { Injectable } from '@nestjs/common';
import {
  EUserSuspendStatus,
  UserSuspend,
} from './entities/user-suspend.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import {
  CreateUserSuspendDto,
  EUpdateUserSuspend,
  UnsuspendUserDto,
  UpdateUserSuspendDto,
} from './dto/create-user-suspend.dto';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import * as moment from 'moment-timezone';

@Injectable()
export class UserSuspendService {
  constructor(
    @InjectRepository(UserSuspend)
    private readonly repo: Repository<UserSuspend>,

    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    @InjectQueue('user_suspend_queue') private userSuspendQueue: Queue,
  ) {}

  async create(
    createUserSuspendDto: CreateUserSuspendDto,
  ): Promise<UserSuspend> {
    return await this.repo.save({
      ...createUserSuspendDto,
    });
  }

  async getById(id: string): Promise<UserSuspend> {
    return await this.repo.findOne({
      where: { id },
      relations: ['user'],
    });
  }

  async getByUserId(userId: string): Promise<UserSuspend> {
    return await this.repo.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
  }

  async suspendUser(updateUserSuspendDto: UpdateUserSuspendDto) {
    switch (updateUserSuspendDto.type) {
      case EUpdateUserSuspend.IMMEDIATE:
        return await this.suspendImmediate(
          updateUserSuspendDto.userId,
          updateUserSuspendDto.reason,
        );

      case EUpdateUserSuspend.SCHEDULE:
        return await this.suspendSchedule(updateUserSuspendDto);

      default:
        break;
    }
  }

  async unsuspendUser(unsuspendUserDto: UnsuspendUserDto) {
    try {
      const suspendRecord = await this.repo.findOne({
        where: {
          user: { id: unsuspendUserDto.userId },
        },
      });

      const userConfig = await this.userConfigRepo.findOne({
        where: { user: { id: unsuspendUserDto.userId } },
      });

      suspendRecord.status = EUserSuspendStatus.PENDING;
      suspendRecord.reason = null;
      suspendRecord.date = null;

      userConfig.isSuspend = false;

      await Promise.all([
        this.repo.save(suspendRecord),
        this.userConfigRepo.save(userConfig),
      ]);

      this.userSuspendQueue.removeJobs(unsuspendUserDto.userId);

      return {
        message: 'Unsuspend user successfully!',
      };
    } catch (error) {
      console.log(error);
    }
  }

  async suspendImmediate(userId: string, reason: string) {
    try {
      const suspendRecord = await this.repo.findOne({
        where: { user: { id: userId }, status: EUserSuspendStatus.PENDING },
      });

      if (!suspendRecord) {
        return {
          message: 'This user is suspended',
        };
      }

      const userConfig = await this.userConfigRepo.findOne({
        where: { user: { id: userId } },
      });

      suspendRecord.reason = reason;
      suspendRecord.status = EUserSuspendStatus.ACTIVE;

      userConfig.isSuspend = true;

      await Promise.all([
        this.repo.save(suspendRecord),
        this.userConfigRepo.save(userConfig),
      ]);

      return {
        message: 'Suspend user successfully!',
      };
    } catch (error) {
      console.log(error);
    }
  }

  async suspendSchedule(updateUserSuspendDto: UpdateUserSuspendDto) {
    try {
      const suspendRecord = await this.repo.findOne({
        where: {
          user: { id: updateUserSuspendDto.userId },
          status: EUserSuspendStatus.PENDING,
          reason: IsNull(),
          date: IsNull(),
        },
      });

      if (!suspendRecord) {
        return {
          message: 'This user has been scheduled to be suspended',
        };
      }

      suspendRecord.reason = updateUserSuspendDto.reason;
      suspendRecord.date = moment
        .tz(updateUserSuspendDto.date, 'Asia/Singapore')
        .toDate();

      const currentTime = moment.tz('Asia/Singapore');
      const choosenTime = moment(updateUserSuspendDto.date);

      const differenceInMilliseconds = choosenTime.diff(
        currentTime,
        'milliseconds',
      );

      this.userSuspendQueue.add(
        'handleSuspend',
        {
          userId: updateUserSuspendDto.userId,
        },
        {
          delay: differenceInMilliseconds,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: updateUserSuspendDto.userId,
        },
      );

      await this.repo.save(suspendRecord);

      return {
        message: 'Schedule suspend user successfully!',
      };
    } catch (error) {
      console.log(error);
    }
  }
}
