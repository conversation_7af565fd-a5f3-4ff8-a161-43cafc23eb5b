import { Module } from '@nestjs/common';
import { UserSuspend } from './entities/user-suspend.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserSuspendController } from './user-suspend.controller';
import { UserSuspendService } from './user-suspend.service';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { RedisModule } from 'src/redis/redis.module';
import { BullModule } from '@nestjs/bull';
import { UserSuspendQueue } from './user-suspend.queue';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserSuspend, UserConfig]),
    RedisModule,
    BullModule.registerQueue({
      name: 'user_suspend_queue',
    }),
  ],
  controllers: [UserSuspendController],
  providers: [UserSuspendService, UserSuspendQueue],
  exports: [UserSuspendService],
})
export class UserSuspendModule {}
