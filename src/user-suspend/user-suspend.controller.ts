import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { UserSuspendService } from './user-suspend.service';
import {
  UnsuspendUserDto,
  UpdateUserSuspendDto,
} from './dto/create-user-suspend.dto';
import { UserSuspend } from './entities/user-suspend.entity';

@Controller('user-suspend')
export class UserSuspendController {
  constructor(private readonly userSuspendService: UserSuspendService) {}

  @Post()
  async suspendUser(@Body() updateUserSuspendDto: UpdateUserSuspendDto) {
    return await this.userSuspendService.suspendUser(updateUserSuspendDto);
  }

  @Post('unsuspend')
  async unsuspendUser(@Body() unsuspendUserDto: UnsuspendUserDto) {
    return await this.userSuspendService.unsuspendUser(unsuspendUserDto);
  }

  @Get('/get-by-user-id/:userId')
  getByUserId(@Param('userId') userId: string): Promise<UserSuspend> {
    return this.userSuspendService.getByUserId(userId);
  }
}
