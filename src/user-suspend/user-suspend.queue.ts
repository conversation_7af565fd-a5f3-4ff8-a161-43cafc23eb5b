import { Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { IsNull, Not, Repository } from 'typeorm';
import {
  EUserSuspendStatus,
  UserSuspend,
} from './entities/user-suspend.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';

@Processor('user_suspend_queue')
export class UserSuspendQueue {
  constructor(
    @InjectRepository(UserSuspend)
    private readonly repo: Repository<UserSuspend>,

    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
  ) {}

  @Process({
    name: 'handleSuspend',
    concurrency: 1,
  })
  async handleSuspend(job: Job<unknown>) {
    try {
      const suspendRecord = await this.repo.findOne({
        where: {
          user: { id: job.data['userId'] },
          status: EUserSuspendStatus.PENDING,
          date: Not(IsNull()),
          reason: Not(IsNull()),
        },
      });

      const userConfig = await this.userConfigRepo.findOne({
        where: { user: { id: job.data['userId'] } },
      });

      if (!suspendRecord) {
        return;
      }

      suspendRecord.status = EUserSuspendStatus.ACTIVE;

      userConfig.isSuspend = true;

      await Promise.all([
        this.repo.save(suspendRecord),
        this.userConfigRepo.save(userConfig),
      ]);
    } catch (error) {
      console.log(error);
    }
  }
}
