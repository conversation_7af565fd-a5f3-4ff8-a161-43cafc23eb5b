import { ColorSchemeDto } from '../dto/color-scheme.dto';
export const defaultColorScheme: ColorSchemeDto[] = [
  {
    slug: '/sections/header',
    colors: {
      '--color-header-background': '#F1F0EE',
      '--color-header-text': '#172C37',
      '--color-header-text-hover': '#D03243',
      '--color-header-text-active': '#D03243',
      '--color-header-divider': 'rgba(23, 44, 55, 0.25)',
    },
    header: 'Header',
  },
  {
    slug: '/sections/search-bar',
    colors: {
      '--color-filter-bar-background': '#FFFFFF',
      '--color-filter-bar-text': '#172C37',
      '--color-filter-bar-placeholder': 'rgba(23, 44, 55, 0.25)',
      '--color-search-bar-border': '#172C37',
      '--color-search-bar-background': '#FFFFFF',
      '--color-button-search-background': '#172C37',
      '--color-button-search-text': '#FFFFFF',
      '--color-filter-sidebar-range-input-background': '#FFFFFF',
      '--color-filter-sidebar-range-input-text': 'rgba(23, 44, 55, 1)',
      '--color-filter-sidebar-range-input-border': '#172C37',
    },
    header: 'Search Bar',
  },
  {
    slug: '/sections/highlight-cards',
    colors: {
      '--color-highlight-text': '#172C37',
      '--color-highlight-background': '#FFFFFF',
    },
    header: 'Highlight Cards',
  },
  {
    slug: '/sections/footer',
    colors: {
      '--color-footer-text': '#FFFFFF',
      '--color-footer-border': '#FFFFFF',
      '--color-footer-background': '#172C37',
    },
    header: 'Footer',
  },
  {
    slug: '/sections/modal-project-detail',
    colors: {
      '--color-modal-project-detail-background': '#FFFFFF',
      '--color-modal-project-detail-divide': 'rgba(23, 44, 55, 0.25)',
      '--color-modal-project-detail-text': 'rgba(23, 44, 55, 1)',
      '--color-modal-project-detail-sold-out': '#D03243',
      '--color-modal-project-detail-available': '#099628',
      '--color-modal-project-detail-show-flat-appointment-background':
        'rgba(23, 44, 55, 1)',
      '--color-modal-project-detail-show-flat-appointment-border':
        'rgba(23, 44, 55, 1)',
      '--color-modal-project-detail-show-flat-appointment-text':
        'rgba(255, 255, 255, 1)',
      '--color-modal-project-detail-sold-out-button-background': '#099628',
      '--color-modal-project-detail-sold-out-button-border': '#099628',
      '--color-modal-project-detail-sold-out-button-text':
        'rgba(255, 255, 255, 1)',
    },
    header: 'Modal Project Detail',
  },
  {
    slug: '/sections/contact-sales',
    colors: {
      '--color-contact-sales-background': '#172C37',
      '--color-contact-sales-header': '#FFFFFF',
      '--color-contact-sales-agent': '#172C37',
      '--color-contact-sales-agent-role': 'rgba(23, 44, 55, 0.5)',
      '--color-contact-sales-input-border': 'rgba(255, 255, 255, 0.25)',
      '--color-contact-sales-input-background': '#172C37',
      '--color-contact-sales-divider': 'rgba(255, 255, 255, 0.25)',
      '--color-contact-sales-text': '#FFFFFF',
      '--color-contact-sales-submit-text': '#FFFFFF',
      '--color-contact-sales-checkbox-border': 'rgba(255, 255, 255, 0.5)',
      '--color-contact-sales-submit-background': '#099628',
    },
    header: 'Contact Sales',
  },
  {
    slug: '/sections/enquiry',
    colors: {
      '--color-enquiry-background': '#F0F1EE',
      '--color-enquiry-header': '#172C37',
      '--color-enquiry-agent': '#172C37',
      '--color-enquiry-agent-role': 'rgba(23, 44, 55, 0.5)',
      '--color-enquiry-input-border': 'rgba(23, 44, 55, 0.25)',
      '--color-enquiry-input-background': 'rgba(255, 255, 255, 1)',
      '--color-enquiry-divider': 'rgba(0, 0, 0, 0.25)',
      '--color-enquiry-text': '#172C37',
      '--color-enquiry-submit-text': '#FFFFFF',
      '--color-enquiry-submit-background': '#172C37',
      '--color-enquiry-checkbox-border': 'rgba(23, 44, 55, 0.5)',
      '--color-enquiry-input-placeholder': 'rgba(23, 44, 55, 0.5)',
    },
    header: 'Enquiry',
  },
  {
    slug: '/sections/form-contact-us',
    colors: {
      '--color-form-contact-us-background': '#F0F1EE',
      '--color-form-contact-us-header': '#172C37',
      '--color-form-contact-us-input-border': 'rgba(23, 44, 55, 0.25)',
      '--color-form-contact-us-input-background': 'rgba(255, 255, 255, 1)',
      '--color-form-contact-us-text': '#172C37',
      '--color-form-contact-us-submit-text': '#FFFFFF',
      '--color-form-contact-us-submit-background': '#172C37',
      '--color-form-contact-us-checkbox-border': '#172C3740',
      '--color-form-contact-us-footer-label-border': '#172C37',
    },
    header: 'Form Contact Us',
  },
  {
    slug: '/sections/table',
    colors: {
      '--color-table-text': '#172C37',
      '--color-table-heading-text': '#FFFFFF',
      '--color-table-heading-background': '#172C37',
      '--color-table-row-divider': 'rgba(0, 0, 0, 0.25)',
    },
    header: 'Table',
  },
  {
    slug: '/sections/curated-properties',
    colors: {
      '--color-curated-properties-heading': '#172C37',
      '--color-curated-properties-background': '#F1F0EE',
    },
    header: 'Curated Properties',
  },
  {
    slug: '/sections/card',
    colors: {
      '--color-card-chip-background': 'rgba(23, 44, 55, 0.1)',
      '--color-card-chip-text': '#172C37',
      '--color-card-text': '#172C37',
      '--color-card-button-text': 'rgba(23, 44, 55, 0.35)',
      '--color-card-button-text-hover': '#172C37',
      '--color-card-button-active-text': '#FFFFFF',
      '--color-card-unit-type-heading': 'rgba(23, 44, 55, 0.35)',
      '--color-card-sold-out-text': '#D03243',
      '--color-card-feature-background': '#FFFFFF',
      '--color-card-feature-text': '#172C37',
      '--color-card-feature-address': 'rgba(23, 44, 55, 0.5)',
      '--color-card-feature-divider': 'rgba(0, 0, 0, 1)',
      '--color-card-project-background': '#FFFFFF',
      '--color-card-project-text': '#172C37',
      '--color-card-project-address': 'rgba(23, 44, 55, 0.5)',
      '--color-card-project-divider': 'rgba(0, 0, 0, 1)',
      '--color-card-project-tenure': '#D03243',
      '--color-card-project-tenure-text': '#D03243',
    },
    header: 'Card',
  },
  {
    slug: '/sections/modal-contact-us',
    colors: {
      '--color-contact-us-text': '#172C37',
      '--color-contact-us-button-text': '#FFFFFF',
      '--color-contact-us-button-background': '#099628',
      '--color-contact-us-button-background-hover': '#099628',
      '--color-contact-us-modal-background': '#FFFFFF',
      '--color-contact-us-modal-backdrop': 'rgba(0, 0, 0, 0.5)',
    },
    header: 'Modal Contact Us',
  },
  {
    slug: '/sections/filter-bar-mobile',
    colors: {
      '--color-filter-bar-mobile-placeholder': 'rgba(23, 44, 55, 0.5)',
      '--color-filter-bar-mobile-background-color': 'rgba(0, 0, 0, 0)',
      '--color-filter-bar-mobile-text': '#FFFFFF',
      '--color-filter-bar-mobile-button-background': '#172C37',
      '--color-filter-bar-mobile-button-text': '#FFFFFF',
    },
    header: 'Filter bar Mobile',
  },
  {
    slug: '/sections/section-go-to-enquiry',
    colors: {
      '--color-project-card-go-to-enquiry-background': '#172C37',
      '--color-project-card-go-to-enquiry-text': '#FFFFFF',
      '--color-project-card-go-to-enquiry-button': '#D03243',
      '--color-project-card-go-to-enquiry-button-text': '#FFFFFF',
      '--color-project-card-go-to-enquiry-button-hover': '#D03243',
    },
    header: 'Section Go to Enquiry',
  },
  {
    slug: '/sections/theme',
    colors: {
      '--color-project-heading-text': '#172C37',
      '--color-background-page-project': '#FFFFFF',
      '--color-section-cta-info-background': '#F1F0EE',
      '--color-project-card-info-text': 'rgba(23, 44, 55, 0.75)',
      '--color-project-card-detail-title-text': 'rgba(23, 44, 55, 0.25)',
      '--color-project-card-site-plan-text': 'rgba(23, 44, 55, 1)',
      '--color-background': '#F1F0EE',
      '--color-color': '#172C37',
      '--color-error': '#D03243',
      '--color-transparent': 'rgba(0, 0, 0, 0)',
      '--color-background-image': '#F1F0EE',
    },
    header: 'Theme',
  },
  {
    slug: '/sections/float-widget-button',
    colors: {
      '--color-float-widget-button-background': '#099628',
      '--color-float-widget-button-text': '#FFFFFF',
    },
    header: 'Widget for Float Button',
  },
];
