import {
  IsA<PERSON>y,
  IsBoolean,
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
  ValidateNested,
} from 'class-validator';
import { TopMessageDto } from './top-message.dto';
import { SocialLinkDto } from './social-link.dto';
import { Type } from 'class-transformer';
import { ColorSchemeDto } from './color-scheme.dto';
import { SiteMetadataDto } from './site-metadata.dto';
import { TopButtonDto } from './top-button.dto';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';
import { SalesTeamDto } from './sales-team.dto';

export class CreateUserConfigDto {
  @IsString()
  @Matches(
    /^(?!:\/\/)([a-zA-Z0-9-_]+\.)*[a-zA-Z0-9][a-zA-Z0-9-_]+\.[a-zA-Z]{2,11}?$/,
    {
      message: 'Domain must be a valid domain name',
    },
  )
  domain: string;

  @IsString()
  siteTitle: string;

  @IsString()
  siteDescription: string;

  @IsUUID()
  @IsOptional()
  siteLogoId: string;

  @IsUUID()
  @IsOptional()
  headerLogoId: string;

  @IsUUID()
  @IsOptional()
  footerLogoId: string;

  @IsUUID()
  @IsOptional()
  coverImageId: string;

  @IsUUID()
  @IsOptional()
  mobileMastheadImageId: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  @IsOptional()
  facebook?: string;

  @IsString()
  @IsOptional()
  instagram?: string;

  @IsString()
  @IsOptional()
  tiktok?: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ColorSchemeDto)
  colorScheme?: ColorSchemeDto[];

  @IsOptional()
  @ValidateNested()
  topMessage?: TopMessageDto;

  @IsArray()
  @ValidateNested({ each: true })
  @IsOptional()
  @Type(() => SocialLinkDto)
  socialLinks?: Array<SocialLinkDto>;

  @IsOptional()
  @IsEmail()
  agencyEmail?: string;

  @IsOptional()
  @IsString()
  agencyName?: string;

  @IsUUID()
  @IsOptional()
  agencyPhotoId?: string;

  @IsUUID()
  @IsOptional()
  agencyLogoId?: string;

  @IsOptional()
  @ValidateNested()
  siteMetadata?: SiteMetadataDto;

  @IsOptional()
  @ValidateNested()
  topButton?: TopButtonDto;

  @IsString()
  @IsOptional()
  twitterCreator?: string;

  @IsString()
  @IsOptional()
  twitterSite?: string;

  @IsString()
  @IsOptional()
  seoImageId?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  seoDescription?: string;

  @IsString()
  @IsOptional()
  promotionBgColor?: string;

  @IsString()
  @IsOptional()
  promotionHeading?: string;

  @IsString()
  @IsOptional()
  upcomingSeoImageId?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  upcomingSeoDescription?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  upcomingSeoTitle?: string;

  @IsString()
  @IsOptional()
  topSeoImageId?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  topSeoDescription?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  topSeoTitle?: string;

  @IsString()
  @IsOptional()
  promotionSeoImageId?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  promotionSeoDescription?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  promotionSeoTitle: string;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  floatingButtonEnabled?: boolean;

  @IsString()
  @IsOptional()
  whatsapp?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => SalesTeamDto)
  salesTeamInfo?: SalesTeamDto;

  @IsString()
  @IsOptional()
  headerAnalyticUrl?: string;

  @IsString()
  @IsOptional()
  bodyAnalyticUrl?: string;

  @IsBoolean()
  @IsOptional()
  isHiddenWhatsapp?: boolean;

  @IsBoolean()
  @IsOptional()
  isHiddenShowFlatNumber?: boolean;

  @IsBoolean()
  @IsOptional()
  isHiddenRegister?: boolean;
}
