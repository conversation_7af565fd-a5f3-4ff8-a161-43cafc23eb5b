import { OmitType, PartialType } from '@nestjs/mapped-types';
import { CreateUserConfigDto } from './create-user-config.dto';
import { ELangCode } from 'src/core/enums/lang.enum';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateUserConfigDto extends PartialType(
  OmitType(CreateUserConfigDto, ['domain']),
) {
  @IsEnum(ELangCode)
  @IsOptional()
  lang?: ELangCode;

  @IsString()
  @IsOptional()
  measuringId?: string;

  @IsString()
  @IsOptional()
  headerScript?: string;

  @IsString()
  @IsOptional()
  bodyScript?: string;

  @IsString()
  @IsOptional()
  enquiryScript?: string;

  @IsUUID()
  @IsOptional()
  agencyLogoId?: string;
}
