import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Domain } from 'src/domains/entities/domain.entity';
import { UserConfigLocation } from 'src/user-config-location/entities/user-config-location.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { TopMessageDto } from '../dto/top-message.dto';
import { SocialLinkDto } from '../dto/social-link.dto';
import { SiteMetadataDto } from '../dto/site-metadata.dto';
import { TopButtonDto } from '../dto/top-button.dto';
import { Section } from 'src/section/entities/section.entity';
import { SalesTeamDto } from '../dto/sales-team.dto';

@Entity()
export class UserConfig extends CrudEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @OneToOne(() => User, (user) => user.config)
  @JoinColumn({
    name: 'userId',
  })
  user: User;

  @Column({ nullable: true })
  measuringId?: string;

  @OneToMany(() => Domain, (domain) => domain.config)
  domains?: Domain[];

  @Column({ type: 'jsonb', nullable: true })
  siteTitle?: Record<string, string>;

  @Column({ type: 'jsonb', nullable: true })
  siteDescription?: Record<string, string>;

  @Column({ type: 'uuid', nullable: true })
  siteLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'siteLogoId' })
  siteLogo?: Asset;

  @Column({ type: 'uuid', nullable: true })
  headerLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'headerLogoId' })
  headerLogo?: Asset;

  @Column({ type: 'uuid', nullable: true })
  footerLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'footerLogoId' })
  footerLogo?: Asset;

  @Column({ type: 'uuid', nullable: true })
  coverImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'coverImageId' })
  coverImage?: Asset;

  @Column({ type: 'uuid', nullable: true })
  mobileMastheadImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'mobileMastheadImageId' })
  mobileMastheadImage?: Asset;

  @Column({ nullable: true })
  phoneNumber?: string;

  @Column({ nullable: true })
  facebook?: string;

  @Column({ nullable: true })
  instagram?: string;

  @Column({ nullable: true })
  tiktok?: string;

  @OneToMany(
    () => UserConfigLocation,
    (userConfigLocation) => userConfigLocation.config,
  )
  userConfigLocations: UserConfigLocation[];

  @Column({ type: 'jsonb', nullable: true })
  colorScheme?: object;

  @Column({ type: 'jsonb', nullable: true })
  topMessage?: TopMessageDto;

  @Column({ type: 'jsonb', nullable: true })
  socialLinks?: Array<SocialLinkDto>;

  @Column({ nullable: true })
  agencyEmail?: string;

  @Column({ nullable: true })
  agencyName?: string;

  @Column({ nullable: true, type: 'uuid' })
  agencyPhotoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'agencyPhotoId' })
  agencyPhoto?: Asset;

  @Column({ type: 'jsonb', nullable: true })
  siteMetadata?: SiteMetadataDto;

  @Column({ type: 'jsonb', nullable: true })
  topButton?: TopButtonDto;

  @OneToMany(() => Section, (section) => section.config)
  sections: Section;

  @Column({ nullable: true })
  twitterCreator?: string;

  @Column({ nullable: true })
  twitterSite?: string;

  @Column({ type: 'uuid', nullable: true })
  seoImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'seoImageId' })
  seoImage?: Asset;

  @Column({ length: 200, nullable: true })
  seoDescription?: string;

  @Column({ nullable: true })
  promotionBgColor?: string;

  @Column({ nullable: true })
  promotionHeading?: string;

  @Column({ type: 'uuid', nullable: true })
  promotionSeoImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'promotionSeoImageId' })
  promotionSeoImage?: Asset;

  @Column({ length: 200, nullable: true })
  promotionSeoDescription?: string;

  @Column({ length: 200, nullable: true })
  promotionSeoTitle?: string;

  @Column({ type: 'uuid', nullable: true })
  upcomingSeoImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'upcomingSeoImageId' })
  upcomingSeoImage?: Asset;

  @Column({ length: 200, nullable: true })
  upcomingSeoDescription?: string;

  @Column({ length: 200, nullable: true })
  upcomingSeoTitle?: string;

  @Column({ type: 'uuid', nullable: true })
  topSeoImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'topSeoImageId' })
  topSeoImage?: Asset;

  @Column({ length: 200, nullable: true })
  topSeoDescription?: string;

  @Column({ length: 200, nullable: true })
  topSeoTitle?: string;

  @Column({ default: true })
  floatingButtonEnabled?: boolean;

  @Column({ nullable: true })
  whatsapp?: string;

  @Column({ type: 'jsonb', nullable: true })
  salesTeamInfo?: SalesTeamDto;

  @Column({ default: false })
  isSuspend: boolean;

  @Column({ nullable: true })
  headerScript?: string;

  @Column({ nullable: true })
  bodyScript?: string;

  @Column({ nullable: true })
  enquiryScript?: string;

  @Column({ nullable: true, default: false })
  isHiddenWhatsapp?: boolean;

  @Column({ nullable: true, default: false })
  isHiddenShowFlatNumber?: boolean;

  @Column({ nullable: true, default: false })
  isHiddenRegister?: boolean;
}
