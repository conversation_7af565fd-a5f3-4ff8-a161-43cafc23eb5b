import { Module } from '@nestjs/common';
import { UserConfigService } from './user-config.service';
import { UserConfigController } from './user-config.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserConfig } from './entities/user-config.entity';
import { DomainsModule } from 'src/domains/domains.module';
import { AssetModule } from 'src/asset/asset.module';
import { UserConfigLocationModule } from 'src/user-config-location/user-config-location.module';
import { RedisModule } from 'src/redis/redis.module';
import { SiteContentModule } from 'src/site-content/site-content.module';
import { SectionModule } from 'src/section/section.module';
import { NotificationModule } from 'src/notification/notification.module';
import { User } from 'src/users/entities/user.entity';
import { UserSuspendModule } from 'src/user-suspend/user-suspend.module';
import { LandingpagesControlModule } from 'src/landingpages-control/landingpages-control.module';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserConfig, User, LandingpagesControl]),
    SiteContentModule,
    DomainsModule,
    AssetModule,
    UserConfigLocationModule,
    RedisModule,
    SectionModule,
    NotificationModule,
    UserSuspendModule,
  ],
  controllers: [UserConfigController],
  providers: [UserConfigService],
  exports: [UserConfigService],
})
export class UserConfigModule {}
