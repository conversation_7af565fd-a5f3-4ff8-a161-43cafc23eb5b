import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { CreateUserConfigDto } from './dto/create-user-config.dto';
import { UpdateUserConfigDto } from './dto/update-user-config.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UserConfig } from './entities/user-config.entity';
import { And, IsNull, Not, Repository } from 'typeorm';
import { DomainsService } from 'src/domains/domains.service';
import { ELangCode } from 'src/core/enums/lang.enum';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { defaultColorScheme } from './constants/color-scheme.constant';
import { UserConfigLocationService } from 'src/user-config-location/user-config-location.service';
import { RedisService } from 'src/redis/redis.service';
import { SiteContentService } from 'src/site-content/site-content.service';
import { SectionService } from 'src/section/section.service';
import { UploadImageDto } from 'src/asset/dto/upload-image.dto';
import { NotificationService } from 'src/notification/notification.service';
import { User } from 'src/users/entities/user.entity';
import { UserSuspendService } from 'src/user-suspend/user-suspend.service';
import { EUserSuspendStatus } from 'src/user-suspend/entities/user-suspend.entity';
import { LandingpagesControl } from 'src/landingpages-control/entities/landingpages-control.entity';
import { ColorSchemeDto } from './dto/color-scheme.dto';

@Injectable()
export class UserConfigService {
  constructor(
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly domainsService: DomainsService,
    private readonly assetService: AssetService,
    private readonly locationConfigService: UserConfigLocationService,
    private readonly redisService: RedisService,
    private readonly siteContentService: SiteContentService,
    private readonly sectionService: SectionService,
    private readonly notificationService: NotificationService,
    private readonly userSuspendService: UserSuspendService,
    @InjectRepository(LandingpagesControl)
    private readonly landingPagesControlRepository: Repository<LandingpagesControl>,
  ) {}

  async create(userId: string, createUserConfigDto: CreateUserConfigDto) {
    const exist = await this.userConfigRepo.findOneBy({
      userId,
    });

    if (exist) {
      throw new UnprocessableEntityException(
        'A config already has been created for this user',
      );
    }
    this.domainsService.validateSubdomain(createUserConfigDto.domain);
    await this.domainsService.throwIfExist(createUserConfigDto.domain);

    if (createUserConfigDto.agencyLogoId) {
      await this.userRepo.update(
        { id: userId },
        { agencyLogoId: createUserConfigDto.agencyLogoId },
      );

      delete createUserConfigDto.agencyLogoId;
    }

    const data: Partial<UserConfig> = {
      ...createUserConfigDto,
      userId,
      colorScheme: createUserConfigDto.colorScheme ?? defaultColorScheme,
      siteDescription: { [ELangCode.en]: createUserConfigDto.siteDescription },
      siteTitle: { [ELangCode.en]: createUserConfigDto.siteTitle },
    };

    const res = await this.userConfigRepo.save(data);

    const domain = await this.domainsService.create({
      name: createUserConfigDto.domain,
      configId: res.id,
      primary: true,
    });

    res.domains = [domain];

    if (createUserConfigDto.coverImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.coverImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    } else {
      const coverImage: UploadImageDto = { fileName: 'cover-image.jpg' };
      const image = await this.assetService.uploadImage(coverImage);
      await this.assetService
        .addAssetRelation(image.id, EAssetRelation.UserConfig, res.id)
        .catch(console.error);
      await this.userConfigRepo.update(
        { id: res.id },
        { coverImageId: image.id },
      );
      res.coverImage = image;
    }

    if (createUserConfigDto.footerLogoId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.footerLogoId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    } else {
      const footerLogo: UploadImageDto = { fileName: 'logo-footer.png' };

      const logo = await this.assetService.uploadImage(footerLogo, null, {
        lossless: true,
      });
      await this.assetService
        .addAssetRelation(logo.id, EAssetRelation.UserConfig, res.id)
        .catch(console.error);
      await this.userConfigRepo.update(
        { id: res.id },
        { footerLogoId: logo.id },
      );
      res.footerLogoId = logo.id;
    }

    if (createUserConfigDto.headerLogoId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.headerLogoId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    } else {
      const headerLogo: UploadImageDto = { fileName: 'logo-header.png' };
      const logo = await this.assetService.uploadImage(headerLogo, null, {
        lossless: true,
      });
      await this.assetService
        .addAssetRelation(logo.id, EAssetRelation.UserConfig, res.id)
        .catch(console.error);
      await this.userConfigRepo.update(
        { id: res.id },
        { headerLogoId: logo.id },
      );
      res.headerLogoId = logo.id;
    }

    if (createUserConfigDto.siteLogoId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.siteLogoId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    } else {
      const siteLogo: UploadImageDto = { fileName: 'logo-favicon.png' };

      const logo = await this.assetService.uploadImage(siteLogo, null, {
        lossless: true,
      });
      await this.assetService
        .addAssetRelation(logo.id, EAssetRelation.UserConfig, res.id)
        .catch(console.error);
      await this.userConfigRepo.update({ id: res.id }, { siteLogoId: logo.id });
      res.siteLogoId = logo.id;
    }

    if (createUserConfigDto.agencyPhotoId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.agencyPhotoId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    if (createUserConfigDto.seoImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.seoImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    if (createUserConfigDto.promotionSeoImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.promotionSeoImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    if (createUserConfigDto.upcomingSeoImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.upcomingSeoImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    if (createUserConfigDto.topSeoImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.topSeoImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    if (createUserConfigDto.mobileMastheadImageId) {
      await this.assetService
        .addAssetRelation(
          createUserConfigDto.mobileMastheadImageId,
          EAssetRelation.UserConfig,
          res.id,
        )
        .catch(console.error);
    }

    await this.userSuspendService.create({
      userId: res.userId,
      status: EUserSuspendStatus.PENDING,
    });

    // await this.locationConfigService.generate(res.id).catch(console.error);
    await this.siteContentService
      .generateDefaultSiteContent(res.userId)
      .catch(console.error);
    await this.sectionService.addDefaultSectionAndSectionProjects(userId);
    return await this.userConfigRepo.findOneBy({ id: res.id });
  }

  async findOneByUser(userId: string) {
    const item = await this.userConfigRepo.findOne({
      where: { userId },
      relations: {
        domains: true,
        headerLogo: true,
        siteLogo: true,
        footerLogo: true,
        coverImage: true,
        seoImage: true,
        agencyPhoto: true,
        promotionSeoImage: true,
        topSeoImage: true,
        upcomingSeoImage: true,
        mobileMastheadImage: true,
        user: {
          photo: true,
          agencyLogo: true,
          userFeature: true,
        },
      },
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    const primaryDomain = item.domains?.find(
      (domain) => domain?.primary === true,
    );

    item['domains'] = [primaryDomain];
    item['colorScheme'] = this.handleColorSchema(item.colorScheme);

    return item;
  }

  handleColorSchema(colorSchemeData?: object) {
    //handle color in constants
    if (Array.isArray(colorSchemeData)) {
      const _colorSchemeData = defaultColorScheme.map((colorScheme) => {
        const foundColorScheme = (colorSchemeData as ColorSchemeDto[]).find(
          (color) => color?.slug === colorScheme.slug,
        );

        const mergedColors: Record<string, string> = {};
        const baseColors = colorScheme.colors || {};
        const overrideColors = foundColorScheme?.colors || {};

        Object.keys(baseColors).forEach((key) => {
          mergedColors[key] = overrideColors[key] || baseColors[key];
        });

        return {
          ...colorScheme,
          ...foundColorScheme,
          colors: mergedColors,
        };
      });
      return _colorSchemeData;
    }
    return colorSchemeData;
  }

  async findOne(id: string) {
    const item = await this.userConfigRepo.findOne({
      where: { id },
      relations: {
        domains: true,
        headerLogo: true,
        siteLogo: true,
        footerLogo: true,
        coverImage: true,
        agencyPhoto: true,
        promotionSeoImage: true,
        topSeoImage: true,
        upcomingSeoImage: true,
        mobileMastheadImage: true,
        user: {
          photo: true,
          agencyLogo: true,
        },
      },
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    return item;
  }

  async findOneByDomain(domainName: string, lang = ELangCode.en) {
    console.log(
      'UserConfigService -> findOneByDomain -> domainName',
      domainName,
    );

    const item = await this.domainsService.getDomainByName(domainName);

    if (!item) {
      throw new NotFoundException('Domain is not found');
    }

    const domains = await this.domainsService.getDomainsByConfigId(
      item.configId,
    );

    const primaryDomain = domains.find((domain) => domain.primary === true);
    console.log('primaryDomain', primaryDomain);

    const userConfigByDomain = await this.userConfigRepo.findOne({
      where: {
        domains: {
          name: primaryDomain.name,
        },
      },
      relations: {
        domains: true,
        headerLogo: true,
        siteLogo: true,
        footerLogo: true,
        coverImage: true,
        seoImage: true,
        agencyPhoto: true,
        promotionSeoImage: true,
        topSeoImage: true,
        upcomingSeoImage: true,
        mobileMastheadImage: true,
        user: {
          photo: true,
          agencyLogo: true,
          userFeature: {
            feature: true,
          },
        },
      },
    });

    userConfigByDomain['siteDisclaimers'] =
      await this.notificationService.getSiteDisclaimers();

    const landingPageFeatureControl =
      await this.landingPagesControlRepository.findOneBy({
        userId: userConfigByDomain.user.id,
      });

    // For  ldp script use default or not => false use user's scripts => true use default scripts
    userConfigByDomain['useProjectSgTrackingId'] =
      !!landingPageFeatureControl?.useProjectSgTrackingId;
    userConfigByDomain['colorScheme'] = this.handleColorSchema(
      userConfigByDomain.colorScheme,
    );
    return this.formatResponse(userConfigByDomain, lang);
  }

  async update(id: string, body: UpdateUserConfigDto) {
    const lang = body.lang || ELangCode.en;

    const item = await this.userConfigRepo.findOne({
      where: { id },
      relations: {
        domains: true,
      },
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    if (body.agencyLogoId !== undefined) {
      await this.userRepo.update(
        { id: item.userId },
        { agencyLogoId: body.agencyLogoId },
      );

      delete body.agencyLogoId;
    }

    const data: Partial<UserConfig> = {
      ...body,
      siteTitle: {
        ...item.siteTitle,
        [lang]: body.siteTitle ?? item.siteTitle?.[lang],
      },
      siteDescription: {
        ...item.siteDescription,
        [lang]: body.siteDescription ?? item.siteDescription?.[lang],
      },
    };

    const res = await this.userConfigRepo.update({ id: item.id }, data);
    try {
      if (body.coverImageId !== item.coverImageId) {
        if (item.coverImageId) {
          await this.assetService.remove(item.coverImageId);
        }
        if (body.coverImageId) {
          await this.assetService.addAssetRelation(
            body.coverImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.footerLogoId !== item.footerLogoId) {
        if (item.footerLogoId) {
          await this.assetService.remove(item.footerLogoId);
        }
        if (body.footerLogoId) {
          await this.assetService.addAssetRelation(
            body.footerLogoId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.headerLogoId !== item.headerLogoId) {
        if (item.headerLogoId) {
          await this.assetService.remove(item.headerLogoId);
        }
        if (body.headerLogoId) {
          await this.assetService.addAssetRelation(
            body.headerLogoId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.siteLogoId !== item.siteLogoId) {
        if (item.siteLogoId) {
          await this.assetService.remove(item.siteLogoId);
        }
        if (body.siteLogoId) {
          await this.assetService.addAssetRelation(
            body.siteLogoId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.seoImageId !== item.seoImageId) {
        if (item.seoImageId) {
          await this.assetService.remove(item.seoImageId);
        }
        if (body.seoImageId) {
          await this.assetService.addAssetRelation(
            body.seoImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.promotionSeoImageId !== item.promotionSeoImageId) {
        if (item.promotionSeoImageId) {
          await this.assetService.remove(item.promotionSeoImageId);
        }
        if (body.promotionSeoImageId) {
          await this.assetService.addAssetRelation(
            body.promotionSeoImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.upcomingSeoImageId !== item.upcomingSeoImageId) {
        if (item.upcomingSeoImageId) {
          await this.assetService.remove(item.upcomingSeoImageId);
        }
        if (body.upcomingSeoImageId) {
          await this.assetService.addAssetRelation(
            body.upcomingSeoImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.topSeoImageId !== item.topSeoImageId) {
        if (item.topSeoImageId) {
          await this.assetService.remove(item.topSeoImageId);
        }
        if (body.topSeoImageId) {
          await this.assetService.addAssetRelation(
            body.topSeoImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
      if (body.mobileMastheadImageId !== item.mobileMastheadImageId) {
        if (item.mobileMastheadImageId) {
          await this.assetService.remove(item.mobileMastheadImageId);
        }
        if (body.mobileMastheadImageId) {
          await this.assetService.addAssetRelation(
            body.mobileMastheadImageId,
            EAssetRelation.UserConfig,
            item.id,
          );
        }
      }
    } catch {}

    if (body.measuringId) {
      await this.userRepo.update(
        { id: item.userId },
        { measuringId: body.measuringId },
      );
    }

    await this.redisService.deletePattern('user-config@*');

    return await this.findOne(item.id);
  }

  async autoFillPhoneNumberConfig() {
    const allRecordsNotHasPhoneNumber = await this.userConfigRepo.find({
      // Find all records that have phoneNumber is null or ''
      where: [{ phoneNumber: IsNull() }, { phoneNumber: '' }],
    });

    allRecordsNotHasPhoneNumber.forEach(async (record) => {
      const user = await this.userRepo.findOne({
        // Find user that has phone number and not soft deleted
        where: {
          id: record.userId,
          phone: And(Not(IsNull()), Not('')),
          deletedAt: IsNull(),
        },
      });

      if (!!user?.phone) {
        await this.userConfigRepo.update(
          { id: record.id },
          { phoneNumber: user.phone },
        );
      }
    });
  }

  formatResponse = (
    item: UserConfig,
    lang: ELangCode,
    fallback = ELangCode.en,
  ) => {
    return {
      ...item,
      siteTitle: getLangValue(lang, item.siteTitle, fallback),
      siteDescription: getLangValue(lang, item.siteDescription, fallback),
    };
  };
}
