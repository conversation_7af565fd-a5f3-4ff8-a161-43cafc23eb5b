import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  Headers,
  UseInterceptors,
} from '@nestjs/common';
import { UserConfigService } from './user-config.service';
import { CreateUserConfigDto } from './dto/create-user-config.dto';
import { UpdateUserConfigDto } from './dto/update-user-config.dto';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { RequireHeader } from '../core/decorators/require-header.decorator';
import { ActiveUser } from '../iam/authentication/decorators/active-user.decorator';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { Roles } from 'src/users/entities/user.entity';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';

@Controller('user-config')
@CacheOption('user-config')
export class UserConfigController {
  constructor(private readonly userConfigService: UserConfigService) {}

  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createUserConfigDto: CreateUserConfigDto,
  ) {
    return this.userConfigService.create(userId, createUserConfigDto);
  }

  @Get('/by-domain')
  @Public()
  @RequireHeader('user-domain')
  @UseInterceptors(CacheInterceptor)
  findByDomain(
    @Query() query: LanguageQueryDto,
    @Headers('user-domain') domain: string,
  ) {
    return this.userConfigService.findOneByDomain(domain, query.lang);
  }

  @Get('/my-config')
  getMyConfig(@ActiveUser('sub') userId: string) {
    return this.userConfigService.findOneByUser(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userConfigService.findOne(id);
  }

  @Get('/by-user/:userId')
  findByUser(@Param('userId') userId: string) {
    return this.userConfigService.findOneByUser(userId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUserConfigDto: UpdateUserConfigDto,
  ) {
    return this.userConfigService.update(id, updateUserConfigDto);
  }

  @AccessRoles(Roles.ADMIN)
  @Post('/auto-fill-phone-number-by-user-phone')
  autoFillPhoneNumber() {
    return this.userConfigService.autoFillPhoneNumberConfig();
  }
}
