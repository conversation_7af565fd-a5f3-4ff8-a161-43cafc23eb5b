import { forwardRef, Module } from '@nestjs/common';
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Project } from './entities/project.entity';
import { AssetModule } from 'src/asset/asset.module';
import { CategoryModule } from 'src/category/category.module';
import { SitePlanModule } from 'src/site-plan/site-plan.module';
import { FloorPlanModule } from 'src/floor-plan/floor-plan.module';
import { LocationModule } from 'src/location/location.module';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { Asset } from 'src/asset/entities/asset.entity';
import { ProjectAmenityModule } from '../project-amenity/project-amenity.module';
import { VirtualTourModule } from 'src/virtual-tour/virtual-tour.module';
import { RedisModule } from 'src/redis/redis.module';
import { DeveloperModule } from 'src/developer/developer.module';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { User } from 'src/users/entities/user.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { Section } from 'src/section/entities/section.entity';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { BullModule } from '@nestjs/bull';
import { ProjectQueue } from './project.queue';
import { UserProjectModule } from 'src/user-project/user-project.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      Asset,
      UserProject,
      User,
      UserProject,
      SectionProject,
      Section,
      Unit,
      UnitTransaction,
      FloorPlan,
    ]),
    AssetModule,
    CategoryModule,
    SitePlanModule,
    FloorPlanModule,
    LocationModule,
    UnitTypeModule,
    ProjectAmenityModule,
    forwardRef(() => VirtualTourModule),
    RedisModule,
    DeveloperModule,
    BullModule.registerQueue({
      name: 'project_queue',
    }),
    forwardRef(() => UserProjectModule),
  ],
  controllers: [ProjectController],
  providers: [ProjectService, ProjectQueue],
  exports: [ProjectService],
})
export class ProjectModule {}
