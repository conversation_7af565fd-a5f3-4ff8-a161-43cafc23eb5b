import { Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { Repository } from 'typeorm';
import { Project } from './entities/project.entity';
import { ProjectService } from './project.service';
import puppeteer, { <PERSON>rows<PERSON> } from 'puppeteer-core';
import { BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Processor('project_queue')
export class ProjectQueue {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    private readonly projectService: ProjectService,
    private readonly configService: ConfigService,
  ) {}

  @Process({
    name: 'updateCoordinates',
    concurrency: 1,
  })
  async updateCoordinates(job: Job<Project[]>) {
    const { data } = job;
    if (!data || data.length === 0) {
      return;
    }

    const browser = await puppeteer.launch({
      headless: true,
      //disabled this row on localhost
      executablePath: this.configService.get<string>('CHROME_PATH'),
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      for await (const project of data) {
        try {
          const coordinates = await this.projectService.getCoordinatesInIFrame(
            project.googleMapUrl,
            browser,
          );

          if (coordinates) {
            const { latitude, longitude } = coordinates;
            await this.projectRepo.update(
              { id: project.id },
              { coordinates: `${latitude}, ${longitude}` },
            );
          }
        } catch (error) {
          console.log('Error handle:', project.googleMapUrl);
        }
      }
    } catch (error) {
      console.log('Error', error);
      throw new BadRequestException(error);
    } finally {
      await browser.close();
    }
  }

  @Process({
    name: 'updateCoordinateByProjectId',
    concurrency: 1,
  })
  async updateCoordinateByProjectId(job: Job<{ projectId: string }>) {
    const { data } = job;
    if (!data || !data.projectId) {
      return;
    }

    let browser: Browser;
    try {
      const project = await this.projectRepo.findOne({
        where: { id: data.projectId },
      });

      if (project && project.googleMapUrl) {
        browser = await puppeteer.launch({
          headless: true,
          executablePath: this.configService.get<string>('CHROME_PATH'),
        });

        const coordinates = await this.projectService.getCoordinatesInIFrame(
          project.googleMapUrl,
          browser,
        );

        if (coordinates) {
          const { latitude, longitude } = coordinates;
          await this.projectRepo.update(
            { id: project.id },
            { coordinates: `${latitude}, ${longitude}` },
          );
        }
      }
    } catch (error) {
      console.log('Error', error);
      throw new BadRequestException(error);
    } finally {
      browser && (await browser.close());
    }
  }
}
