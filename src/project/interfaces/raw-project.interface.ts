import { EMarketSegment } from '../enums/market-segment.enum';

export interface IProjectRaw {
  id: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  name?: string;
  description?: string;
  location?: string;
  address?: string;
  coordinates?: string;
  area?: number;
  tenure?: number;
  tenureEffectFrom?: string;
  marketSegment?: EMarketSegment;
  expectedTop?: EMarketSegment;
  photo_id?: string;
  photo_urls?: string;
  photo_type?: string;
  developer_id?: string;
  developer_name?: string;
  category_id?: string;
  category_name?: string;
  category_shortname?: string;
  category_slug?: string;
  location_id?: string;
  location_name?: string;
}
