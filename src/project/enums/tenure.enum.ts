export enum ETenure {
  Freehold = 'freehold',
  Yr999 = '999-yr',
  Yr99 = '99-yr',
  Yr101 = '101-yr',
}

export const TENURE_ITEM = {
  [ETenure.Freehold]: {
    id: ETenure.Freehold,
    name: 'Freehold',
  },
  [ETenure.Yr99]: {
    id: ETenure.Yr99,
    name: '99 years',
  },
  [ETenure.Yr101]: {
    id: ETenure.Yr101,
    name: '101 years',
  },
  [ETenure.Yr999]: {
    id: ETenure.Yr999,
    name: '999 years',
  },
};
