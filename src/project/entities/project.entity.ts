import { Asset } from 'src/asset/entities/asset.entity';
import { Category } from 'src/category/entities/category.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { ProjectAmenity } from 'src/project-amenity/entities/project-amenity.entity';
import { SitePlan } from 'src/site-plan/entities/site-plan.entity';
import { Location } from 'src/location/entities/location.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { EMarketSegment } from '../enums/market-segment.enum';
import { Developer } from 'src/developer/entities/developer.entity';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { ETenure } from '../enums/tenure.enum';
import {
  EProjectStatus,
  EProjectWhatIsNextStatus,
} from '../enums/project-status.enum';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { VirtualTour } from 'src/virtual-tour/entities/virtual-tour.entity';
import { ETOPStatus } from '../enums/top-status.enum';
import { Unit } from 'src/unit/entities/unit.entity';

@Entity()
export class Project extends CrudEntity {
  @Column({
    type: 'jsonb',
  })
  name: Record<string, string>;

  @Column({
    type: 'jsonb',
  })
  description: Record<string, string>;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  keyPoint: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  photoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo?: Asset;

  media: Asset[];

  @Column({
    type: 'enum',
    enum: EMarketSegment,
    nullable: true,
  })
  marketSegment: EMarketSegment;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  developerId?: string;

  @ManyToOne(() => Developer)
  @JoinColumn({ name: 'developerId' })
  developer?: Developer;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  categoryId?: string;

  @ManyToOne(() => Category, (category) => category.projects)
  @JoinColumn({ name: 'categoryId' })
  category?: Category;

  @Column({ nullable: true, type: 'jsonb' })
  address?: Record<string, string>;

  @Column({ nullable: true, type: 'uuid' })
  locationId?: string;

  @ManyToOne(() => Location, (location) => location.projects)
  @JoinColumn({ name: 'locationId' })
  location?: Location;

  @Column({ nullable: true })
  coordinates?: string;

  @Column({
    type: 'float',
    default: 0,
  })
  area: number;

  @Column({ nullable: true })
  expectedTop?: Date;

  @Column({
    type: 'enum',
    enum: ETenure,
    nullable: true,
  })
  tenure?: ETenure;

  @Column({ nullable: true })
  tenureEffectFrom?: Date;

  @OneToMany(() => FloorPlan, (floorPlan) => floorPlan.project)
  floorPlans: FloorPlan[];

  @OneToMany(() => ProjectAmenity, (projectAmenity) => projectAmenity.project)
  projectAmenities: ProjectAmenity[];

  @OneToMany(() => SitePlan, (sitePlan) => sitePlan.project)
  sitePlans: SitePlan[];

  @Column({ type: 'uuid', nullable: true })
  createdByUserId?: string;

  @OneToMany(() => UserProject, (userProject) => userProject.project)
  userProjects: UserProject[];

  @Column({ type: 'enum', enum: EProjectStatus, nullable: true })
  status?: EProjectStatus;

  @OneToMany(() => VirtualTour, (virtualTour) => virtualTour.project)
  virtualTours?: Array<VirtualTour>;

  @OneToMany(() => UnitTransaction, (transaction) => transaction.project)
  transactions: UnitTransaction[];

  @OneToMany(
    () => ContactSaleSubmission,
    (contactSaleSubmission) => contactSaleSubmission.project,
  )
  contactSaleSubmission: ContactSaleSubmission[];

  @Column({ nullable: true })
  googleMapUrl?: string;

  @Column({ nullable: true })
  showflatLocation?: string;

  @Column({ nullable: true })
  isShowflatLocation?: boolean;

  @Column({
    nullable: true,
    type: 'uuid',
  })
  sitePlanImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'sitePlanImageId' })
  sitePlanImage?: Asset;

  @Column({
    nullable: true,
    type: 'uuid',
  })
  elevationChartId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'elevationChartId' })
  elevationChart?: Asset;

  @Column({
    type: 'enum',
    enum: ETOPStatus,
    nullable: true,
  })
  topStatus: ETOPStatus;

  @Column({
    nullable: true,
  })
  upcomingLaunch: boolean;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  detail: Record<string, string>;

  @Column({ type: 'uuid', nullable: true })
  seoImageId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'seoImageId' })
  seoImage?: Asset;

  @Column({ length: 200, nullable: true })
  seoDescription?: string;

  @Column({ nullable: true })
  seoTitle?: string;

  @Column({ nullable: true, type: 'jsonb' })
  keywords?: string[];

  @Column({ type: 'jsonb', nullable: true })
  amenityHtml?: Record<string, string>;

  @Column({ type: 'jsonb', nullable: true })
  facilityHtml?: Record<string, string>;

  @Column({
    type: 'float',
    default: 0,
  })
  directCommission?: number;

  @Column({
    nullable: true,
    default: false,
  })
  isCommissionUpTo?: boolean;

  @Column({ nullable: true, default: false })
  promotion?: boolean;

  @Column({ nullable: true, default: false })
  featured?: boolean;

  @Column({ nullable: true })
  manualAdded?: boolean;

  @OneToMany(() => Unit, (unit) => unit.project)
  units: Unit[];

  @Column({
    default: 0,
  })
  manualTotalUnitsCount: number;

  @Column({
    default: 0,
  })
  manualAvailableUnitsCount: number;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  mobileLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'mobileLogoId' })
  mobileLogo?: Asset;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  desktopLogoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'desktopLogoId' })
  desktopLogo?: Asset;

  @OneToMany(() => Asset, (asset) => asset.project)
  siteplanImages: Asset[];

  @Column({
    type: 'enum',
    enum: EProjectWhatIsNextStatus,
    nullable: false,
    default: EProjectWhatIsNextStatus.PreLaunch,
  })
  whatIsNextStatus?: EProjectWhatIsNextStatus;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  brochureAssetId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'brochureAssetId' })
  brochureAsset?: Asset;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: [
      EProjectWhatIsNextStatus.PreLaunch,
      EProjectWhatIsNextStatus.Preview,
      EProjectWhatIsNextStatus.PostLaunch,
    ],
  })
  whatIsNextStatusOptions?: EProjectWhatIsNextStatus[];
}
