import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { CreateProjectDto } from './dto/create-project.dto';
import { ProjectService } from './project.service';
import { ProjectQueryDto } from './dto/project-query.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import * as _ from 'lodash';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';

@Controller('project')
@CacheOption('project')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  @Post()
  create(@Body() body: CreateProjectDto, @ActiveUser('sub') userId: string) {
    return this.projectService.create(body, userId);
  }

  @Get(':slugOrId')
  @UseInterceptors(CacheInterceptor)
  detail(
    @Param('slugOrId') slugOrId: string,
    @ActiveUser('sub') userId: string,
    @Query() query: LanguageQueryDto,
  ) {
    return this.projectService.detail(userId, slugOrId, query.lang);
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  listing(@ActiveUser('sub') userId: string, @Query() query: ProjectQueryDto) {
    const isOnlyUserId =
      _.isEmpty(_.omit(query, ['userId'])) && query.userId !== undefined;

    if (_.isEmpty(query) || isOnlyUserId) {
      return this.projectService.listingSelection(query);
    } else {
      return this.projectService.listing(userId, query);
    }
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() body: UpdateProjectDto,
    @ActiveUser('sub') userId: string,
  ) {
    return this.projectService.update(id, body);
  }

  @Delete(':id')
  delete(@Param('id') id: string, @ActiveUser('sub') userId: string) {
    return this.projectService.delete(id);
  }

  @AccessRoles(Roles.ADMIN)
  @Put('update-coordinates')
  async updateCoordinates() {
    return await this.projectService.updateCoordinates();
  }

  @AccessRoles(Roles.ADMIN)
  @Put('test-puppeteer')
  async testPuppeteer() {
    console.log;
    return await this.projectService.testPuppeteer();
  }
}
