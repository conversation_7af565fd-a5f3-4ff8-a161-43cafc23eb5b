import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Length,
} from 'class-validator';
import { EMarketSegment } from '../enums/market-segment.enum';
import { ETenure } from '../enums/tenure.enum';
import {
  EProjectStatus,
  EProjectWhatIsNextStatus,
} from '../enums/project-status.enum';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class CreateProjectDto {
  @IsString()
  name: string;

  @IsUUID()
  locationId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  keyPoint?: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsUUID()
  @IsOptional()
  photoId?: string;

  @IsEnum(EMarketSegment)
  @IsOptional()
  marketSegment?: EMarketSegment;

  @IsUUID()
  @IsOptional()
  developerId?: string;

  @IsUUID()
  @IsOptional()
  categoryId?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  showflatLocation?: string;

  @IsBoolean()
  @IsOptional()
  isShowflatLocation?: boolean;

  @IsString()
  @IsOptional()
  coordinates?: string;

  @IsString()
  @IsOptional()
  googleMapUrl?: string;

  @IsNumber()
  @IsOptional()
  area?: number;

  @IsDate()
  @IsOptional()
  expectedTop?: Date;

  @IsEnum(ETenure)
  @IsOptional()
  tenure?: ETenure;

  @IsEnum(EProjectStatus)
  @IsOptional()
  status?: EProjectStatus;

  @IsDate()
  @IsOptional()
  tenureEffectFrom?: Date;

  @IsUUID('all', { each: true })
  @IsOptional()
  medias?: string[];

  @IsUUID()
  @IsOptional()
  sitePlanImageId?: string;

  @IsUUID()
  @IsOptional()
  elevationChartId?: string;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  upcomingLaunch?: boolean;

  @IsString()
  @IsOptional()
  detail?: string;

  @IsUUID()
  @IsOptional()
  seoImageId?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  seoDescription?: string;

  @IsString()
  @IsOptional()
  @Length(0, 200)
  seoTitle?: string;

  @IsString({ each: true })
  @IsOptional()
  keywords?: string[];

  @IsString()
  @IsOptional()
  amenityHtml?: string;

  @IsString()
  @IsOptional()
  facilityHtml?: string;

  @IsNumber()
  @IsOptional()
  directCommission?: number;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  isCommissionUpTo?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  promotion?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  featured?: boolean;

  @IsOptional()
  manualTotalUnitsCount?: number;

  @IsOptional()
  manualAvailableUnitsCount?: number;

  @IsUUID()
  @IsOptional()
  mobileLogoId?: string;

  @IsUUID()
  @IsOptional()
  desktopLogoId?: string;

  @IsUUID('all', { each: true })
  @IsOptional()
  siteplanImageIds?: string[];

  @IsEnum(EProjectWhatIsNextStatus)
  @IsOptional()
  whatIsNextStatus?: EProjectWhatIsNextStatus;

  @IsUUID()
  @IsOptional()
  brochureAssetId?: string;

  @IsEnum(EProjectWhatIsNextStatus, { each: true })
  @IsOptional()
  whatIsNextStatusOptions?: EProjectWhatIsNextStatus[];
}
