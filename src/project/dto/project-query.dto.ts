import {
  Is<PERSON>rray,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { ETenure } from '../enums/tenure.enum';
import { EMarketSegment } from '../enums/market-segment.enum';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class ProjectQueryDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  search?: string;

  @IsOptional()
  @IsArray()
  filter: Filter[];

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsOptional()
  @IsUUID()
  userId?: string;
}

export class Filter {
  @IsOptional()
  @IsEnum(ETenure, { each: true })
  tenure: ETenure[];

  @IsOptional()
  @IsEnum(EMarketSegment, { each: true })
  marketSegment: EMarketSegment[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  category: string[];

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  upcomingLaunch?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  promotion?: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  featured?: boolean;
}
