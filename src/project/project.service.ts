import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Project } from './entities/project.entity';
import { In, IsNull, Not, Repository } from 'typeorm';
import { CreateProjectDto } from './dto/create-project.dto';
import { ProjectQueryDto } from './dto/project-query.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UpdateProjectDto } from './dto/update-project.dto';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { IProjectRaw } from './interfaces/raw-project.interface';
import { beautifyObject, diffArrays } from 'src/core/utils/common.util';
import { generateSlug } from 'src/core/utils/slug.util';
import { isUUID } from 'class-validator';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { FloorPlanService } from 'src/floor-plan/floor-plan.service';
import { SitePlanService } from 'src/site-plan/site-plan.service';
import { CategoryService } from 'src/category/category.service';
import { Asset } from 'src/asset/entities/asset.entity';
import { LocationService } from 'src/location/location.service';
import { ProjectAmenityService } from '../project-amenity/project-amenity.service';
import { VirtualTourService } from 'src/virtual-tour/virtual-tour.service';
import { RedisService } from 'src/redis/redis.service';
import { DeveloperService } from 'src/developer/developer.service';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { Roles, User } from 'src/users/entities/user.entity';
import { SectionProject } from 'src/section/entities/section-project.entity';
import { Section } from 'src/section/entities/section.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import puppeteer, { Browser } from 'puppeteer-core';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { UserProjectService } from 'src/user-project/user-project.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ProjectService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(Asset)
    private readonly assetRepo: Repository<Asset>,
    private readonly assetService: AssetService,
    private readonly floorPlanService: FloorPlanService,
    private readonly developerService: DeveloperService,
    private readonly sitePlanService: SitePlanService,
    private readonly categoryService: CategoryService,
    private readonly locationService: LocationService,
    private readonly projectAmenityService: ProjectAmenityService,
    private readonly virtualTourService: VirtualTourService,
    private readonly redisService: RedisService,
    private readonly userProjectService: UserProjectService,
    private readonly configService: ConfigService,
    @InjectRepository(FloorPlan)
    private readonly floorPlanRepo: Repository<FloorPlan>,

    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    @InjectRepository(UserProject)
    private readonly userProjectRepo: Repository<UserProject>,

    @InjectRepository(SectionProject)
    private readonly sectionProjectRepo: Repository<SectionProject>,

    @InjectRepository(Section)
    private readonly sectionRepo: Repository<Section>,

    @InjectRepository(Unit)
    private readonly unitRepo: Repository<Unit>,

    @InjectRepository(UnitTransaction)
    private readonly unitTransactionRepo: Repository<UnitTransaction>,

    @InjectQueue('project_queue') private projectQueue: Queue,
  ) {}

  async create(body: CreateProjectDto, createdByUserId?: string) {
    if (body.slug) {
      const existed = await this.projectRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug = body.slug ?? (await generateSlug(body.name, this.projectRepo));

    const data: Partial<Project> = {
      ...body,
      createdByUserId,
      slug,
      description: { [ELangCode.en]: body.description },
      keyPoint: { [ELangCode.en]: body.keyPoint },
      address: { [ELangCode.en]: body.address },
      name: { [ELangCode.en]: body.name },
      detail: { [ELangCode.en]: body.detail },
      amenityHtml: { [ELangCode.en]: body.amenityHtml },
      facilityHtml: { [ELangCode.en]: body.facilityHtml },
      manualAdded: true,
    };

    const project = await this.projectRepo.save(data);

    if (body.googleMapUrl) {
      await this.projectQueue.add('updateCoordinateByProjectId', {
        projectId: project.id,
      });
    }

    try {
      if (body.photoId) {
        // await this.assetService.addAssetRelation(
        //   body.photoId,
        //   EAssetRelation.Project,
        //   project.id,
        // );
        await this.assetRepo.update(
          { id: body.photoId },
          { project: { id: project.id }, relation: EAssetRelation.Project },
        );
      }
      if (body.mobileLogoId) {
        // await this.assetService.addAssetRelation(
        //   body.mobileLogoId,
        //   EAssetRelation.Project,
        //   project.id,
        // );
        await this.assetRepo.update(
          { id: body.mobileLogoId },
          {
            project: { id: project.id },
            relation: EAssetRelation.Project,
          },
        );
      }
      if (body.desktopLogoId) {
        // await this.assetService.addAssetRelation(
        //   body.desktopLogoId,
        //   EAssetRelation.Project,
        //   project.id,
        // );
        await this.assetRepo.update(
          { id: body.desktopLogoId },
          {
            project: { id: project.id },
            relation: EAssetRelation.Project,
          },
        );
      }
      if (body.siteplanImageIds?.length) {
        console.log('===>create siteplanImageIds');
        await Promise.all(
          body.siteplanImageIds.map((id) =>
            // this.assetService.attachAssetToProject(id, project.id),
            this.assetRepo.update(
              { id },
              {
                project: { id: project.id },
                relation: EAssetRelation.Project,
              },
            ),
          ),
        );
      }
      if (body.elevationChartId) {
        await this.assetService.addAssetRelation(
          body.elevationChartId,
          EAssetRelation.Project,
          project.id,
        );
      }
      if (body.seoImageId) {
        await this.assetService.addAssetRelation(
          body.seoImageId,
          EAssetRelation.Project,
          project.id,
        );
      }

      if (body.medias) {
        const medias = body.medias.filter(
          (id) =>
            id !== body.photoId &&
            id !== body.mobileLogoId &&
            id !== body.desktopLogoId &&
            id !== body.elevationChartId &&
            id !== body.seoImageId &&
            id !== body.brochureAssetId,
        );

        await Promise.all(
          medias.map((id) =>
            this.assetService.addAssetRelation(
              id,
              EAssetRelation.Project,
              project.id,
            ),
          ),
        );
      }
      if (body.brochureAssetId) {
        // await this.assetService.addAssetRelation(
        //   body.brochureAssetId,
        //   EAssetRelation.Project,
        //   project.id,
        // );
        await this.assetRepo.update(
          { id: body.brochureAssetId },
          { project: { id: project.id }, relation: EAssetRelation.Project },
        );
      }
    } catch (e) {
      console.log('update project assets failed cause', e);
    }

    await this.redisService.deletePattern('project@*');

    // Assign project to all agency users
    // const agencyUsers = await this.userRepo.find();

    // const assignUserProjects = agencyUsers.map(async (user) => {
    //   return await this.userProjectRepo.save({
    //     user: { id: user.id },
    //     project: { id: project.id },
    //     userId: user.id,
    //     projectId: project.id,
    //   });
    // });

    // await Promise.all(assignUserProjects);

    if (project.locationId) {
      const sections = await this.sectionRepo.find({
        where: { location: { id: project.locationId } },
      });

      await this.sectionProjectRepo.save(
        sections.map((section) => ({
          section: { id: section.id },
          project: project,
        })),
      );
    }

    await this.userProjectService.addProjectToAllUserProject(project.id);

    return project;
  }

  async update(id: string, body: UpdateProjectDto, userId?: string) {
    const langCode = body.lang ?? ELangCode.en;
    const project = await this.projectRepo.findOne({
      where: { id },
      relations: {
        photo: true,
        seoImage: true,
        elevationChart: true,
        mobileLogo: true,
        desktopLogo: true,
        siteplanImages: true,
        brochureAsset: true,
      },
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    if (userId && project.createdByUserId !== userId) {
      throw new ForbiddenException();
    }

    if (body.slug && body.slug !== project.slug) {
      const existed = await this.projectRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== project.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    const data: Partial<Project> = {
      ...body,
      name: {
        ...project.name,
        [langCode]: body.name ?? project.name?.[langCode],
      },
      description: {
        ...project.description,
        [langCode]: body.description ?? project.description?.[langCode],
      },
      keyPoint: {
        ...project.keyPoint,
        [langCode]: body.keyPoint ?? project.keyPoint?.[langCode],
      },
      address: {
        ...project.address,
        [langCode]: body.address ?? project.address?.[langCode],
      },
      detail: {
        ...project.detail,
        [langCode]: body.detail ?? project.detail?.[langCode],
      },
      facilityHtml: {
        ...project.facilityHtml,
        [langCode]: body.facilityHtml ?? project.facilityHtml?.[langCode],
      },
      amenityHtml: {
        ...project.amenityHtml,
        [langCode]: body.amenityHtml ?? project.amenityHtml?.[langCode],
      },
    };

    try {
      if (body.photoId !== undefined) {
        if (project.photoId) {
          await this.assetRepo.update(
            { id: project.photoId },
            { project: null },
          );
        }
        if (body.photoId) {
          await this.assetRepo.update(
            { id: body.photoId },
            { project: { id: project.id } },
          );
        }
        project.photoId = body.photoId;
        project.photo = null;
      }

      if (body.mobileLogoId !== undefined) {
        if (project.mobileLogoId) {
          await this.assetRepo.update(
            { id: project.mobileLogoId },
            { project: null },
          );
        }

        if (body.mobileLogoId) {
          await this.assetRepo.update(
            { id: body.mobileLogoId },
            { project: { id: project.id } },
          );
        }
        project.mobileLogoId = body.mobileLogoId;
        project.mobileLogo = null;
      }

      if (body.desktopLogoId !== undefined) {
        if (project.desktopLogoId) {
          await this.assetRepo.update(
            { id: project.desktopLogoId },
            { project: null },
          );
        }
        if (body.desktopLogoId) {
          await this.assetRepo.update(
            { id: body.desktopLogoId },
            { project: { id: project.id } },
          );
        }
        project.desktopLogoId = body.desktopLogoId;
        project.desktopLogo = null;
      }

      if (body.siteplanImageIds !== undefined) {
        if (project.siteplanImages?.length) {
          await Promise.all(
            project.siteplanImages.map((img) =>
              this.assetRepo.update({ id: img.id }, { project: null }),
            ),
          );
        }

        if (body.siteplanImageIds?.length) {
          await Promise.all(
            body.siteplanImageIds.map((id) =>
              this.assetRepo.update({ id }, { project: { id: project.id } }),
            ),
          );

          const updatedImages = await this.assetRepo.find({
            where: { id: In(body.siteplanImageIds) },
          });
          project.siteplanImages = updatedImages;
        } else {
          project.siteplanImages = [];
        }
      }

      if (
        data.elevationChartId !== undefined &&
        data.elevationChartId !== project.elevationChartId
      ) {
        if (project.elevationChartId) {
          await this.assetService.remove(project.elevationChartId);
        }
        if (data.elevationChartId) {
          await this.assetService.addAssetRelation(
            data.elevationChartId,
            EAssetRelation.Project,
            project.id,
          );
        }
      }

      if (
        data.seoImageId !== undefined &&
        data.seoImageId !== project.seoImageId
      ) {
        if (project.seoImageId) {
          await this.assetService.remove(project.seoImageId);
        }
        if (data.seoImageId) {
          await this.assetService.addAssetRelation(
            data.seoImageId,
            EAssetRelation.Project,
            project.id,
          );
        }
      }

      if (
        data.brochureAssetId !== undefined &&
        data.brochureAssetId !== project.brochureAssetId
      ) {
        if (project.brochureAssetId) {
          await this.assetRepo.update(
            { id: project.brochureAssetId },
            { project: null },
          );
        }
        if (body.brochureAssetId) {
          await this.assetRepo.update(
            { id: body.brochureAssetId },
            { project: { id: project.id } },
          );
        }

        project.brochureAssetId = body.brochureAssetId;
        project.brochureAsset = null;
      }

      const newUserProject = {};
      if (data.promotion !== undefined) {
        newUserProject['promotion'] = data.promotion;
      }
      if (data.featured !== undefined) {
        newUserProject['featured'] = data.featured;
      }
      if (data.upcomingLaunch !== undefined) {
        newUserProject['upcomingLaunch'] = data.upcomingLaunch;
      }

      const res = await this.projectRepo.save({
        ...project,
        ...data,
      });

      await this.userProjectRepo.update(
        {
          projectId: project.id,
        },
        newUserProject,
      );

      const excludeAssetIds = [
        body.photoId,
        body.mobileLogoId,
        body.desktopLogoId,
        ...(body.siteplanImageIds || []),
        body.elevationChartId,
        body.seoImageId,
        body.brochureAssetId,
        project.photoId,
        project.seoImageId,
        project.elevationChartId,
        project.mobileLogoId,
        project.desktopLogoId,
        project.brochureAssetId,
        ...(project.siteplanImages?.map((img) => img.id) || []),
      ].filter((id) => id !== undefined && id !== null);

      if (body.medias) {
        const medias = body.medias.filter(
          (id) => !excludeAssetIds.includes(id),
        );

        const oldAssets = (
          await this.assetService.getByRelatedId(project.id, excludeAssetIds)
        ).map((i) => i.id);

        const diffAssets = diffArrays(oldAssets, medias);
        await Promise.all(
          diffAssets.newItems.map((id) =>
            this.assetService.addAssetRelation(
              id,
              EAssetRelation.Project,
              project.id,
            ),
          ),
        );
        await Promise.all(
          diffAssets.deletedItems.map((id) =>
            this.assetService.remove(id).catch(console.error),
          ),
        );
      }

      if (body.googleMapUrl) {
        await this.projectQueue.add('updateCoordinateByProjectId', {
          projectId: project.id,
        });
      }

      await this.redisService.deletePattern('project@*');

      return res;
    } catch (e) {
      console.log('update project assets failed cause', e);
      throw e;
    }
  }

  async findById(id: string) {
    const item = await this.projectRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Project is not found');
    }

    return item;
  }

  async countUnitsStatistic(projectId: string): Promise<{
    totalUnitsCount: number;
    availableUnitsCount: number;
  }> {
    try {
      const result = await this.floorPlanRepo
        .createQueryBuilder('floorPlans')
        .select([
          'SUM(floorPlans.totalUnits) AS totalUnitsCount',
          'SUM(floorPlans.availableUnits) AS availableUnitsCount',
        ])
        .where('floorPlans.projectId = :projectId', { projectId })
        .getRawOne();

      return {
        totalUnitsCount: result?.totalunitscount || 0,
        availableUnitsCount: result?.availableunitscount || 0,
      };
    } catch (error) {
      console.error('Error fetching units count statistic:', error);
      return {
        totalUnitsCount: 0,
        availableUnitsCount: 0,
      };
    }
  }

  async detail(
    userId: string,
    slugOrId: string,
    lang: ELangCode = ELangCode.en,
  ) {
    const isId = isUUID(slugOrId);
    const queryBuilder = this._createQueryBuilder(userId, lang);

    if (isId) {
      queryBuilder.where('project.id = :id', { id: slugOrId });
    } else {
      queryBuilder.where('project.slug = :slug', { slug: slugOrId });
    }

    queryBuilder
      .addSelect('project.manualTotalUnitsCount', 'manualTotalUnitsCount')
      .addSelect(
        'project.manualAvailableUnitsCount',
        'manualAvailableUnitsCount',
      )
      .addSelect('project.seoTitle', 'seoTitle')
      .addSelect('project.keywords', 'keywords')
      .addSelect('project.googleMapUrl', 'googleMapUrl')
      .addSelect('project.whatIsNextStatus', 'whatIsNextStatus')
      .addSelect(
        createMultilingualSelect('project', 'facilityHtml', lang),
        'facilityHtml',
      )
      .addSelect(
        createMultilingualSelect('project', 'amenityHtml', lang),
        'amenityHtml',
      )
      .addSelect('project.showflatLocation', 'showflatLocation')
      .addSelect('project.isShowflatLocation', 'isShowflatLocation')
      .leftJoin('project.seoImage', 'seoImage')
      .leftJoin('project.elevationChart', 'elevationChart')
      .leftJoin('project.sitePlanImage', 'sitePlanImage')
      .leftJoin('project.mobileLogo', 'mobileLogo')
      .leftJoin('project.desktopLogo', 'desktopLogo')
      .leftJoin('project.photo', 'photo')
      .leftJoin('project.siteplanImages', 'siteplanImages')
      .leftJoin('project.brochureAsset', 'brochureAsset')
      .addSelect(['mobileLogo.id', 'mobileLogo.urls', 'mobileLogo.type'])
      .addSelect(['desktopLogo.id', 'desktopLogo.urls', 'desktopLogo.type'])
      .addSelect(['photo.id', 'photo.urls', 'photo.type'])
      .addSelect([
        'siteplanImages.id',
        'siteplanImages.urls',
        'siteplanImages.type',
      ])
      .addSelect(['seoImage.id', 'seoImage.urls', 'seoImage.type'])
      .addSelect([
        'elevationChart.id',
        'elevationChart.urls',
        'elevationChart.type',
      ])
      .addSelect([
        'sitePlanImage.id',
        'sitePlanImage.urls',
        'sitePlanImage.type',
      ])
      .addSelect([
        'brochureAsset.id',
        'brochureAsset.urls',
        'brochureAsset.type',
      ])
      .addGroupBy('photo.id')
      .addGroupBy('elevationChart.id')
      .addGroupBy('sitePlanImage.id')
      .addGroupBy('seoImage.id')
      .addGroupBy('mobileLogo.id')
      .addGroupBy('desktopLogo.id')
      .addGroupBy('siteplanImages.id')
      .addGroupBy('brochureAsset.id');

    const item = await queryBuilder.getRawOne();

    if (!item) {
      throw new NotFoundException('Project is not found');
    }

    const result = this._beautifyProject(item);

    const excludeAssetIds = [
      // item.photoId,
      item.seoImageId,
      item.sitePlanImageId,
      item.elevationChartId,
    ].filter(Boolean);

    const medias = await this.assetService.getByRelatedId(
      item.id,
      excludeAssetIds,
    );

    const countUnitsStatistic = await this.countUnitsStatistic(result.id);

    result.medias = medias;
    result.totalUnitsCount = countUnitsStatistic.totalUnitsCount;
    result.availableUnitsCount = countUnitsStatistic.availableUnitsCount;

    const excludeSitePlanImagesAssetIds = [
      item.photo_id,
      item.desktopLogo_id,
      item.mobileLogo_id,
      item.brochureAsset_id,
      item.elevationChart_id,
    ].filter((item) => item);

    const siteplanImages = await this.assetRepo.find({
      where: {
        project: { id: result.id },
        id: Not(In(excludeSitePlanImagesAssetIds)),
      },
      select: ['id', 'type', 'urls'],
    });

    result.siteplanImages = siteplanImages;

    return result;
  }

  async listingSelection(query: ProjectQueryDto) {
    if (query && query.userId) {
      const userProject = await this.userProjectRepo.findOne({
        where: { user: { id: query.userId } },
        relations: ['project'],
      });
      return await this.projectRepo
        .createQueryBuilder('project')
        .select('project.id', 'id')
        .addSelect('project.slug', 'slug')
        .addSelect('project.whatIsNextStatus', 'whatIsNextStatus')
        .addSelect(
          createMultilingualSelect('project', 'name', ELangCode.en),
          'name',
        )
        .where('project.id = :projectId', {
          projectId: userProject?.project?.id,
        })
        .getRawMany();
    } else {
      return await this.projectRepo
        .createQueryBuilder('project')
        .select('project.id', 'id')
        .addSelect('project.slug', 'slug')
        .addSelect('project.whatIsNextStatus', 'whatIsNextStatus')
        .addSelect(
          createMultilingualSelect('project', 'name', ELangCode.en),
          'name',
        )
        .getRawMany();
    }
  }

  async listing(userId: string, query: ProjectQueryDto) {
    const pagination = getPaginationOption(query);
    const lang = query.lang ?? ELangCode.en;

    const queryBuilder = this._createQueryBuilder(userId, lang)
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        `regexp_replace(${createMultilingualSelect('project', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :name`,
        {
          name: `%${query.search.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%`,
        },
      );
    }

    if (query.filter) {
      query.filter.forEach((filter) => {
        if (filter.tenure) {
          queryBuilder.andWhere('project.tenure IN (:...tenure)', {
            tenure: filter.tenure,
          });
        }
        if (filter.marketSegment) {
          queryBuilder.andWhere(
            'project.marketSegment IN (:...marketSegment)',
            { marketSegment: filter.marketSegment },
          );
        }
        if (filter.category) {
          queryBuilder.andWhere('project.category.id IN (:...category)', {
            category: filter.category,
          });
        }
        if (filter.upcomingLaunch) {
          queryBuilder.andWhere('project.upcomingLaunch = :upcomingLaunch', {
            upcomingLaunch: filter.upcomingLaunch,
          });
        }
        if (filter.promotion) {
          queryBuilder.andWhere('project.promotion = :promotion', {
            promotion: filter.promotion,
          });
        }
        if (filter.featured) {
          queryBuilder.andWhere('project.featured = :featured', {
            featured: filter.featured,
          });
        }
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        if (key === 'directCommission' && order === 'DESC') {
          queryBuilder.addOrderBy(`project.${key}`, 'DESC', 'NULLS LAST');
        } else {
          queryBuilder.addOrderBy(`project.${key}`, order);
        }
      });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    const beautifiedData = data.map(this._beautifyProject);

    const result = await Promise.all(
      beautifiedData.map(async (project) => {
        const excludeAssetIds = [
          project.photoId,
          project.seoImageId,
          project.sitePlanImageId,
          project.elevationChartId,
        ].filter(Boolean);

        const medias = await this.assetService.getByRelatedId(
          project.id,
          excludeAssetIds,
        );

        const siteplanImages = await this.assetRepo.find({
          where: {
            project: { id: project.id },
          },
          select: ['id', 'type', 'urls'],
        });

        return {
          ...project,
          medias,
          siteplanImages: siteplanImages?.length ? siteplanImages : [],
        };
      }),
    );

    return createPaginationResponse(result, total, pagination);
  }

  _beautifyProject(raw: IProjectRaw) {
    const obj = beautifyObject(raw, [
      'photo',
      'developer',
      'category',
      'location',
      'seoImage',
      'mobileLogo',
      'desktopLogo',
      'siteplanImages',
      'brochureAsset',
    ]);
    if (obj['floorPlanCount']) {
      obj['floorPlanCount'] = +obj['floorPlanCount'] || 0;
    }
    return obj;
  }

  _createQueryBuilder(userId: string, lang: ELangCode) {
    return this.projectRepo
      .createQueryBuilder('project')
      .leftJoin('project.developer', 'developer')
      .leftJoin('project.category', 'category')
      .leftJoin('project.location', 'location')
      .leftJoin('project.floorPlans', 'floorPlans')
      .leftJoin(
        'project.userProjects',
        'userProjects',
        'userProjects.projectId = project.id AND userProjects.userId = :userId',
        { userId },
      )
      .select('project.id', 'id')
      .addSelect('project.slug', 'slug')
      .addSelect('project.tenure', 'tenure')
      .addSelect('project.tenureEffectFrom', 'tenureEffectFrom')
      .addSelect('project.coordinates', 'coordinates')
      .addSelect('project.area', 'area')
      .addSelect('project.upcomingLaunch', 'upcomingLaunch')
      .addSelect('project.promotion', 'promotion')
      .addSelect('project.featured', 'featured')
      .addSelect('project.marketSegment', 'marketSegment')
      .addSelect('project.expectedTop', 'expectedTop')
      .addSelect('project.whatIsNextStatus', 'whatIsNextStatus')
      .addSelect('project.whatIsNextStatusOptions', 'whatIsNextStatusOptions')
      .addSelect('COUNT(floorPlans.id)', 'floorPlanCount')
      .addSelect(['category.id', 'category.slug'])
      .addSelect(['location.id', 'location.slug'])
      .addSelect(['developer.id'])
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .addSelect(
        createMultilingualSelect('project', 'description', lang),
        'description',
      )
      .addSelect(
        createMultilingualSelect('project', 'address', lang),
        'address',
      )

      .addSelect(
        createMultilingualSelect('category', 'name', lang),
        'category_name',
      )
      .addSelect(
        createMultilingualSelect('category', 'shortname', lang),
        'category_shortname',
      )
      .addSelect(
        createMultilingualSelect('location', 'name', lang),
        'location_name',
      )
      .addSelect(
        createMultilingualSelect('developer', 'name', lang),
        'developer_name',
      )
      .addSelect(
        `CASE 
        WHEN userProjects.id IS NOT NULL THEN
          jsonb_build_object(
            'id', "userProjects".id, 
            'weight', "userProjects".weight, 
            'featured',  "userProjects".featured, 
            'promotion',  "userProjects".promotion
          )
        ELSE
          NULL
        END`,
        'userProject',
      )
      .addSelect('project.directCommission', 'directCommission')
      .addSelect('project.isCommissionUpTo', 'isCommissionUpTo')
      .addGroupBy('project.id')
      .addGroupBy('category.id')
      .addGroupBy('location.id')
      .addGroupBy('userProjects.id')
      .addGroupBy('developer.id');
  }

  async delete(id: string, userId?: string) {
    const item = await this.projectRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Project is not found');
    }
    if (userId && item.createdByUserId !== userId) {
      throw new ForbiddenException();
    }

    await this.assetService.removeAllByRelatedId(item.id).catch((err) => {
      // this.logger.error(err);
    });

    await this.redisService.deletePattern('project@*');

    return await this.projectRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  formatResponse(item: Project, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      description: getLangValue(lang, item.description, fallback),
      address: getLangValue(lang, item.address, fallback),
      amenityHtml: getLangValue(lang, item.amenityHtml, fallback),
      facilityHtml: getLangValue(lang, item.facilityHtml, fallback),
      detail: getLangValue(lang, item.detail, fallback),
      keyPoint: getLangValue(lang, item.keyPoint, fallback),
      floorPlanCount: item['floorPlanCount']
        ? +item['floorPlanCount'] || 0
        : item['floorPlanCount'],
      floorPlans: item.floorPlans?.map((fp) =>
        this.floorPlanService.formatResponse(fp, lang, fallback),
      ),
      sitePlans: item.sitePlans?.map((plan) =>
        this.sitePlanService.formatResponse(plan, lang, fallback),
      ),
      category: item.category
        ? this.categoryService.formatResponse(item.category, lang, fallback)
        : item.category,
      developer: item.developer
        ? this.developerService.formatResponse(item.developer, lang, fallback)
        : item.developer,
      location: item.location
        ? this.locationService.formatResponse(item.location, lang, fallback)
        : item.location,
      projectAmenities: item.projectAmenities?.map((amenity) =>
        this.projectAmenityService.formatResponse(amenity, lang, fallback),
      ),
      virtualTours: item.virtualTours?.map((plan) =>
        this.virtualTourService.formatResponse(plan, lang, fallback),
      ),
    };
  }

  async findByName(name: string) {
    return await this.redisService.cacheWrapper(
      `project@name:${name}`,
      async () => {
        return await this.projectRepo
          .createQueryBuilder('project')
          .where(
            `${createMultilingualSelect('project', 'name', ELangCode.en)} ILIKE :name`,
            { name: `%${name}%` },
          )
          .getOne();
      },
    );
  }

  async getProjectIdListByLocationId(locationId: string) {
    const ids = await this.projectRepo
      .createQueryBuilder('project')
      .select('project.id')
      .where('project.locationId = :locationId', { locationId })
      .limit(10)
      .getMany();
    return ids.map((id, idx) => ({
      projectId: id.id,
      weight: idx + 1,
    }));
  }

  async getDefaultProjectsByLocationId(locationId: string) {
    return await this.projectRepo
      .createQueryBuilder('project')
      .where('project.locationId = :locationId', { locationId })
      .andWhere(
        'project.manualAdded IS NULL OR project.manualAdded != :manualAdded',
        { manualAdded: true },
      )
      .getMany();
  }

  async updateCoordinates() {
    const projects = await this.projectRepo.find({
      where: {
        googleMapUrl: Not(IsNull()),
        deletedAt: IsNull(),
        coordinates: IsNull(),
      },
    });

    await this.projectQueue.add('updateCoordinates', projects);
    return null;
  }

  async getCoordinates(googleMapUrl: string) {
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: this.configService.get<string>('CHROME_PATH'),
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const result = await this.getCoordinatesInIFrame(googleMapUrl, browser);
      return result;
    } catch (error) {
      throw new BadRequestException(error);
    } finally {
      await browser.close();
    }
  }

  async getCoordinatesInIFrame(
    src: string,
    browser: Browser,
  ): Promise<{ latitude: number; longitude: number } | null> {
    const page = await browser.newPage();
    try {
      const htmlContent = `
        <html>
          <body>
          </div>
            <iframe
              src=${src}
              width="600"
              height="450"
              style="border:0;"
              allowfullscreen=""
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
            ></iframe>
          </body>
        </html>
      `;

      await page.setContent(htmlContent);
      /**
       * 10s for pending get data
       */
      await new Promise((resolve) => setTimeout(resolve, 3000));

      const iframeHandle = await page.$('iframe');
      const iframe = await iframeHandle.contentFrame();
      await iframe.waitForSelector('.google-maps-link');

      const href = await iframe.$eval('.google-maps-link', (el) => {
        const aTag = el.querySelector('a');
        return aTag ? aTag.getAttribute('href') : el.getAttribute('href');
      });

      const result = this.getCoordinatesFromURL(href);
      return result;
    } catch (error) {
      throw new BadRequestException(error);
    } finally {
      await page.close();
    }
  }

  getCoordinatesFromURL(
    href: string,
  ): { latitude: number; longitude: number } | null {
    const parsedUrl = new URL(href);
    const llParam = parsedUrl.searchParams.get('ll');

    if (llParam) {
      const [latitude, longitude] = llParam.split(',').map(Number);
      return { latitude, longitude };
    } else {
      console.log('No ll parameter found in URL');
      return null;
    }
  }

  async testPuppeteer() {
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: this.configService.get<string>('CHROME_PATH'),
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    try {
      const page = await browser.newPage();
      await page.goto('https://example.com');
      const title = await page.title();
      return title;
    } catch (error) {
      throw new BadRequestException(error);
    } finally {
      await browser.close();
    }
  }
}
