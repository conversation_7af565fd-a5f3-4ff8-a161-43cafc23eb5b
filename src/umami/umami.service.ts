import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { TUmamiAuthenticateResp } from './types/authenticate.response.interface';
import { CreateWebsiteByUserDto } from './dtos/websites.dto';
import { IUmamiCreatedWebsitesResponse } from './types/websites-response.interface';

@Injectable()
export class UmamiService {
  private token: string;
  private umamiUser: string;
  private umamiPassword: string;
  umamiHost: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.umamiUser = this.configService.get<string>('UMAMI_USER');
    this.umamiPassword = this.configService.get<string>('UMAMI_PASSWORD');
    this.umamiHost = this.configService.get<string>('UMAMI_HOST');
  }

  async onModuleInit() {
    try {
      const authResponse = await this.request<TUmamiAuthenticateResp>(
        'post',
        `${this.umamiHost}/api/auth/login`,
        {
          payload: {
            username: this.umamiUser,
            password: this.umamiPassword,
          },
        },
      );
      this.token = authResponse.token;
    } catch (error) {
      console.error('Failed to authenticate Umami service:', error);
      this.token = '';
    }
  }

  async createWebsiteByUser(
    body: CreateWebsiteByUserDto,
  ): Promise<IUmamiCreatedWebsitesResponse> {
    try {
      const authResponse = await this.request<TUmamiAuthenticateResp>(
        'post',
        `${this.umamiHost}/api/auth/login`,
        {
          payload: {
            username: body.usernname,
            password: body.password,
          },
        },
      );

      const { data } = await firstValueFrom(
        this.httpService.request<IUmamiCreatedWebsitesResponse>({
          method: 'post',
          url: `${this.umamiHost}/api/websites`,
          headers: {
            Authorization: `Bearer ${authResponse.token}`,
          },
          data: {
            domain: body.domain,
            name: body.name,
          },
        }),
      );

      return data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async request<T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    options: {
      payload?: any;
      params?: Record<string, any>;
      headers?: Record<string, string>;
    } = {},
  ): Promise<T> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.request<T>({
          method,
          url,
          headers: {
            Authorization: `Bearer ${this.token}`,
            ...options.headers,
          },
          data: options.payload || undefined,
          params: options.params || undefined,
        }),
      );
      return data;
    } catch (error) {
      console.error(
        'Request error:',
        (error as AxiosError).response?.data || error.message,
      );
      throw new Error('An error occurred while processing the request.');
    }
  }
}
