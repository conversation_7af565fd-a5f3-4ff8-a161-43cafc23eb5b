import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { UmamiService } from './umami.service';
import { UmamiAuthenticateDto } from './dtos/authenticate.dto';
import { TUmamiAuthenticateResp } from './types/authenticate.response.interface';
import { IAuthVerifyResponse } from './types/auth-verify.response.interface';
import { CreateUmamiUsers, UpdateUmamiUsers } from './dtos/users.dto';
import {
  IUmamiCreateUsersResponse,
  IUmamiGetTeamsByUserIdResponse,
  IUmamiGetUserByIdResponse,
  IUmamiGetWebsitesByUserIdResponse,
  IUmamiUpdateUserByIdResponse,
  IUmammiGetUsersResponse,
} from './types/users-response.interface';
import {
  AddUserToTeam,
  CreateTeamDto,
  GetWebsitesByTeamIdQuery,
  IUmamiWebsiteResponse,
  JoinTeamDto,
  UpdateTeamDto,
  UpdateUserRoleInTeam,
} from './dtos/teams.dto';
import {
  IUmamiAddUserToTeam,
  IUmamiGetTeamById,
  IUmamiGetTeamMembersResponse,
  IUmamiGetUserInTeam,
  IUmamiTeamResponse,
  IUmamiUpdateTeamById,
  TUmamiCreateTeamsResponse,
} from './types/teams-response.interface';
import { ConfigService } from '@nestjs/config';

@Controller('umami')
export class UmamiController {
  private umamiHost: string;

  constructor(
    private readonly umamiService: UmamiService,
    private readonly configService: ConfigService,
  ) {
    this.umamiHost = this.configService.get<string>('UMAMI_HOST');
  }

  @Post('authenticate')
  async authenticate(
    @Body() body: UmamiAuthenticateDto,
  ): Promise<TUmamiAuthenticateResp> {
    return this.umamiService.request<TUmamiAuthenticateResp>(
      'post',
      `${this.umamiHost}/api/auth/login`,
      {
        payload: body,
      },
    );
  }

  @Get('auth-verify')
  async authVerify(): Promise<IAuthVerifyResponse> {
    return this.umamiService.request<IAuthVerifyResponse>(
      'post',
      `${this.umamiHost}/api/auth/verify`,
    );
  }

  @Post('users')
  async createUsers(
    @Body() body: CreateUmamiUsers,
  ): Promise<IUmamiCreateUsersResponse> {
    return this.umamiService.request<IUmamiCreateUsersResponse>(
      'post',
      `${this.umamiHost}/api/users`,
      {
        payload: body,
      },
    );
  }

  @Get('users')
  async getUsers(): Promise<IUmammiGetUsersResponse> {
    return this.umamiService.request<IUmammiGetUsersResponse>(
      'get',
      `${this.umamiHost}/api/admin/users`,
    );
  }

  @Get('users/:id')
  async getUserById(
    @Param('id') id: string,
  ): Promise<IUmamiGetUserByIdResponse> {
    return this.umamiService.request<IUmamiGetUserByIdResponse>(
      'get',
      `${this.umamiHost}/api/users/${id}`,
    );
  }

  @Post('users/:id')
  async updateUserById(
    @Param('id') id: string,
    @Body() body: UpdateUmamiUsers,
  ): Promise<IUmamiUpdateUserByIdResponse> {
    return this.umamiService.request<IUmamiUpdateUserByIdResponse>(
      'post',
      `${this.umamiHost}/api/users/${id}`,
      {
        payload: body,
      },
    );
  }

  @Delete('users/:id')
  async deleteUserById(@Param('id') id: string) {
    return this.umamiService.request(
      'delete',
      `${this.umamiHost}/api/users/${id}`,
    );
  }

  @Get('users/:id/websites')
  async getWebsitesByUserId(
    @Param('id') id: string,
    @Query() query: any,
  ): Promise<IUmamiGetWebsitesByUserIdResponse[]> {
    return this.umamiService.request<IUmamiGetWebsitesByUserIdResponse[]>(
      'get',
      `${this.umamiHost}/api/users/${id}/websites`,
      { params: query },
    );
  }

  @Get('users/:id/teams')
  async getTeamsByUserId(
    @Param('id') id: string,
    @Query() query: any,
  ): Promise<IUmamiGetTeamsByUserIdResponse[]> {
    return this.umamiService.request<IUmamiGetTeamsByUserIdResponse[]>(
      'get',
      `${this.umamiHost}/api/users/${id}/teams`,
      {
        params: query,
      },
    );
  }

  @Post('teams')
  async createTeams(
    @Body() body: CreateTeamDto,
  ): Promise<TUmamiCreateTeamsResponse[]> {
    return this.umamiService.request<TUmamiCreateTeamsResponse[]>(
      'post',
      `${this.umamiHost}/api/teams`,
      {
        payload: body,
      },
    );
  }

  @Get('teams')
  async getTeams(): Promise<IUmamiTeamResponse[]> {
    return this.umamiService.request<IUmamiTeamResponse[]>(
      'get',
      `${this.umamiHost}/api/teams`,
    );
  }

  @Post('teams/join')
  async joinTeam(@Body() body: JoinTeamDto): Promise<IUmamiTeamResponse[]> {
    return this.umamiService.request<IUmamiTeamResponse[]>(
      'post',
      `${this.umamiHost}/api/teams/join`,
      {
        payload: body,
      },
    );
  }

  @Get('teams/:id')
  async getTeamById(@Param('id') id: string): Promise<IUmamiGetTeamById> {
    return this.umamiService.request<IUmamiGetTeamById>(
      'get',
      `${this.umamiHost}/api/teams/${id}`,
    );
  }

  @Post('teams/:id')
  async updateTeamById(
    @Param('id') id: string,
    @Body() body: UpdateTeamDto,
  ): Promise<IUmamiUpdateTeamById> {
    return this.umamiService.request<IUmamiUpdateTeamById>(
      'post',
      `${this.umamiHost}/api/teams/${id}`,
      {
        payload: body,
      },
    );
  }

  @Delete('teams/:id')
  async deleteTeamById(@Param('id') id: string) {
    return this.umamiService.request(
      'delete',
      `${this.umamiHost}/api/teams/${id}`,
    );
  }

  @Get('teams/:id/users')
  async getTeamUsers(
    @Param('id') id: string,
  ): Promise<IUmamiGetTeamMembersResponse> {
    return this.umamiService.request<IUmamiGetTeamMembersResponse>(
      'get',
      `${this.umamiHost}/api/teams/${id}/users`,
    );
  }

  @Post('teams/:id/users')
  async addUserToTeam(
    @Param('id') id: string,
    @Body() body: AddUserToTeam,
  ): Promise<IUmamiAddUserToTeam> {
    return this.umamiService.request<IUmamiAddUserToTeam>(
      'post',
      `${this.umamiHost}/api/teams/${id}/users`,
      {
        payload: body,
      },
    );
  }

  @Get('teams/:teamId/users/:userId')
  async getUserInTeam(
    @Param('teamId') teamId: string,
    @Param('userId') userId: string,
  ): Promise<IUmamiGetUserInTeam> {
    return this.umamiService.request<IUmamiGetUserInTeam>(
      'get',
      `${this.umamiHost}/api/teams/${teamId}/users/${userId}`,
    );
  }

  @Post('teams/:teamId/users/:userId')
  async updateUserRoleInTeam(
    @Param('teamId') teamId: string,
    @Param('userId') userId: string,
    @Body() body: UpdateUserRoleInTeam,
  ): Promise<'ok'> {
    return this.umamiService.request<'ok'>(
      'post',
      `${this.umamiHost}/api/teams/${teamId}/users/${userId}`,
      { payload: body },
    );
  }

  @Delete('teams/:teamId/users/:userId')
  async deleteUserInTeam(
    @Param('teamId') teamId: string,
    @Param('userId') userId: string,
  ): Promise<'ok'> {
    return this.umamiService.request<'ok'>(
      'delete',
      `${this.umamiHost}/api/teams/${teamId}/users/${userId}`,
    );
  }

  @Get('teams/:teamId/websites')
  async getWebsitesInTeam(
    @Param('teamId') teamId: string,
    @Query() query: GetWebsitesByTeamIdQuery,
  ): Promise<IUmamiWebsiteResponse> {
    return this.umamiService.request<IUmamiWebsiteResponse>(
      'get',
      `${this.umamiHost}/api/teams/${teamId}/websites`,
      {
        params: query,
      },
    );
  }
}
