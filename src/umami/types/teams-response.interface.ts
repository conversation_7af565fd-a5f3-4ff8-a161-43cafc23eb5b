export type TUmamiCreateTeamsResponse =
  | {
      accessCode: string;
      createdAt: string;
      id: string;
      name: string;
      updatedAt: string | null;
    }
  | {
      createdAt: string;
      id: string;
      role: string;
      teamId: string;
      updatedAt: string | null;
      userId: string;
    };

export interface IUmamiGetTeamsResponse {}

export interface IUmamiUser {
  id: string;
  username: string;
}

export interface IUmamiTeamUser {
  createdAt: string;
  id: string;
  role: string;
  teamId: string;
  updatedAt: string | null;
  user: IUmamiUser;
  userId: string;
}

export interface IUmamiTeam {
  accessCode: string;
  createdAt: string;
  id: string;
  name: string;
  teamUser: IUmamiTeamUser[];
}

export interface IUmamiTeamResponse {
  teams: IUmamiTeam[];
}

export interface IUmamiGetTeamById {
  id: string;
  name: string;
  accessCode: string;
  createdAt: string;
  updatedAt: string;
}

export interface IUmamiUpdateTeamById {
  accessCode: string;
  createdAt: string;
  id: string;
  name: string;
  updatedAt: string;
}

export interface IUmamiTeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string | null;
  user: IUmamiUser;
}

export type IUmamiGetTeamMembersResponse = IUmamiTeamMember[];

export interface IUmamiAddUserToTeam {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface IUmamiGetUserInTeam {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}
