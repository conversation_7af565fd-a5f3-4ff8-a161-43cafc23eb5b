export interface IUmamiCreateUsersResponse {
  id: string;
  username: string;
  role: string;
  createdAt: string;
}

export interface IUmammiGetUsersResponse {
  id: string;
  username: string;
  role: string;
  createdAt: string;
}

export interface IUmamiGetUserByIdResponse {
  id: string;
  username: string;
  role: string;
}

export interface IUmamiUpdateUserByIdResponse {
  id: string;
  username: string;
  role: string;
  createdAt: string;
}

export interface IUmamiGetWebsitesByUserIdResponse {
  id: string;
  userId: string;
  domain: string;
  name: string;
  shareId: string;
  createdAt: string;
  deletedAt: string;
  resetAt: string;
  updatedAt: string;
}

export interface IUmamiGetTeamsByUserIdResponse {
  id: string;
  name: string;
  createdAt: string;
  deletedAt: string;
  updatedAt: string;
}
