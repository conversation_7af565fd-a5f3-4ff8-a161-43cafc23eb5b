import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateTeamDto {
  @IsString()
  name: string;
}

export class JoinTeamDto {
  @IsString()
  accessCode: string;
}

export class UpdateTeamDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  accessCode?: string;
}

export class AddUserToTeam {
  @IsString()
  userId: string;

  @IsString()
  role: 'mamber' | 'view-only';
}

export class UpdateUserRoleInTeam {
  @IsString()
  role: 'mamber' | 'view-only';
}

export class GetWebsitesByTeamIdQuery {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @IsString()
  pageSize?: string = '10';

  @IsOptional()
  @IsString()
  orderBy?: string = 'name';
}

export interface IUmamiTeamUser {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string | null;
}

export interface IUmamiTeam {
  id: string;
  name: string;
  accessCode: string;
  createdAt: string;
  updatedAt: string;
  teamUser: IUmamiTeamUser[];
}

export interface IUmamiTeamWebsite {
  id: string;
  teamId: string;
  websiteId: string;
  createdAt: string;
  team: IUmamiTeam;
}

export interface User {
  id: string;
  username: string;
}

export interface IUmamiWebsite {
  id: string;
  name: string;
  domain: string;
  shareId: string;
  resetAt: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  teamWebsite: IUmamiTeamWebsite[];
  user: User;
}

export type IUmamiWebsiteResponse = IUmamiWebsite[];
