import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateUmamiUsers {
  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsString()
  role: 'admin' | 'user';
}

export class UpdateUmamiUsers {
  @IsOptional()
  @IsString()
  username?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsString()
  role?: 'admin' | 'user';
}

export class GetWebsitesByUserIdQuery {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @IsString()
  pageSize?: string = '10';

  @IsOptional()
  @IsString()
  orderBy?: string = 'name';
}

export class GetTeamsByUserIdQuery {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @IsString()
  pageSize?: string = '10';

  @IsOptional()
  @IsString()
  orderBy?: string = 'name';
}
