import { Modu<PERSON> } from '@nestjs/common';
import { UmamiController } from './umami.controller';
import { UmamiService } from './umami.service';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UmamiMapping } from './entities/umami-mapping.entity';
import { UmamiWebsites } from './entities/umami-websites.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([UmamiMapping, UmamiWebsites]),
  ],
  controllers: [UmamiController],
  providers: [UmamiService],
  exports: [UmamiService],
})
export class UmamiModule {}
