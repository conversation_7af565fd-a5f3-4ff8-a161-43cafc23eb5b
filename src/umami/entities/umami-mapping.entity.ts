import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, OneToOne } from 'typeorm';

@Entity('umami_mapping')
export class UmamiMapping extends CrudEntity {
  @Column({ nullable: true })
  userId?: string;

  @OneToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({ nullable: true })
  umamiUserId?: string;

  @Column({ nullable: true })
  umamiUserName?: string;

  @Column({ nullable: true })
  umamiPassword?: string;
}
