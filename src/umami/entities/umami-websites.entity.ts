import CrudEntity from 'src/core/entities/crud.entity';
import { Domain } from 'src/domains/entities/domain.entity';
import { UserProjectLandingPage } from 'src/user-project-landing-page/entities/user-project-landing-page.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity('umami_websites')
export class UmamiWebsites extends CrudEntity {
  @Column({ nullable: true })
  userId?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({ nullable: true })
  domainId?: string;

  @OneToOne(() => Domain)
  @JoinColumn({ name: 'domainId' })
  domain?: Domain;

  @Column({ nullable: true })
  userProjectLandingPageId?: string;

  @OneToOne(() => UserProjectLandingPage)
  @JoinColumn({ name: 'userProjectLandingPageId' })
  userProjectLandingPage?: UserProjectLandingPage;

  @Column({ nullable: true })
  umamiWebsiteUuid?: string;

  @Column({ nullable: true })
  umamiWebsiteId?: string;

  @Column({ nullable: true })
  umamiWebsiteName?: string;

  @Column({ nullable: true })
  umamiWebsiteDomain?: string;

  @Column({ nullable: true })
  umamiWebsiteShareId?: string;

  @Column({ nullable: true })
  umamiWebsiteTrackingCode?: string;
}
