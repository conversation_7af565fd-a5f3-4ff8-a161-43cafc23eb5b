import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { XeroProvider } from './xero-payment.provider';
import { MailerService } from 'src/mailer/mailer.service';
import { UsersService } from 'src/users/users.service';
import { EMAIL_TEMPLATE } from 'src/mailer/enums/mailer.enum';
import { XeroPaymentService } from './xero-payment.service';
import * as moment from 'moment-timezone';
import { ConfigService } from '@nestjs/config';
import { UserConfigService } from 'src/user-config/user-config.service';

@Processor('xero_payment_queue')
export class XeroPaymentQueue {
  constructor(
    @InjectQueue('xero_payment_queue')
    private readonly xeroPaymentQueue: Queue,

    private readonly xeroProvider: XeroProvider,
    private readonly mailerService: MailerService,
    private readonly usersService: UsersService,
    private readonly xeroPaymentService: XeroPaymentService,
    private readonly configService: ConfigService,
    private readonly userConfigService: UserConfigService,
  ) {}

  @Process('handleTokenRefresh')
  async handleTokenRefresh(job: Job) {
    console.log(`Processing refresh-token job: ${JSON.stringify(job.data)}`);

    try {
      const { refreshToken } = job.data;
      const newTokenSet =
        await this.xeroProvider.refreshAccessToken(refreshToken);
      await this.xeroProvider.saveTokens(newTokenSet);

      console.log('Token refreshed and saved successfully.');
    } catch (error) {
      console.error('Error processing refresh-token job:', error.message);
    }
  }

  @Process('sendMail1st')
  async sendMail1st(job: Job) {
    console.log(`Processing sendMail1st job: ${JSON.stringify(job.data)}`);

    try {
      const { userId, xeroPaymentId } = job.data;

      const paymentData = await this.xeroPaymentService.findOne(
        userId,
        xeroPaymentId,
      );
      const { user, xeroPaymentHistories } = paymentData;
      const userConfig = await this.userConfigService.findOneByUser(userId);
      await this.mailerService.sendMail(
        {
          to: user.email,
          subject: 'Payment Pending (1st)',
          html: EMAIL_TEMPLATE.PAYMENT_PENDING_1ST,
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: `${user.firstName} ${user.lastName}`,
          amount: xeroPaymentHistories?.at(-1).discountPrice,
          dueDate: moment(paymentData.createdAt)
            .add(5, 'days')
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
          paymentLink: this.configService.get('ADMIN_LINK'),
        },
        'mjml',
      );

      await this.xeroPaymentQueue.add(
        'sendMail2nd',
        { userId, xeroPaymentId },
        {
          //delay 2 days
          delay: 2 * 24 * 60 * 60 * 1000,
          jobId: `sendMail2nd_${user.id}`,
        },
      );

      console.log('Token refreshed and saved successfully.');
    } catch (error) {
      console.error('Error processing sendMail1st job:', error.message);
    }
  }

  @Process('sendMail2nd')
  async sendMail2nd(job: Job) {
    console.log(`Processing sendMail2nd job: ${JSON.stringify(job.data)}`);

    try {
      const { userId, xeroPaymentId } = job.data;

      const paymentData = await this.xeroPaymentService.findOne(
        userId,
        xeroPaymentId,
      );
      const { user, xeroPaymentHistories } = paymentData;
      const userConfig = await this.userConfigService.findOneByUser(userId);
      await this.mailerService.sendMail(
        {
          to: user.email,
          subject: 'Payment Pending (2nd)',
          html: EMAIL_TEMPLATE.PAYMENT_PENDING_2ND,
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: `${user.firstName} ${user.lastName}`,
          amount: xeroPaymentHistories?.at(-1).discountPrice,
          dueDate: moment(paymentData.createdAt)
            .add(5, 'days')
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
          paymentLink: this.configService.get('ADMIN_LINK'),
        },
        'mjml',
      );

      await this.xeroPaymentQueue.add(
        'sendMailFailed',
        { userId, xeroPaymentId },
        {
          //delay 2 days
          delay: 2 * 24 * 60 * 60 * 1000,
          jobId: `sendMailFailed_${user.id}`,
        },
      );

      console.log('Token refreshed and saved successfully.');
    } catch (error) {
      console.error('Error processing sendMail2nd job:', error.message);
    }
  }

  @Process('sendMail3rd')
  async sendMail3rd(job: Job) {
    console.log(`Processing sendMail3rd job: ${JSON.stringify(job.data)}`);

    try {
      const { userId, xeroPaymentId } = job.data;

      const paymentData = await this.xeroPaymentService.findOne(
        userId,
        xeroPaymentId,
      );
      const { user, xeroPaymentHistories, package: packageData } = paymentData;
      const userConfig = await this.userConfigService.findOneByUser(userId);
      await this.mailerService.sendMail(
        {
          to: user.email,
          subject: 'Payment Failed',
          html: EMAIL_TEMPLATE.PAYMENT_FAILED,
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: `${user.firstName} ${user.lastName}`,
          packageName: packageData.name,
          amount: xeroPaymentHistories?.at(-1).discountPrice,
          dueDate: moment(paymentData.createdAt)
            .add(5, 'days')
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
        },
        'mjml',
      );

      console.log('Token refreshed and saved successfully.');
    } catch (error) {
      console.error('Error processing sendMail3rd job:', error.message);
    }
  }
}
