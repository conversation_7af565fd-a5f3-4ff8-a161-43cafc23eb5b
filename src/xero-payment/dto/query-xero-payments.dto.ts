import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { XeroPayment } from '../entities/xero-payment.entity';
import { EXeroPaymentStatus } from '../enums/xero-payment-status';
import { EXeroPaymentType } from '../enums/xero-payment-type.enum';

class XeroPaymentFilter
  implements Partial<Pick<XeroPayment, 'status' | 'type'>>
{
  @IsOptional()
  @IsEnum(EXeroPaymentStatus)
  status?: EXeroPaymentStatus;

  @IsOptional()
  @IsEnum(EXeroPaymentType)
  type?: EXeroPaymentType;

  @IsOptional()
  @IsUUID()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsUUID()
  @IsString()
  id?: string;
}

export class QueryXeroPaymentsDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => XeroPaymentFilter)
  filter?: XeroPaymentFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
