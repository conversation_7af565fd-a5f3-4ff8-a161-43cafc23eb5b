import { IsArray, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { EXeroPaymentStatus } from '../enums/xero-payment-status';

export class GetXeroPaymentDto {
  @IsOptional()
  @IsUUID()
  packageId?: string;

  @IsOptional()
  @IsEnum(EXeroPaymentStatus)
  status?: EXeroPaymentStatus;

  @IsOptional()
  @IsEnum(EXeroPaymentStatus, { each: true })
  @IsArray()
  statuses?: EXeroPaymentStatus[];

  @IsOptional()
  @IsUUID()
  userId?: string;
}
