import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, IsNull, Repository } from 'typeorm';
import { XeroPayment } from './entities/xero-payment.entity';
import { XeroProvider } from './xero-payment.provider';
import { UserPackageService } from 'src/user-package/user-package.service';
import { CreateXeroPaymentDto } from './dto/create-xero-payment.dto';
import { PackageService } from 'src/package/package.service';
import { UsersService } from 'src/users/users.service';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';
import { XeroWebhookBody } from './interfaces/xero-webhook-body.interface';
import { EEventCategory } from './enums/event-category.enum';
import { EEventType } from './enums/event-type.enum';
import { InvoiceService } from 'src/invoice/invoice.service';
import { EXeroPaymentType } from './enums/xero-payment-type.enum';
import { EPromotionType } from 'src/promotion/enums/promotion-type.enum.';
import { EXeroPaymentStatus } from './enums/xero-payment-status';
import { XeroPaymentHistoryService } from 'src/xero-payment-history/xero-payment-history.service';
import { UpdateXeroPaymentHistory } from './dto/update-xero-payment-history.dto';
import { GetXeroPaymentDto } from './dto/query-xero-payment-dto';
import { QueryXeroPaymentsDto } from './dto/query-xero-payments.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { ApproveRejectXeroTransactionDto } from './dto/approve-reject-transaction.dto';
import { UpdateAssetXeroPaymentDto } from './dto/update-asset-xero-payment.dto';
import { Invoice } from 'xero-node';
import { UserFlagService } from 'src/user-flag/user-flag.service';
import { EUserFlagType } from 'src/user-flag/enums/user-flag-type.enum';
import { EXeroPaymentMethod } from './enums/xero-payment-method';
import { MergedXeroPaymentHistoryDto } from 'src/xero-payment-history/dto/merged-payment-history.dto';
import { PromotionService } from 'src/promotion/promotion.service';
import { PromotionUsageService } from 'src/promotion-usage/promotion-usage.service';
import { PromotionUsage } from 'src/promotion-usage/entities/promotion-usage.entity';
import { Promotion } from 'src/promotion/entities/promotion.entity';
import { EUsageLimitType } from 'src/promotion/enums/usage-limit-type.enum';
import { EPromotionStatus } from 'src/promotion/enums/promotion-status.enum';
import { NotificationAppService } from 'src/notification-app/notification-app.service';
import { ETitleKey } from 'src/notification-app/enums/title-key.enum';
import { EDescriptionKey } from 'src/notification-app/enums/description-key.enum';
import { Roles } from 'src/users/entities/user.entity';
import { MailerService } from 'src/mailer/mailer.service';
import { EMAIL_TEMPLATE } from 'src/mailer/enums/mailer.enum';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import * as moment from 'moment-timezone';
import { UserConfigService } from 'src/user-config/user-config.service';

@Injectable()
export class XeroPaymentService {
  constructor(
    @InjectRepository(XeroPayment)
    private readonly xeroPaymentRepository: Repository<XeroPayment>,
    @InjectQueue('xero_payment_queue')
    private readonly xeroPaymentQueue: Queue,
    private readonly xeroProvider: XeroProvider,
    private readonly userPackageService: UserPackageService,
    private readonly packageService: PackageService,
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private readonly invoiceService: InvoiceService,
    private readonly promotionService: PromotionService,
    private readonly promotionUsageService: PromotionUsageService,
    private readonly xeroPaymentHistoryService: XeroPaymentHistoryService,
    private readonly userFlagService: UserFlagService,
    private readonly notificationAppService: NotificationAppService,
    private readonly mailerService: MailerService,
    private readonly userConfigService: UserConfigService,
  ) {}

  async getAll(dto: QueryXeroPaymentsDto) {
    const { filter, sort, search } = dto;

    const pagination = getPaginationOption(dto);

    const queryBuilder = this.xeroPaymentRepository
      .createQueryBuilder('xeroPayment')
      .leftJoinAndSelect('xeroPayment.user', 'user')
      .leftJoin('xeroPayment.asset', 'asset')
      .addSelect(['asset.urls'])
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.status)
        queryBuilder.andWhere('xeroPayment.status = :status', {
          status: filter.status,
        });

      if (filter.type)
        queryBuilder.andWhere('xeroPayment.type = :type', {
          type: filter.type,
        });

      if (filter.userId)
        queryBuilder.andWhere('xeroPayment.userId = :userId', {
          userId: filter.userId,
        });

      if (filter.id)
        queryBuilder.andWhere('xeroPayment.id = :id', {
          id: filter.id,
        });
    }

    if (search) {
      queryBuilder.andWhere(
        `LOWER(CONCAT(user.firstName, ' ', user.lastName)) LIKE LOWER(:search)`,
        { search: `%${search}%` },
      );
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`xeroPayment.${key}`, order);
      });
    }

    const [xeroPayments, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(xeroPayments, total, pagination);
  }

  async findOne(userId: string | undefined, paymentId: string) {
    const where: FindOptionsWhere<XeroPayment> = {
      id: paymentId,
      deletedAt: IsNull(),
    };
    if (userId) {
      where.userId = userId;
    }

    return await this.xeroPaymentRepository.findOne({
      where,
      relations: ['user', 'package', 'promotionUsage', 'xeroPaymentHistories'],
    });
  }

  async getXeroPayment(userId: string | undefined, dto: GetXeroPaymentDto) {
    const { packageId, status, statuses, userId: paramUserId } = dto;
    const queryBuilder = this.xeroPaymentRepository
      .createQueryBuilder('xero_payment')
      .andWhere('xero_payment.deletedAt IS NULL')
      .leftJoinAndSelect('xero_payment.package', 'package')
      .leftJoinAndSelect(
        'xero_payment.xeroPaymentHistories',
        'xero_payment_history',
      )
      .leftJoinAndSelect('xero_payment.promotionUsage', 'promotion_usage')
      .leftJoinAndSelect('promotion_usage.promotion', 'promotion')
      .orderBy('xero_payment.createdAt', 'DESC')
      .limit(1);

    if (packageId) {
      queryBuilder.andWhere('xero_payment.packageId = :packageId', {
        packageId,
      });
    }

    if (userId) {
      queryBuilder.andWhere('xero_payment.userId = :userId', { userId });
    }

    if (!userId && paramUserId) {
      queryBuilder.andWhere('xero_payment.userId = :userId', { paramUserId });
    }

    if (status) {
      queryBuilder.andWhere('xero_payment.status = :status', { status });
    }

    if (statuses) {
      queryBuilder.andWhere('xero_payment.status IN (:...statuses)', {
        statuses,
      });
    }

    return await queryBuilder.getOne();
  }

  async createPayment(dto: CreateXeroPaymentDto, userId: string) {
    const { packageId, promotionCode, type } = dto;

    switch (type) {
      case EXeroPaymentType.PACKAGE: {
        return await this.createPackagePayment({
          packageId,
          promotionCode,
          userId,
        });
      }
      default: {
        throw new NotFoundException(`Not found ${type} in payment type`);
      }
    }
  }

  async findOrCreatePayment(userId: string, dto: CreateXeroPaymentDto) {
    // Only find user's package payment  is pending  => If failed or succeed, should be create a new one
    const xeroPaymentPending = await this.getXeroPayment(userId, {
      packageId: dto.packageId,
      status: EXeroPaymentStatus.PENDING,
    });

    if (xeroPaymentPending) {
      return xeroPaymentPending;
    }

    return await this.createPayment(dto, userId);
  }

  async callback(query: any) {
    try {
      const callbackUrl = `${this.configService.get('XERO_REDIRECT_URI')}?${new URLSearchParams(query).toString()}`;
      await this.xeroProvider.exchangeCodeForToken(callbackUrl);

      return {
        message: 'Authentication successfully',
      };
    } catch (error) {
      return {
        message: 'Authentication failed',
        error: error.message,
      };
    }
  }

  async webhooks(signature: string, rawBody: Buffer) {
    await this.xeroProvider.ensureValidToken();

    const computedSignature = crypto
      .createHmac('sha256', this.configService.get('XERO_WEBHOOK_KEY'))
      .update(rawBody)
      .digest('base64');

    if (computedSignature !== signature) {
      throw new UnauthorizedException('Invalid Xero signature');
    }

    const body: XeroWebhookBody = JSON.parse(rawBody.toString('utf8'));
    console.log('Webhook body:', body);

    for await (const event of body?.events || []) {
      try {
        switch (true) {
          case event.eventCategory === EEventCategory.INVOICE &&
            event.eventType === EEventType.UPDATE: {
            await this.updateTransaction({
              xeroInvoiceID: event.resourceId,
            });
            break;
          }
          case event.eventCategory === EEventCategory.INVOICE &&
            event.eventType === EEventType.CREATE: {
            break;
          }
          default: {
            throw new NotFoundException(
              `Not found ${event.eventCategory} - ${event.eventType}`,
            );
          }
        }
      } catch (error) {
        console.log('Error', error);
        return { ok: true };
      }
    }
    return { ok: true };
  }

  async createPackagePayment(data: {
    packageId: string;
    promotionCode: string;
    userId: string;
  }) {
    const { packageId, promotionCode, userId } = data;
    const packageData = await this.packageService.findPackageById(packageId);
    let promotionData: Promotion | undefined;
    let promotionUsageData: PromotionUsage | undefined;

    if (!packageData) {
      throw new Error('Package not found');
    }

    if (promotionCode) {
      promotionData = await this.promotionService.getOne({
        code: promotionCode,
        type: EPromotionType.PACKAGE,
        status: EPromotionStatus.ACTIVE,
        activeAt: new Date(),
        userId,
      });

      if (!promotionData) {
        throw new Error('Promotion not found');
      }

      promotionUsageData = await this.promotionUsageService.create({
        promotionId: promotionData.id,
        byUserId: userId,
      });
    }

    const user = await this.usersService.findOne(userId);
    const transaction = this.xeroPaymentRepository.create({
      userId: user.id,
      packageId: packageData.id,
      status: EXeroPaymentStatus.INIT,
      promotionUsageId: promotionUsageData ? promotionUsageData.id : undefined,
      type: EXeroPaymentType.PACKAGE,
    });

    //Step 1: Generated xero payment
    const savedTransaction = await this.xeroPaymentRepository.save(transaction);
    const xeroPaymentHistoryGenerated =
      await this.xeroPaymentHistoryService.create({
        xeroPaymentId: savedTransaction.id,
        xeroPaymentType: savedTransaction.type,
        userId: savedTransaction.userId,
        packageId: savedTransaction.packageId,
        status: savedTransaction.status,
        promotionUsageId: savedTransaction.promotionUsageId,
        paymentUrl: savedTransaction.paymentUrl,
        onlineInvoiceUrl: savedTransaction.onlineInvoiceUrl,
      });

    //Step 2: Create xero invoice
    const {
      invoiceID,
      paymentUrl,
      onlineInvoiceUrl,
      subTotal,
      totalTax,
      total,
    } = await this.xeroProvider.createInvoice(
      user,
      savedTransaction,
      packageData,
      promotionData && promotionUsageData ? promotionData : undefined,
    );

    await this.xeroPaymentRepository.update(savedTransaction.id, {
      paymentUrl,
      onlineInvoiceUrl,
      xeroInvoiceID: invoiceID,
      status: EXeroPaymentStatus.PENDING,
    });
    const xeroPaymentHistory = await this.xeroPaymentHistoryService.create({
      xeroPaymentId: savedTransaction.id,
      xeroPaymentType: savedTransaction.type,
      userId: savedTransaction.userId,
      packageId: savedTransaction.packageId,
      status: EXeroPaymentStatus.PENDING,
      promotionUsageId: savedTransaction.promotionUsageId,
      paymentUrl,
      onlineInvoiceUrl,
      xeroInvoiceID: invoiceID,

      originPrice: packageData.price || 0,
      discountPrice: subTotal || 0,
      totalTax: totalTax || 0,
      total: total || 0,
      paid: 0,
      totalDue: total || 0,
    });

    await this.notificationAppService.create({
      titleKey: ETitleKey.PAYMENT,
      descriptionKey: EDescriptionKey.PENDING_PAYMENT,
      type: EXeroPaymentType.PACKAGE,
      data: {
        name: `${user.firstName} ${user.lastName}`,
        paymentId: savedTransaction.id,
        userId,
        email: user.email,
      },
      accessRole: Roles.ADMIN,
    });
    const userConfig = await this.userConfigService.findOneByUser(user.id);
    await this.mailerService.sendMail(
      {
        to: user.email,
        subject: 'Package Order Confirmation',
        html: EMAIL_TEMPLATE.PACKAGE_ORDER_CONFIRMATION,
      },
      {
        currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
        agentName: `${user.firstName} ${user.lastName}`,
        packageName: packageData.name,
        price: packageData.price,
        loginLink: this.configService.get('ADMIN_LINK'),
      },
      'mjml',
    );

    await this.xeroPaymentQueue.add(
      'sendMail1st',
      { userId, xeroPaymentId: savedTransaction.id },
      {
        //delay 1 days
        delay: 1 * 24 * 60 * 60 * 1000,
        jobId: `sendMail1st_${userId}`,
      },
    );

    return {
      ...savedTransaction,
      status: EXeroPaymentStatus.PENDING,
      package: packageData,
      promotionUsage: promotionUsageData || null,
      paymentUrl,
      onlineInvoiceUrl,
      xeroInvoiceID: invoiceID,
      xeroPaymentHistories: [xeroPaymentHistoryGenerated, xeroPaymentHistory],
    };
  }

  async updateTransactionStatus(
    transactionId: string,
    status: EXeroPaymentStatus,
  ): Promise<void> {
    await this.xeroPaymentRepository.update(transactionId, { status });
  }

  async updateHistory(dto: UpdateXeroPaymentHistory) {
    const xeroPaymentData = await this.updatePaymentHistory(dto);
    return await this.xeroPaymentRepository.save(xeroPaymentData);
  }

  async updatePaymentHistory(
    dto: UpdateXeroPaymentHistory,
    xeroPaymentData?: XeroPayment,
  ) {
    const isUpdateOne = Boolean(xeroPaymentData);
    const { xeroInvoiceID, xeroPaymentId } = dto;

    if (!xeroPaymentData) {
      xeroPaymentData = await this.xeroPaymentRepository.findOne({
        where: { id: xeroPaymentId, deletedAt: IsNull() },
        relations: ['user', 'package'],
      });

      if (!xeroPaymentData) {
        throw new NotFoundException('XeroPayment not found');
      }
    }

    const xeroInvoiceData =
      await this.xeroProvider.xeroClient.accountingApi.getInvoice(
        this.configService.get('XERO_TENANT_ID'),
        xeroInvoiceID,
      );
    const invoice = xeroInvoiceData.body.invoices?.at(0);
    const methodDefault = EXeroPaymentMethod.CREDIT_CARD;
    xeroPaymentData.createdAt = invoice.updatedDateUTC;
    xeroPaymentData.updatedAt = invoice.updatedDateUTC;

    //reject
    if (invoice.status === Invoice.StatusEnum.VOIDED) {
      await this.removePendingSendMailQueueJob(xeroPaymentData.user.id);

      xeroPaymentData.status = EXeroPaymentStatus.FAILED;
      await this.saveMergedPaymentHistory(xeroPaymentData, {
        originPrice: xeroPaymentData.package.price || 0,
        discountPrice: invoice.subTotal || 0,
        totalTax: invoice.totalTax || 0,
        total: invoice.total || 0,
        paid: invoice.payments?.at(-1).amount || 0,
        totalDue: invoice.amountDue || 0,
        xeroPaymentID: invoice.payments?.at(-1).paymentID,
      });
      await this.notificationAppService.create({
        titleKey: ETitleKey.PAYMENT,
        descriptionKey: EDescriptionKey.REJECTED_PAYMENT,
        type: EXeroPaymentType.PACKAGE,
        data: {
          name: `${xeroPaymentData.user.firstName} ${xeroPaymentData.user.lastName}`,
          paymentId: xeroPaymentData.id,
          userId: xeroPaymentData.userId,
          email: xeroPaymentData.user.email,
        },
        accessRole: Roles.ADMIN,
      });
      const userConfig = await this.userConfigService.findOneByUser(
        xeroPaymentData.userId,
      );
      await this.mailerService.sendMail(
        {
          to: xeroPaymentData.user.email,
          subject: 'Payment Failed',
          html: EMAIL_TEMPLATE.PAYMENT_FAILED,
        },
        {
          currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
          agentName: `${xeroPaymentData.user.firstName} ${xeroPaymentData.user.lastName}`,
          packageName: xeroPaymentData.package.name,
          amount: invoice.subTotal,
          dueDate: moment(xeroPaymentData.createdAt)
            .add(5, 'days')
            .utcOffset(8)
            .format('HH:mm on MMM DD, YYYY'),
        },
        'mjml',
      );
    } else {
      if (!(invoice.payments || [])?.length) {
        throw new BadRequestException('Please make this payment');
      }
      xeroPaymentData.method = xeroPaymentData.method || methodDefault;

      if (isUpdateOne) {
        xeroPaymentData.status =
          invoice.amountDue === 0
            ? EXeroPaymentStatus.SUCCEED
            : EXeroPaymentStatus.PENDING;

        await this.saveMergedPaymentHistory(xeroPaymentData, {
          originPrice: xeroPaymentData.package.price,
          discountPrice: invoice.subTotal,
          totalTax: invoice.totalTax,
          total: invoice.total,
          paid: invoice.payments?.at(-1).amount,
          totalDue: invoice.amountDue || 0,
          xeroPaymentID: invoice.payments?.at(-1).paymentID,
        });
      } else {
        let totalDue = invoice.subTotal || 0;
        for await (const payment of invoice.payments) {
          totalDue -= payment.amount;
          xeroPaymentData.status =
            totalDue === 0
              ? EXeroPaymentStatus.SUCCEED
              : EXeroPaymentStatus.PENDING;
          await this.saveMergedPaymentHistory(xeroPaymentData, {
            originPrice: xeroPaymentData.package.price || 0,
            discountPrice: invoice.subTotal || 0,
            totalTax: invoice.totalTax || 0,
            total: invoice.total || 0,
            paid: payment.amount || 0,
            totalDue: totalDue,
            xeroPaymentID: payment.paymentID,
          });
        }
      }

      if (invoice.amountDue === 0) {
        await this.removePendingSendMailQueueJob(xeroPaymentData.user.id);

        const invoiceData = await this.invoiceService.createInvoice({
          xeroPaymentId: xeroPaymentData.id,
          xeroInvoiceID: xeroPaymentData.xeroInvoiceID,
          originPrice: xeroPaymentData.package.price || 0,
          discountPrice: invoice.subTotal || 0,
          totalTax: invoice.totalTax || 0,
          total: invoice.total || 0,
          paid: invoice.amountPaid || 0,
          totalDue: invoice.amountDue || 0,
        });

        await this.userPackageService.attachUserPackage({
          packageId: xeroPaymentData.package.id,
          userId: xeroPaymentData.user.id,
        });

        await this.notificationAppService.create({
          titleKey: ETitleKey.PAYMENT,
          descriptionKey: EDescriptionKey.SUCCESSFUL_PAYMENT,
          type: EXeroPaymentType.PACKAGE,
          data: {
            name: `${xeroPaymentData.user.firstName} ${xeroPaymentData.user.lastName}`,
            paymentId: xeroPaymentData.id,
            userId: xeroPaymentData.userId,
            email: xeroPaymentData.user.email,
          },
          accessRole: Roles.ADMIN,
        });
        await this.userFlagService.create({
          userId: xeroPaymentData.user.id,
          type: EUserFlagType.PAYMENT_CONFIRMED,
          data: { paymentId: xeroPaymentData.id },
        });
        const userConfig = await this.userConfigService.findOneByUser(
          xeroPaymentData.userId,
        );
        await this.mailerService.sendMail(
          {
            to: xeroPaymentData.user.email,
            subject: 'Payment Completed',
            html: EMAIL_TEMPLATE.PAYMENT_COMPLETED,
          },
          {
            currentEa: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
            agentName: `${xeroPaymentData.user.firstName} ${xeroPaymentData.user.lastName}`,
            packageName: xeroPaymentData.package.name,
            loginLink: this.configService.get('ADMIN_LINK'),
          },
          'mjml',
        );

        xeroPaymentData.status = EXeroPaymentStatus.SUCCEED;
        xeroPaymentData.invoiceId = invoiceData.id;
      }
    }

    return xeroPaymentData;
  }

  async updateTransaction(data: { xeroInvoiceID: string }) {
    const { xeroInvoiceID } = data;
    const where: FindOptionsWhere<XeroPayment> = {
      status: In([EXeroPaymentStatus.PENDING, EXeroPaymentStatus.IN_PROGRESS]),
      xeroInvoiceID,
    };

    const xeroPaymentData = await this.xeroPaymentRepository.findOne({
      where,
      relations: ['user', 'package'],
    });

    if (!xeroPaymentData) {
      throw new NotFoundException('XeroPayment not found');
    }

    await this.updatePaymentHistory(
      { xeroInvoiceID, xeroPaymentId: xeroPaymentData.id },
      xeroPaymentData,
    );

    try {
      return await this.xeroPaymentRepository.save(xeroPaymentData);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async approveTransaction(data: ApproveRejectXeroTransactionDto) {
    const { xeroInvoiceID, reason } = data;
    const where: FindOptionsWhere<XeroPayment> = {
      status: EXeroPaymentStatus.PENDING,
      xeroInvoiceID,
    };

    const xeroPaymentData = await this.xeroPaymentRepository.findOne({
      where,
    });

    if (!xeroPaymentData) {
      throw new NotFoundException('XeroPayment not found');
    }

    try {
      const xeroInvoiceData =
        await this.xeroProvider.xeroClient.accountingApi.getInvoice(
          this.configService.get('XERO_TENANT_ID'),
          xeroInvoiceID,
        );
      const invoice = xeroInvoiceData.body.invoices?.at(0);

      xeroPaymentData.status = EXeroPaymentStatus.IN_PROGRESS;
      xeroPaymentData.reason = reason;

      const xeroPaymentHistoryLatestData =
        await this.xeroPaymentHistoryService.findTheLatest(xeroPaymentData.id);

      await this.xeroPaymentHistoryService.createOver({
        ...xeroPaymentHistoryLatestData,
        id: undefined,
        status: EXeroPaymentStatus.IN_PROGRESS,
        reason,
      });

      await this.xeroProvider.paidInvoice({
        invoiceID: xeroInvoiceID,
        amount: invoice.amountDue,
      });

      return await this.xeroPaymentRepository.save(xeroPaymentData);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async voidTransaction(
    userId: string | undefined,
    dto: ApproveRejectXeroTransactionDto,
  ) {
    const { xeroInvoiceID, reason } = dto;
    return await this.rejectTransaction({
      xeroInvoiceID,
      userId,
      reason,
    });
  }

  async rejectTransaction(data: {
    xeroInvoiceID: string;
    userId?: string;
    reason?: string;
  }) {
    const { xeroInvoiceID, userId, reason } = data;
    const where: FindOptionsWhere<XeroPayment> = {
      status: EXeroPaymentStatus.PENDING,
      xeroInvoiceID,
    };
    if (userId) {
      where.userId = userId;
    }

    const xeroPaymentData = await this.xeroPaymentRepository.findOne({
      where,
    });

    if (!xeroPaymentData) {
      throw new NotFoundException('XeroPayment not found');
    }

    try {
      xeroPaymentData.status = EXeroPaymentStatus.IN_PROGRESS;
      xeroPaymentData.reason = reason;

      const xeroPaymentHistoryLatestData =
        await this.xeroPaymentHistoryService.findTheLatest(xeroPaymentData.id);

      await this.xeroPaymentHistoryService.createOver({
        ...xeroPaymentHistoryLatestData,
        id: undefined,
        status: EXeroPaymentStatus.IN_PROGRESS,
        reason,
      });

      await this.xeroProvider.voidInvoice({ invoiceID: xeroInvoiceID });

      return await this.xeroPaymentRepository.save(xeroPaymentData);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async connect() {
    try {
      await this.xeroProvider.ensureValidToken();
      return null;
    } catch (error) {
      console.log(error);
      const url = await this.xeroProvider.getAuthUrl();
      return { url };
    }
  }

  async delete(query: FindOptionsWhere<XeroPayment>) {
    const { id } = query;
    const where: FindOptionsWhere<XeroPayment> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const voucherData = await this.xeroPaymentRepository.findOneBy(where);
    if (!voucherData) {
      throw new NotFoundException('XeroPayment not found');
    }

    const result = await this.xeroPaymentRepository.softDelete(where);
    return result;
  }

  async updateAsset(userId: string, dto: UpdateAssetXeroPaymentDto) {
    const { assetId, xeroPaymentId } = dto;
    const xeroPaymentData = await this.xeroPaymentRepository.findOne({
      where: { id: xeroPaymentId, userId, deletedAt: IsNull() },
      relations: ['package', 'user'],
    });

    if (!xeroPaymentData) {
      throw new NotFoundException('XeroPayment not found');
    }

    const xeroPaymentHistoryLatestData =
      await this.xeroPaymentHistoryService.findTheLatest(xeroPaymentData.id);

    await this.xeroPaymentHistoryService.createOver({
      ...xeroPaymentHistoryLatestData,
      assetId,
      method: EXeroPaymentMethod.PAY_NOW,
      id: undefined,
    });

    const result = await this.xeroPaymentRepository.update(xeroPaymentData.id, {
      assetId,
      method: EXeroPaymentMethod.PAY_NOW,
    });

    await this.notificationAppService.create({
      titleKey: ETitleKey.PAYMENT,
      descriptionKey: EDescriptionKey.UPLOADED_SCREENSHOT,
      type: EXeroPaymentType.PACKAGE,
      data: {
        name: `${xeroPaymentData.user.firstName} ${xeroPaymentData.user.lastName}`,
        paymentId: xeroPaymentData.id,
        userId,
        email: xeroPaymentData.user.email,
      },
      accessRole: Roles.ADMIN,
    });

    return result;
  }

  async saveMergedPaymentHistory(
    xeroPayment: XeroPayment,
    mergedXeroPaymentHistory: MergedXeroPaymentHistoryDto,
  ) {
    const {
      id,
      userId,
      packageId,
      invoiceId,
      paymentUrl,
      onlineInvoiceUrl,
      xeroInvoiceID,
      status,
      promotionUsageId,
      type,
      assetId,
      method,
      reason,
      createdAt,
      updatedAt,
    } = xeroPayment;
    const {
      originPrice = 0,
      discountPrice = 0,
      totalTax = 0,
      total = 0,
      paid = 0,
      totalDue = 0,
      xeroPaymentID,
    } = mergedXeroPaymentHistory;

    await this.xeroPaymentHistoryService.create({
      xeroPaymentId: id,
      userId,
      packageId,
      paymentUrl,
      onlineInvoiceUrl,
      xeroInvoiceID,
      status,
      promotionUsageId,
      xeroPaymentType: type,
      assetId,
      method,
      reason,
      createdAt,
      updatedAt,

      //price
      originPrice,
      discountPrice,
      totalTax,
      total,
      paid,
      totalDue,
      xeroPaymentID,
    });
  }

  async removePendingSendMailQueueJob(userId: string) {
    await this.xeroPaymentQueue.removeJobs(`sendMail1st_${userId}`);
    await this.xeroPaymentQueue.removeJobs(`sendMail2nd_${userId}`);
    await this.xeroPaymentQueue.removeJobs(`sendMail3rd_${userId}`);
  }
}
