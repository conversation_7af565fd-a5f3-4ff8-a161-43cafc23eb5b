import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { XeroPayment } from './entities/xero-payment.entity';
import { XeroPaymentService } from './xero-payment.service';
import { XeroPaymentController } from './xero-payment.controller';
import { UsersModule } from 'src/users/users.module';
import { XeroProvider } from './xero-payment.provider';
import { PackageModule } from 'src/package/package.module';
import { InvoiceModule } from 'src/invoice/invoice.module';
import { HttpModule } from '@nestjs/axios';
import { RedisModule } from 'src/redis/redis.module';
import { BullModule } from '@nestjs/bull';
import { XeroPaymentQueue } from './xero-payment.queue';
import { UserPackageModule } from 'src/user-package/user-package.module';
import { PromotionUsageModule } from 'src/promotion-usage/promotion-usage.module';
import { XeroPaymentHistoryModule } from 'src/xero-payment-history/xero-payment-history.module';
import { UserFlagModule } from 'src/user-flag/user-flag.module';
import { PromotionModule } from 'src/promotion/promotion.module';
import { NotificationAppModule } from 'src/notification-app/notification-app.module';
import { MailerModule } from 'src/mailer/mailer.module';
import { UserConfigModule } from 'src/user-config/user-config.module';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([XeroPayment]),
    RedisModule,
    BullModule.registerQueue({
      name: 'xero_payment_queue',
    }),
    UserPackageModule,
    MailerModule,
    forwardRef(() => PromotionModule),
    forwardRef(() => PromotionUsageModule),
    forwardRef(() => XeroPaymentHistoryModule),
    forwardRef(() => UserFlagModule),
    forwardRef(() => UsersModule),
    forwardRef(() => InvoiceModule),
    forwardRef(() => PackageModule),
    forwardRef(() => NotificationAppModule),
    forwardRef(() => UserConfigModule),
  ],
  providers: [XeroPaymentService, XeroProvider, XeroPaymentQueue],
  controllers: [XeroPaymentController],
  exports: [XeroPaymentService, XeroProvider],
})
export class XeroPaymentModule {}
