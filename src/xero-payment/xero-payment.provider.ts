import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import {
  Account,
  AccountType,
  Invoice,
  Invoices,
  LineItem,
  Payment,
  TokenSet,
  XeroClient,
} from 'xero-node';
import { ConfigService } from '@nestjs/config';
import { XeroPayment } from './entities/xero-payment.entity';
import { Package } from 'src/package/entities/package.entity';
import { RedisService } from 'src/redis/redis.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { User } from 'src/users/entities/user.entity';
import { Promotion } from 'src/promotion/entities/promotion.entity';
import { EDiscountType } from 'src/promotion/enums/discount-type.enum';
import { EPackageDiscountType } from 'src/package/enums/package-discount-type.enum';

@Injectable()
export class XeroProvider {
  xeroClient: XeroClient;
  tenantId: string | null;
  private tokenSet: TokenSet = null;

  constructor(
    @Inject(forwardRef(() => ConfigService))
    private readonly configService: ConfigService,

    private readonly redisService: RedisService,

    @InjectQueue('xero_payment_queue') private readonly xeroPaymentQueue: Queue,
  ) {
    this.xeroClient = new XeroClient({
      clientId: this.configService.get<string>('XERO_CLIENT_ID'),
      clientSecret: this.configService.get<string>('XERO_CLIENT_SECRET'),
      redirectUris: [this.configService.get<string>('XERO_REDIRECT_URI')],
      scopes: [
        'openid',
        'profile',
        'email',
        'accounting.transactions',
        'accounting.contacts',
        'offline_access',
        'accounting.settings',
      ],
    });

    this.tenantId = this.configService.get<string>('XERO_TENANT_ID');
  }

  async getAuthUrl(): Promise<string> {
    return await this.xeroClient.buildConsentUrl();
  }

  async exchangeCodeForToken(fullCallbackUrl: string) {
    try {
      const tokenSet = await this.xeroClient.apiCallback(fullCallbackUrl);
      this.saveTokens(tokenSet);

      return tokenSet;
    } catch (error) {
      console.error('Error exchanging code for token:', error);
      throw error;
    }
  }

  async refreshAccessToken(refreshToken: string) {
    try {
      const clientId = this.configService.get('XERO_CLIENT_ID');
      const clientSecret = this.configService.get('XERO_CLIENT_SECRET');

      const tokenSet = await this.xeroClient.refreshWithRefreshToken(
        clientId,
        clientSecret,
        refreshToken,
      );

      return tokenSet;
    } catch (error) {
      console.error('Error refreshing access token:', error.message);
      throw error;
    }
  }

  async getToken(): Promise<TokenSet> {
    try {
      const tokenString =
        await this.redisService.get<string>(`xero_token_admin`);
      if (!tokenString) {
        throw new Error('Token not found in Redis. Please login to Xero.');
      }
      this.tokenSet = JSON.parse(tokenString);
      return this.tokenSet;
    } catch (error) {
      console.error('Error retrieving token:', error.message);
      throw error;
    }
  }

  async saveTokens(tokenSet: TokenSet) {
    try {
      if (!tokenSet || !tokenSet.expires_at || !tokenSet.refresh_token) {
        throw new Error('Invalid token set: missing required fields.');
      }

      const expiresAtUTC = new Date(tokenSet.expires_at * 1000).toISOString();
      const currentUTC = new Date().toISOString();
      const expiresInMs = tokenSet.expires_at * 1000 - Date.now();

      console.log('Token expires_at (UTC):', expiresAtUTC);
      console.log('Current time (UTC):', currentUTC);
      console.log('Expires in (milliseconds):', expiresInMs);

      if (expiresInMs <= 0) {
        throw new Error('Token is already expired.');
      }

      this.xeroClient.setTokenSet(tokenSet);
      this.tokenSet = tokenSet;

      await this.redisService.set(
        `xero_token_admin`,
        JSON.stringify(tokenSet),
        Math.floor(expiresInMs / 1000), // TTL in seconds
      );

      console.log(
        `Token saved successfully. Expires in ${expiresInMs / 1000} seconds.`,
      );

      if (expiresInMs > 5 * 60 * 1000) {
        await this.scheduleTokenRefresh(
          tokenSet.refresh_token,
          expiresInMs - 5 * 60 * 1000,
        );
      } else {
        console.warn('Token will expire soon. Consider refreshing manually.');
      }
    } catch (error) {
      console.error('Error saving tokens:', error.message);
      throw error;
    }
  }

  async scheduleTokenRefresh(refreshToken: string, delayMs: number) {
    try {
      if (delayMs <= 0) {
        console.warn('Invalid delay for scheduling token refresh.');
        return;
      }

      await this.xeroPaymentQueue.add(
        'handleTokenRefresh',
        { refreshToken },
        { delay: delayMs },
      );

      console.log(
        `Refresh job scheduled successfully. Will run in ${delayMs / 1000} seconds.`,
      );
    } catch (error) {
      console.error('Error scheduling token refresh job:', error.message);
      throw error;
    }
  }

  async ensureValidToken(): Promise<void> {
    try {
      const token = await this.getToken();
      if (!token || !token.expires_at || !token.refresh_token) {
        throw new Error('Invalid token set: missing required fields.');
      }

      const expiresAt = token.expires_at * 1000;
      const now = Date.now();

      console.log('Current time (UTC):', new Date(now).toISOString());
      console.log('Token expires_at (UTC):', new Date(expiresAt).toISOString());

      // if (expiresAt - now < 5 * 60 * 1000) {
      //   console.log('Token is near expiration. Refreshing now...');
      //   // await this.refreshAccessToken(token.refresh_token);
      //   // await this.saveTokens(tokenSet);
      // }
      this.xeroClient.setTokenSet(token);
    } catch (error) {
      console.error('Error ensuring valid token:', error.message);
      throw error;
    }
  }

  async createInvoice(
    user: User,
    transaction: XeroPayment,
    packageData: Package,
    promotionData: Promotion,
  ) {
    try {
      if (!this.tenantId) {
        throw new Error('Tenant ID not available. Please initialize tenant.');
      }

      const contact = {
        name: user.firstName + ' ' + user.lastName,
        emailAddress: user.email,
      };

      /**
       * Calculating for discount price with package
       */
      let discountPackagePrice = packageData.price;
      if (packageData?.discountType) {
        const discount = Number(packageData.discount);
        let finalPrice = packageData.price;

        if (packageData.discountType === EPackageDiscountType.PERCENT) {
          finalPrice -= (packageData.price * discount) / 100;
        } else if (packageData.discountType === EPackageDiscountType.PRICE) {
          finalPrice -= discount;
        }

        discountPackagePrice = finalPrice > 0 ? finalPrice : 0;
      }

      /**
       * Calculating for discount price
       */
      let discountPrice = discountPackagePrice;
      if (promotionData?.discountType) {
        const discount = Number(promotionData.discount);
        let finalPrice = packageData.price;

        if (promotionData.discountType === EDiscountType.PERCENT) {
          finalPrice -= (packageData.price * discount) / 100;
        } else if (promotionData.discountType === EDiscountType.PRICE) {
          finalPrice -= discount;
        }

        discountPrice = finalPrice > 0 ? finalPrice : 0;
      }

      const lineItems: LineItem[] = [
        {
          description: packageData.name,
          quantity: 1,
          unitAmount: discountPrice,
          accountCode: '400',
          //disabled tax
          taxType: 'NONE',
        },
      ];

      const invoiceData: Invoice = {
        type: Invoice.TypeEnum.ACCREC,
        reference: transaction.id,
        contact,
        lineItems,
        date: new Date().toISOString(),
        dueDate: new Date(
          new Date().getTime() + 7 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        status: Invoice.StatusEnum.AUTHORISED,
      };

      const invoices: Invoices = {
        invoices: [invoiceData],
      };

      const idempotencyKey = `invoice-${transaction.id}`;
      const response = await this.xeroClient.accountingApi.createInvoices(
        this.tenantId,
        invoices,
        true,
        4,
        idempotencyKey,
      );
      if (!response?.body?.invoices?.length) {
        throw new Error('No invoice was created by Xero API.');
      }

      const invoice = response.body.invoices?.at(0);

      const responseInvoice =
        await this.xeroClient.accountingApi.getOnlineInvoice(
          this.tenantId,
          invoice.invoiceID,
        );
      const onlineInvoice = responseInvoice.body?.onlineInvoices?.at(0);

      /**
       * disabled because "Daily Email Rate Limit Exceeded"
       */
      // await this.xeroClient.accountingApi.emailInvoice(
      //   this.tenantId,
      //   invoice.invoiceID,
      //   {},
      // )

      const paymentUrl = `https://invoicing.xero.com/view/${invoice.invoiceID}`;

      console.log('Invoice created with payment URL:', paymentUrl);
      console.log('Public invoice:', onlineInvoice.onlineInvoiceUrl);
      return {
        ...invoice,
        paymentUrl,
        onlineInvoiceUrl: onlineInvoice.onlineInvoiceUrl,
      };
    } catch (error) {
      console.error('Error creating invoice in Xero:', error.message || error);
      throw new Error(
        `Failed to create invoice in Xero. Reason: ${error.message || 'Unknown error'}`,
      );
    }
  }

  async voidInvoice(data: { invoiceID: string }) {
    const { invoiceID } = data;
    const updatedInvoice = await this.xeroClient.accountingApi.updateInvoice(
      this.tenantId,
      invoiceID,
      {
        invoices: [
          {
            invoiceID,
            status: Invoice.StatusEnum.VOIDED,
          },
        ],
      },
    );
    return updatedInvoice;
  }
  async paidInvoice(data: { invoiceID: string; amount: number }) {
    const { invoiceID, amount } = data;

    const response = await this.xeroClient.accountingApi.getAccounts(
      this.tenantId,
    );

    const accounts = response.body.accounts;

    const bankAccount = accounts.find(
      (account) =>
        account.type === AccountType.BANK &&
        account.status === Account.StatusEnum.ACTIVE,
    );
    if (!bankAccount) {
      throw new BadRequestException("Don't have account to payment");
    }

    const payment: Payment = {
      invoice: { invoiceID },
      account: { accountID: bankAccount.accountID },
      date: new Date().toISOString(),
      amount,
    };

    await this.xeroClient.accountingApi.createPayment(this.tenantId, payment);
  }
}
