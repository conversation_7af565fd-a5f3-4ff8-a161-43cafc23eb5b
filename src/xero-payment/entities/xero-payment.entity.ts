import CrudEntity from 'src/core/entities/crud.entity';
import { Invoice } from 'src/invoice/entities/invoice.entity';
import { Package } from 'src/package/entities/package.entity';
import { User } from 'src/users/entities/user.entity';
import { PromotionUsage } from 'src/promotion-usage/entities/promotion-usage.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { EXeroPaymentType } from '../enums/xero-payment-type.enum';
import { EXeroPaymentStatus } from '../enums/xero-payment-status';
import { XeroPaymentHistory } from 'src/xero-payment-history/entities/xero-payment-history.entity';
import { Asset } from 'src/asset/entities/asset.entity';
import { EXeroPaymentMethod } from '../enums/xero-payment-method';

@Entity()
export class XeroPayment extends CrudEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  packageId: string;

  @ManyToOne(() => Package)
  @JoinColumn({ name: 'packageId' })
  package: Package;

  @Column({ type: 'uuid', nullable: true })
  invoiceId: string;

  @OneToOne(() => Invoice, (invoice) => invoice.xeroPayment)
  @JoinColumn({ name: 'invoiceId' })
  invoice: Invoice;

  @Column({ nullable: true })
  paymentUrl?: string;

  @Column({ nullable: true })
  onlineInvoiceUrl?: string;

  @Column({ nullable: true })
  xeroInvoiceID?: string;

  @Column({
    type: 'enum',
    enum: EXeroPaymentStatus,
    default: EXeroPaymentStatus.PENDING,
  })
  status: EXeroPaymentStatus;

  @Column({ type: 'uuid', nullable: true })
  promotionUsageId?: string;

  @OneToOne(
    () => PromotionUsage,
    (promotionUsage) => promotionUsage.xeroPayment,
  )
  @JoinColumn({ name: 'promotionUsageId' })
  promotionUsage?: PromotionUsage;

  @Column({
    type: 'enum',
    enum: EXeroPaymentType,
  })
  type: EXeroPaymentType;

  @OneToMany(() => XeroPaymentHistory, (history) => history.xeroPayment)
  xeroPaymentHistories?: XeroPaymentHistory[];

  @Column({ type: 'uuid', nullable: true })
  assetId?: string;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset?: Asset;

  @Column({
    type: 'enum',
    enum: EXeroPaymentMethod,
    nullable: true,
  })
  method: EXeroPaymentMethod;

  @Column({
    nullable: true,
  })
  reason: string;
}
