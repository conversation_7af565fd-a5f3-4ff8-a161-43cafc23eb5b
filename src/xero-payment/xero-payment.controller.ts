import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Query,
  Put,
  Param,
  Delete,
} from '@nestjs/common';
import { XeroPaymentService } from './xero-payment.service';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { XeroProvider } from './xero-payment.provider';
import { ApproveRejectXeroTransactionDto } from './dto/approve-reject-transaction.dto';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { CreateXeroPaymentDto } from './dto/create-xero-payment.dto';
import { XeroSignature } from 'src/xero-payment/decorators/xero-signature.decorator';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { UpdateXeroPaymentHistory } from './dto/update-xero-payment-history.dto';
import { GetXeroPaymentDto } from './dto/query-xero-payment-dto';
import { QueryXeroPaymentsDto } from './dto/query-xero-payments.dto';
import { UpdateAssetXeroPaymentDto } from './dto/update-asset-xero-payment.dto';

@Controller('xero-payment')
@UseGuards(RolesGuard)
export class XeroPaymentController {
  constructor(
    private readonly xeroPaymentService: XeroPaymentService,
    private readonly xeroProvider: XeroProvider,
  ) {}

  @Get()
  @AccessRoles(Roles.ADMIN)
  async getAll(@Query() dto: QueryXeroPaymentsDto) {
    return await this.xeroPaymentService.getAll(dto);
  }

  @Post()
  async createPayment(
    @Body() dto: CreateXeroPaymentDto,
    @ActiveUser('sub') userId: string,
  ) {
    await this.xeroProvider.ensureValidToken();
    return await this.xeroPaymentService.findOrCreatePayment(userId, dto);
  }

  @Get('current')
  async getXeroPayment(
    @ActiveUser('sub') userId: string,
    @Query() dto: GetXeroPaymentDto,
  ) {
    return await this.xeroPaymentService.getXeroPayment(userId, dto);
  }

  @Get('callback')
  @Public()
  async callback(@Query() query: any) {
    return await this.xeroPaymentService.callback(query);
  }

  @Get('connect')
  @AccessRoles(Roles.ADMIN)
  async connect() {
    return await this.xeroPaymentService.connect();
  }

  @Post('webhooks')
  @Public()
  async webhook(@XeroSignature() signature: string, @Body() rawBody: Buffer) {
    return await this.xeroPaymentService.webhooks(signature, rawBody);
  }

  @Post('update-history')
  async updateHistory(@Body() body: UpdateXeroPaymentHistory) {
    await this.xeroProvider.ensureValidToken();
    return await this.xeroPaymentService.updateHistory(body);
  }

  @Post('approve')
  @AccessRoles(Roles.ADMIN)
  async approveTransaction(@Body() body: ApproveRejectXeroTransactionDto) {
    await this.xeroProvider.ensureValidToken();
    return await this.xeroPaymentService.approveTransaction(body);
  }

  @Post('reject')
  async voidTransaction(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Body() body: ApproveRejectXeroTransactionDto,
  ) {
    await this.xeroProvider.ensureValidToken();
    return await this.xeroPaymentService.voidTransaction(
      Roles.ADMIN === role ? undefined : userId,
      body,
    );
  }

  @Put('asset')
  async updateAsset(
    @ActiveUser('sub') userId: string,
    @Body() body: UpdateAssetXeroPaymentDto,
  ) {
    return await this.xeroPaymentService.updateAsset(userId, body);
  }

  @Get(':id')
  async findOne(
    @ActiveUser('role') role: Roles,
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
  ) {
    return await this.xeroPaymentService.findOne(
      Roles.ADMIN === role ? undefined : userId,
      id,
    );
  }

  // @Delete(':id')
  // async delete(
  //   @Param('id') id: string,
  //   @ActiveUser('sub') userId: string,
  //   @ActiveUser('role') role: string,
  // ) {
  //   if (role === Roles.ADMIN) {
  //     return await this.xeroPaymentService.delete({ id });
  //   }
  //   return await this.xeroPaymentService.delete({ id, userId });
  // }
}
