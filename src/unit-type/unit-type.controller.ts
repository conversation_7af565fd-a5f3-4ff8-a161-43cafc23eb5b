import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { UnitTypeService } from './unit-type.service';
import { CreateUnitTypeDto } from './dto/create-unit-type.dto';
import { UpdateUnitTypeDto } from './dto/update-unit-type.dto';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { QueryUnitTypeDto } from './dto/query-unit-type.dto';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermission<PERSON>pi<PERSON>ey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('unit-type')
@CacheOption('unit-type')
export class UnitTypeController {
  constructor(private readonly unitTypeService: UnitTypeService) {}

  @Post()
  create(@Body() createUnitTypeDto: CreateUnitTypeDto) {
    return this.unitTypeService.create(createUnitTypeDto);
  }

  @Get()
  @Public()
  @UseInterceptors(CacheInterceptor)
  findAll(@Query() query: LanguageQueryDto) {
    return this.unitTypeService.getAll(query.lang);
  }

  @Get('share')
  @Public()
  @UseInterceptors(CacheInterceptor)
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_UNIT_TYPES])
  share(@Query() query: LanguageQueryDto) {
    return this.unitTypeService.getAll(query.lang);
  }

  @Get('listing')
  @UseInterceptors(CacheInterceptor)
  listing(@Query() query: QueryUnitTypeDto) {
    return this.unitTypeService.listing(query);
  }

  @Get(':id')
  getDetail(@Param('id') id: string) {
    return this.unitTypeService.getDetail(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUnitTypeDto: UpdateUnitTypeDto,
  ) {
    return this.unitTypeService.update(id, updateUnitTypeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.unitTypeService.delete(id);
  }
}
