import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { generateSlug } from 'src/core/utils/slug.util';
import { UnitType } from './entities/unit-type.entity';
import { CreateUnitTypeDto } from './dto/create-unit-type.dto';
import { UpdateUnitTypeDto } from './dto/update-unit-type.dto';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import {
  createPaginationResponse,
  getPaginationOption,
  PaginationOption,
} from 'src/core/utils/pagination.util';
import { EUnitTypeSortBy, QueryUnitTypeDto } from './dto/query-unit-type.dto';
import { EOrderType } from 'src/core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';

@Injectable()
export class UnitTypeService {
  constructor(
    @InjectRepository(UnitType)
    private readonly unitTypeRepo: Repository<UnitType>,
    private readonly redisService: RedisService,

    @InjectRepository(FloorPlan)
    private readonly floorPlanRepo: Repository<FloorPlan>,
  ) {}

  async create(body: CreateUnitTypeDto) {
    if (body.slug) {
      const existed = await this.unitTypeRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug =
      body.slug ?? (await generateSlug(body.title, this.unitTypeRepo));

    const data: Partial<UnitType> = {
      ...body,
      slug,
      title: { [ELangCode.en]: body.title },
    };

    await this.redisService.deletePattern('unit-type@*');
    return await this.unitTypeRepo.save(data);
  }

  async getDetail(id: string) {
    const item = await this.unitTypeRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Unit type is not found');
    }

    return item;
  }

  async listing(query: QueryUnitTypeDto) {
    const pagination = getPaginationOption(query);
    const orderBy = query.sortBy || EUnitTypeSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.unitTypeRepo
      .createQueryBuilder('unitType')
      .orderBy(`unitType.${orderBy}`, sorting)
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.title) {
      queryBuilder.andWhere(
        `"unitType"."title"->>'${ELangCode.en}' ILIKE :title`,
        {
          title: `%${query.title}%`,
        },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async getAll(lang = ELangCode.en) {
    return await this.unitTypeRepo
      .createQueryBuilder('unitType')
      .select('unitType.id', 'id')
      .addSelect('unitType.slug', 'slug')
      .addSelect('unitType.createdAt', 'createdAt')
      .addSelect('unitType.updatedAt', 'updatedAt')
      .addSelect('unitType.deletedAt', 'deletedAt')
      .addSelect('unitType.bedRoomCount', 'bedRoomCount')
      .addSelect('unitType.hasStudyRoom', 'hasStudyRoom')
      .addSelect('unitType.isDeluxe', 'isDeluxe')
      .addSelect('unitType.isPremium', 'isPremium')
      .addSelect('unitType.isPrivateLift', 'isPrivateLift')
      .addSelect('unitType.isCompact', 'isCompact')
      .addSelect('unitType.isCompactPlus', 'isCompactPlus')
      .addSelect('unitType.isDuplex', 'isDuplex')
      .addSelect('unitType.isPenthouse', 'isPenthouse')
      .addSelect('unitType.isUtility', 'isUtility')
      .addSelect('unitType.hasGuest', 'hasGuest')
      .addSelect('unitType.isStudio', 'isStudio')
      .addSelect(createMultilingualSelect('unitType', 'title', lang), 'title')
      .getRawMany();
  }

  async update(id: string, body: UpdateUnitTypeDto) {
    const item = await this.unitTypeRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('UnitType is not found');
    }

    const langCode = body.lang ?? ELangCode.en;

    if (body.slug && body.slug !== item.slug) {
      const existed = await this.unitTypeRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== item.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    const data: Partial<UnitType> = {
      ...item,
      ...body,
      title: {
        ...item.title,
        [langCode]: body.title ?? item.title?.[langCode],
      },
    };

    await this.redisService.deletePattern('unit-type@*');
    return await this.unitTypeRepo.save(data);
  }

  async delete(id: string) {
    const item = await this.unitTypeRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Unit type is not found');
    }

    await this.redisService.deletePattern('unit-type@*');

    return await this.unitTypeRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  formatResponse(item: UnitType, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      title: getLangValue(lang, item.title, fallback),
    };
  }

  async getBySlugs(slugs: string[]): Promise<UnitType[]> {
    try {
      return await this.unitTypeRepo.find({
        where: { slug: In(slugs) },
      });
    } catch (error) {
      console.log(error);

      return [];
    }
  }

  async countUnitType(projectId: string) {
    const floorPlans = await this.floorPlanRepo.find({
      where: { projectId },
      relations: ['unitType'],
    });

    const unitTypeNames = floorPlans.map(
      (floorPlan) =>
        floorPlan.unitType.title?.[ELangCode.en] ?? floorPlan.unitType.slug,
    );

    return [...new Set(unitTypeNames)].length;
  }

  async findByTitle(title: string) {
    return await this.redisService.cacheWrapper(
      `unit-type@title:${title}`,
      async () => {
        return await this.unitTypeRepo.findOne({
          where: {
            title: {
              en: title,
            },
          },
        });
      },
    );
  }
}
