import CrudEntity from 'src/core/entities/crud.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity()
export class UnitType extends CrudEntity {
  @Column({ type: 'jsonb' })
  title: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @Column({ type: 'smallint', default: 0 })
  bedRoomCount: number;

  @Column({ default: false })
  hasStudyRoom: boolean;

  @Column({ default: false })
  isDeluxe: boolean;

  @Column({ default: false })
  isPremium: boolean;

  @Column({ default: false })
  isPrivateLift: boolean;

  @Column({ default: false })
  isCompact: boolean;

  @Column({ default: false })
  isCompactPlus: boolean;

  @Column({ default: false })
  isDuplex: boolean;

  @Column({ default: false })
  isPenthouse: boolean;

  @Column({ default: false })
  isUtility: boolean;

  @Column({ default: false })
  hasGuest: boolean;

  @Column({ default: false })
  isStudio: boolean;

  @OneToMany(() => FloorPlan, (floorPlan) => floorPlan.unitType)
  floorPlans: FloorPlan[];

  @OneToMany(() => Unit, (unit) => unit.unitType)
  units: Unit[];

  @OneToMany(
    () => UnitTransaction,
    (unitTransaction) => unitTransaction.unitType,
  )
  transactions: UnitTransaction[];
}
