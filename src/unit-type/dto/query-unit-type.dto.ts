import { OmitType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EUnitTypeSortBy {
  title = 'title',
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  bedRoomCount = 'bedRoomCount',
}

export class QueryUnitTypeDto extends OmitType(PaginationQueryDto, ['lang']) {
  @IsString()
  @IsOptional()
  title?: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EUnitTypeSortBy)
  @IsOptional()
  sortBy?: EUnitTypeSortBy;
}
