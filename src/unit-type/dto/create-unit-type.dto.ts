import { IsBoolean, IsInt, IsOptional, IsString } from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class CreateUnitTypeDto {
  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsInt()
  bedRoomCount: number;

  @IsBoolean()
  @ParseOptionalBoolean()
  hasStudyRoom: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isDeluxe: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isPremium: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isPrivateLift: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isCompact: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isCompactPlus: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isDuplex: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isPenthouse: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  isUtility: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  hasGuest: boolean;

  @IsBoolean()
  @ParseOptionalBoolean()
  @IsOptional()
  isStudio?: boolean;
}
