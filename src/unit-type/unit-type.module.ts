import { forwardRef, Module } from '@nestjs/common';
import { UnitTypeService } from './unit-type.service';
import { UnitTypeController } from './unit-type.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UnitType } from './entities/unit-type.entity';
import { RedisModule } from 'src/redis/redis.module';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { ApiKeyModule } from 'src/api-key/api-key.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UnitType, FloorPlan]),
    RedisModule,
    forwardRef(() => ApiKeyModule),
  ],
  controllers: [UnitTypeController],
  providers: [UnitTypeService],
  exports: [UnitTypeService],
})
export class UnitTypeModule {}
