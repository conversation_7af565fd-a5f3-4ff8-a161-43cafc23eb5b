import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ELangCode } from '../../core/enums/lang.enum';
import { OmitType } from '@nestjs/mapped-types';
import { PaginationQueryDto } from '../../core/dto/pagination-query.dto';
import { EOrderType } from '../../core/enums/sort.enum';

export enum EDeveloperSortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  name = 'name',
}

export class DeveloperQueryDto {
  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsEnum(ELangCode)
  @IsOptional()
  lang?: ELangCode;
}

export class ListingDeveloperQueryDto extends OmitType(PaginationQueryDto, [
  'lang',
]) {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EDeveloperSortBy)
  @IsOptional()
  sortBy?: EDeveloperSortBy;
}
