import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateDeveloperDto } from './dto/create-developer.dto';
import { UpdateDeveloperDto } from './dto/update-developer.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Developer } from './entities/developer.entity';
import { ELangCode } from '../core/enums/lang.enum';
import { EAssetRelation } from '../asset/enums/asset.enum';
import { AssetService } from '../asset/asset.service';
import {
  DeveloperQueryDto,
  EDeveloperSortBy,
  ListingDeveloperQueryDto,
} from './dto/developer-query.dto';
import { createMultilingualSelect } from '../core/utils/sql.util';
import { beautifyObject } from '../core/utils/common.util';
import { IDeveloperRaw } from './interfaces/raw-developer.interface';
import {
  createPaginationResponse,
  getPaginationOption,
} from '../core/utils/pagination.util';
import { EOrderType } from '../core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';
import { getLangValue } from 'src/core/utils/multi-language.ulti';

@Injectable()
export class DeveloperService {
  constructor(
    @InjectRepository(Developer)
    private readonly developerRepo: Repository<Developer>,
    private readonly assetService: AssetService,
    private readonly redisService: RedisService,
  ) {}

  async create(body: CreateDeveloperDto) {
    const data: Partial<Developer> = {
      ...body,
      description: { [ELangCode.en]: body.description },
      name: { [ELangCode.en]: body.name },
    };
    const developer = await this.developerRepo.save(data);
    try {
      if (body.logoId) {
        await this.assetService.addAssetRelation(
          body.logoId,
          EAssetRelation.Developer,
          developer.id,
        );
      }
    } catch (e) {
      console.log('update developer logo failed cause', e);
    }

    await this.redisService.deletePattern('developer@*');

    return developer;
  }

  async listing(query: ListingDeveloperQueryDto) {
    const pagination = getPaginationOption(query);
    const orderBy = query.sortBy || EDeveloperSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;
    const queryBuilder = this.developerRepo
      .createQueryBuilder('developer')
      .leftJoinAndSelect('developer.logo', 'logo')
      .orderBy(`developer.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);
    if (query.name) {
      queryBuilder.andWhere(
        `"developer"."name"->>'${ELangCode.en}' ILIKE :name`,
        { name: `%${query.name}%` },
      );
    }
    const [data, total] = await queryBuilder.getManyAndCount();
    return createPaginationResponse(data, total, pagination);
  }

  async getAll(query: DeveloperQueryDto) {
    const lang = query.lang ?? ELangCode.en;
    const queryBuilder = this._createQueryBuilder(lang);
    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`developer.${key}`, order);
      });
    }
    const [rawData] = await Promise.all([queryBuilder.getRawMany()]);
    return rawData.map(this._beautifyDeveloper);
  }

  async getOne(id: string, query: DeveloperQueryDto) {
    const lang = query.lang ?? ELangCode.en;
    const queryBuilder = this._createQueryBuilder(lang);
    queryBuilder.andWhere('developer.id = :id', { id });
    const [rawData] = await Promise.all([queryBuilder.getRawOne()]);
    return this._beautifyDeveloper(rawData);
  }

  async update(id: string, body: UpdateDeveloperDto) {
    const langCode = body.lang ?? ELangCode.en;
    const developer = await this.developerRepo.findOneBy({ id });

    if (!developer) {
      throw new NotFoundException('Developer is not found');
    }

    const data: Partial<Developer> = {
      ...body,
      description: {
        ...developer.description,
        [langCode]: body.description,
      },
      name: {
        ...developer.name,
        [langCode]: body.name,
      },
    };

    if (data.logoId !== undefined && data.logoId !== developer.logoId) {
      if (developer.logoId) {
        await this.assetService.remove(developer.logoId);
      }
      if (data.logoId) {
        await this.assetService.addAssetRelation(
          data.logoId,
          EAssetRelation.Developer,
          developer.id,
        );
      }
    }

    await this.redisService.deletePattern('developer@*');

    return await this.developerRepo.save({
      ...developer,
      ...data,
    });
  }

  async delete(id: string) {
    const developer = await this.developerRepo.findOneBy({ id });

    if (!developer) {
      throw new NotFoundException('Developer is not found');
    }

    await this.redisService.deletePattern('developer@*');

    return await this.developerRepo.update({ id }, { deletedAt: new Date() });
  }

  _createQueryBuilder(lang: ELangCode) {
    return this.developerRepo
      .createQueryBuilder('developer')
      .leftJoin('developer.logo', 'logo')
      .select('developer.id', 'id')
      .addSelect(['logo.id', 'logo.urls', 'logo.type'])
      .addSelect(createMultilingualSelect('developer', 'name', lang), 'name')
      .addSelect(
        createMultilingualSelect('developer', 'description', lang),
        'description',
      );
  }

  _beautifyDeveloper(raw: IDeveloperRaw) {
    return beautifyObject(raw, ['logo']);
  }

  async findByName(name: string) {
    return await this.redisService.cacheWrapper(
      `developer@name:${name}`,
      async () => {
        return await this.developerRepo.findOne({
          where: {
            name: {
              en: name,
            },
          },
        });
      },
    );
  }

  async getOrCreateByName(name: string) {
    const location = await this.findByName(name);

    if (location) {
      return location;
    }

    return await this.create({
      name,
      description: 'Created by import projects function',
    });
  }

  formatResponse(item: Developer, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      description: getLangValue(lang, item.description, fallback),
    };
  }
}
