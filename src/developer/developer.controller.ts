import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { DeveloperService } from './developer.service';
import { CreateDeveloperDto } from './dto/create-developer.dto';
import { UpdateDeveloperDto } from './dto/update-developer.dto';
import { Public } from '../iam/authentication/decorators/auth.decorator';
import {
  DeveloperQueryDto,
  ListingDeveloperQueryDto,
} from './dto/developer-query.dto';
import { AccessRoles } from '../iam/authentication/decorators/role.decorator';
import { RolesGuard } from '../iam/authentication/guards/authentication/role.guard';
import { Roles } from '../users/entities/user.entity';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { Api<PERSON><PERSON> } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('developer')
@UseGuards(RolesGuard)
@CacheOption('developer')
export class DeveloperController {
  constructor(private readonly developerService: DeveloperService) {}

  @Post()
  @AccessRoles(Roles.ADMIN)
  create(@Body() body: CreateDeveloperDto) {
    return this.developerService.create(body);
  }

  @Get()
  @Public()
  @UseInterceptors(CacheInterceptor)
  getAll(@Query() query: DeveloperQueryDto) {
    return this.developerService.getAll(query);
  }

  @Get('listing')
  @AccessRoles(Roles.ADMIN)
  @UseInterceptors(CacheInterceptor)
  listing(@Query() query: ListingDeveloperQueryDto) {
    return this.developerService.listing(query);
  }

  @Get('share')
  @Public()
  @UseInterceptors(CacheInterceptor)
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_DEVELOPERS])
  share(@Query() query: ListingDeveloperQueryDto) {
    return this.developerService.listing(query);
  }

  @Get(':id')
  @Public()
  getOne(@Param('id') id: string, @Query() query: DeveloperQueryDto) {
    return this.developerService.getOne(id, query);
  }

  @Get('share/:id')
  @Public()
  @UseInterceptors(CacheInterceptor)
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_DEVELOPER_BY_ID])
  async shareGetOneById(
    @Param('id') id: string,
    @Query() query: DeveloperQueryDto,
  ) {
    return await this.developerService.getOne(id, query);
  }

  @Patch(':id')
  @AccessRoles(Roles.ADMIN)
  update(@Param('id') id: string, @Body() body: UpdateDeveloperDto) {
    return this.developerService.update(id, body);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  delete(@Param('id') id: string) {
    return this.developerService.delete(id);
  }
}
