import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, JoinColumn, OneToOne } from 'typeorm';

@Entity()
export class Developer extends CrudEntity {
  @Column({ type: 'jsonb' })
  name?: Record<string, string>;

  @Column({ nullable: true, type: 'jsonb' })
  description?: Record<string, string>;

  @Column({ nullable: true, type: 'uuid' })
  logoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'logoId' })
  logo?: Asset;

  @Column({ nullable: true })
  address?: string;

  @Column({ nullable: true })
  isShowAddress?: boolean;
}
