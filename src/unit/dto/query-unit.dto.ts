import { IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class QueryUnitDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  projectId?: string;

  @IsString()
  @IsOptional()
  floorPlanId?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };
}
