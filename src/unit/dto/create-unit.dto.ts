import {
  IsEnum,
  IsInt,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { EUnitStatus } from '../enums/unit.enum';

export class CreateManyUnitDto {
  @IsString()
  @IsOptional()
  name: string;

  @IsPositive()
  @IsInt()
  @IsOptional()
  count?: number;

  @IsInt()
  @IsOptional()
  floor?: number;

  @IsString()
  @IsOptional()
  block?: string;

  @IsOptional()
  price?: number;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  remarks?: string;

  @IsEnum(EUnitStatus)
  @IsOptional()
  status?: EUnitStatus;

  @IsUUID()
  projectId: string;

  @IsUUID()
  floorPlanId: string;
}
