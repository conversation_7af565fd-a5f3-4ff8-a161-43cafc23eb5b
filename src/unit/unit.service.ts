import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateManyUnitDto } from './dto/create-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Unit } from './entities/unit.entity';
import { In, Not, Repository } from 'typeorm';
import { QueryUnitDto } from './dto/query-unit.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EUnitStatus } from './enums/unit.enum';
import { FloorPlanService } from 'src/floor-plan/floor-plan.service';
import { UpsertUnitDto } from './dto/upsert-unit.dto';

@Injectable()
export class UnitService {
  constructor(
    @InjectRepository(Unit) private readonly unitRepo: Repository<Unit>,

    private readonly floorPlanService: FloorPlanService,
  ) {}

  async createMany(createUnitDto: CreateManyUnitDto) {
    const { count = 1, ...data } = createUnitDto;
    const unit = await this.unitRepo.save(new Array(count).fill(data));

    await this.floorPlanService.updateSoldAndAvailableUnits(
      createUnitDto.floorPlanId,
    );

    return unit;
  }

  async upsertUnit(upsertDto: UpsertUnitDto) {
    const { id, name, projectId, floorPlanId, price, status } = upsertDto;

    if (id) {
      const existingUnit = await this.unitRepo.findOne({
        where: { id },
        relations: ['project', 'floorPlan'],
      });

      if (!existingUnit) {
        throw new NotFoundException(`Unit with id ${id} not found`);
      }

      if (name && name !== existingUnit.name) {
        const duplicateUnit = await this.unitRepo.findOne({
          where: {
            name,
            projectId: existingUnit.project?.id,
            floorPlanId: existingUnit.floorPlan?.id,
            id: Not(id),
          },
        });

        if (duplicateUnit) {
          throw new ConflictException(
            `Another unit with the name '${name}' already exists in the same project and floor plan.`,
          );
        }
      }

      const updateData: Partial<UpsertUnitDto> = {
        ...(name && { name }),
        ...(price && { price }),
        ...(status && { status }),
      };

      await this.unitRepo.update(id, updateData);
      await this.floorPlanService.updateSoldAndAvailableUnits(
        existingUnit.floorPlan?.id,
      );

      return this.unitRepo.findOne({ where: { id } });
    } else {
      if (!projectId || !floorPlanId || !name) {
        throw new BadRequestException(
          'Project ID, Floor Plan ID, and Name are required to create a new unit.',
        );
      }

      const existingUnit = await this.unitRepo.findOne({
        where: { name, projectId, floorPlanId },
      });

      if (existingUnit) {
        throw new ConflictException(
          `A unit with the name '${name}' already exists in the same project and floor plan.`,
        );
      }

      const newUnit = this.unitRepo.create(upsertDto);
      const savedUnit = await this.unitRepo.save(newUnit);

      await this.floorPlanService.updateSoldAndAvailableUnits(floorPlanId);
      return savedUnit;
    }
  }

  async syncDuplicates() {
    const duplicates = await this.unitRepo
      .createQueryBuilder('unit')
      .select([
        'unit.name AS unit_name',
        'unit.status AS unit_status',
        'unit.projectId AS unit_projectId',
        'unit.floorPlanId AS unit_floorPlanId',
        'COUNT(*) AS duplicatecount',
      ])
      .groupBy('unit.name')
      .addGroupBy('unit.status')
      .addGroupBy('unit.projectId')
      .addGroupBy('unit.floorPlanId')
      .having('COUNT(*) > 1')
      .getRawMany();

    if (!duplicates.length) {
      console.log('No duplicates found!');
      return { message: 'No duplicates found!' };
    }

    const deletePromises = duplicates.map(async (duplicate) => {
      const { unit_name, unit_status, unit_projectid, unit_floorplanid } =
        duplicate;

      const records = await this.unitRepo
        .createQueryBuilder('unit')
        .select(['unit.id', 'unit.createdAt'])
        .where('unit.name = :name', { name: unit_name })
        .andWhere('unit.status = :status', { status: unit_status })
        .andWhere('unit.projectId = :projectId', { projectId: unit_projectid })
        .andWhere('unit.floorPlanId = :floorPlanId', {
          floorPlanId: unit_floorplanid,
        })
        .orderBy('unit.createdAt', 'ASC')
        .getMany();

      if (records.length <= 1) {
        console.log(
          `No duplicates found for group ${unit_name} with projectId ${unit_projectid}.`,
        );
        return;
      }

      const keepId = records[0].id;
      const idsToDelete = records.slice(1).map((record) => record.id);

      console.log(
        `Processing group ${unit_name} (${unit_status}, projectId: ${unit_projectid}):`,
        { keepId, idsToDelete },
      );

      if (idsToDelete.length) {
        const deleteResult = await this.unitRepo.delete({
          id: In(idsToDelete),
        });

        console.log(
          `Deleted records for group ${unit_name}:`,
          deleteResult.affected,
          `Remaining: ${idsToDelete}`,
        );
      }
    });

    await Promise.all(deletePromises);

    console.log('Sync completed!');
    return { message: 'Sync completed!', deletedGroups: duplicates.length };
  }

  async findAll(query: QueryUnitDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.unitRepo
      .createQueryBuilder('unit')
      .orderBy('unit.createdAt', 'DESC')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.projectId) {
      queryBuilder.andWhere('unit.projectId = :projectId', {
        projectId: query.projectId,
      });
    }

    if (query.floorPlanId) {
      queryBuilder.andWhere('unit.floorPlanId = :floorPlanId', {
        floorPlanId: query.floorPlanId,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('unit.status = :status', {
        status: query.status,
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`unit.${key}`, order);
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    queryBuilder.andWhere('unit.status = :status', {
      status: EUnitStatus.Available,
    });
    const availableUnitCount = await queryBuilder.getCount();

    return createPaginationResponse(data, total, pagination, {
      availableUnitCount,
    });
  }

  findOne(id: string) {
    return this.unitRepo.findOne({ where: { id } });
  }

  async update(id: string, updateUnitDto: UpdateUnitDto) {
    const item = await this.unitRepo.findOne({ where: { id } });
    if (!item) {
      throw new NotFoundException('Unit is not found');
    }

    await this.unitRepo.update(id, updateUnitDto);
    await this.floorPlanService.updateSoldAndAvailableUnits(item.floorPlanId);
    return await this.unitRepo.findOne({ where: { id } });
  }

  async remove(id: string) {
    const item = await this.unitRepo.findOne({ where: { id } });
    if (!item) {
      throw new NotFoundException('Unit is not found');
    }
    const result = await this.unitRepo.softRemove(item);
    await this.floorPlanService.updateSoldAndAvailableUnits(item.floorPlanId);

    return result;
  }

  async updateByProjectAndUnitName(
    projectId: string,
    unitName: string,
    status: EUnitStatus,
  ) {
    try {
      const exist = await this.unitRepo.findOne({
        where: {
          project: { id: projectId },
          name: unitName,
        },
      });

      if (!exist) return;

      await this.unitRepo.update({ id: exist.id }, { status });
    } catch (error) {
      console.log(error);
    }
  }
}
