import { Module } from '@nestjs/common';
import { UnitService } from './unit.service';
import { UnitController } from './unit.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Unit } from './entities/unit.entity';
import { FloorPlanModule } from 'src/floor-plan/floor-plan.module';

@Module({
  imports: [TypeOrmModule.forFeature([Unit]), FloorPlanModule],
  controllers: [UnitController],
  providers: [UnitService],
  exports: [UnitService],
})
export class UnitModule {}
