import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UnitService } from './unit.service';
import { Auth, Public } from 'src/iam/authentication/decorators/auth.decorator';
import { AuthType } from 'src/iam/authentication/enums/auth-type.enum';
import { QueryUnitDto } from './dto/query-unit.dto';
import { UpsertUnitDto } from './dto/upsert-unit.dto';

@Auth(AuthType.None)
@Controller('unit')
export class UnitController {
  constructor(private readonly unitService: UnitService) {}

  @Get()
  findAll(@Query() query: QueryUnitDto) {
    return this.unitService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.unitService.findOne(id);
  }

  @Public()
  @Patch('sync-duplicates')
  async syncDuplicates() {
    return await this.unitService.syncDuplicates();
  }

  @Patch(':id')
  async updateUnit(@Param('id') id: string, @Body() upsertDto: UpsertUnitDto) {
    return await this.unitService.upsertUnit({ ...upsertDto, id });
  }

  @Post()
  async createUnit(@Body() upsertDto: UpsertUnitDto) {
    return await this.unitService.upsertUnit(upsertDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.unitService.remove(id);
  }
}
