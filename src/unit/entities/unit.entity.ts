import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { EUnitStatus } from '../enums/unit.enum';
import { Project } from 'src/project/entities/project.entity';
import { FloorPlan } from 'src/floor-plan/entities/floor-plan.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';

@Entity()
export class Unit extends CrudEntity {
  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true, type: 'integer' })
  floor?: number;

  @Column({ nullable: true })
  block?: string;

  @Column({ nullable: true })
  price: number;

  @Column({ default: 'SGD' })
  currency: string;

  @Column({ nullable: true })
  remarks?: string;

  @Column({
    type: 'enum',
    enum: EUnitStatus,
    default: EUnitStatus.Available,
  })
  status: EUnitStatus;

  @Column({ type: 'uuid', name: 'projectId' })
  projectId: string;

  @Column({ type: 'uuid' })
  floorPlanId: string;

  @Column({ type: 'uuid', nullable: true })
  transactionId: string;

  @ManyToOne(() => Project, (project) => project.units)
  project: Project;

  @ManyToOne(() => FloorPlan, (floorPlan) => floorPlan.units)
  floorPlan: FloorPlan;

  @ManyToOne(() => UnitType, (unitType) => unitType.units, { nullable: true })
  @JoinColumn({ name: 'unitTypeId' })
  unitType: UnitType;

  @Column({ type: 'uuid', nullable: true })
  unitTypeId: string;
}
