import { OmitType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EPromotionBannerSortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  name = 'name',
}

export class ListingPromotionBannerDto extends OmitType(PaginationQueryDto, [
  'lang',
]) {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EPromotionBannerSortBy)
  @IsOptional()
  sortBy?: EPromotionBannerSortBy;
}
