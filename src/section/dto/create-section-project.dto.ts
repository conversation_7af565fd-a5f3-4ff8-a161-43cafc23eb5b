import {
  Is<PERSON>rray,
  <PERSON>Int,
  Is<PERSON>ptional,
  IsUUID,
  Max,
  ValidateNested,
} from 'class-validator';
import { Project } from 'src/project/entities/project.entity';

export class AddProjectToSectionItemDto {
  @IsUUID()
  projectId: string;

  @IsInt()
  @Max(32767)
  @IsOptional()
  weight?: number;
}

export class AddProjectToSectionBodyDto {
  @IsArray()
  @ValidateNested({ each: true })
  data: AddProjectToSectionItemDto[];
}

export class AddOrUpdateProjectSectionBodyDto {
  @IsArray()
  @ValidateNested({ each: true })
  data: Project[];
}
