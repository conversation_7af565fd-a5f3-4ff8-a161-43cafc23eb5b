import { PartialType } from '@nestjs/mapped-types';
import { ELangCode } from 'src/core/enums/lang.enum';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { CreateSectionDto } from './create-section.dto';

export class UpdateSectionDto extends PartialType(CreateSectionDto) {
  @IsOptional()
  @IsEnum(ELangCode)
  lang?: ELangCode;
}

export class ChangeWeightDto {
  @IsNotEmpty()
  @IsNumber()
  newPosition: number;

  @IsOptional()
  @IsUUID()
  beforeId?: string;
}

export class ProjectChangeWeightDto {
  @IsNotEmpty()
  @IsNumber()
  newPosition: number;

  @IsOptional()
  @IsUUID()
  beforeId?: string;
}
