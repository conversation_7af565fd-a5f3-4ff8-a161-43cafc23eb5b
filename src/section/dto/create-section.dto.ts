import {
  Is<PERSON>rray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';
import { ELayout } from '../enums/layout.enum';
import { LanguageQueryDto } from 'src/core/dto/language-query.dto';
import { BottomCtaDto } from './bottom-cta.dto';

export class CreateSectionDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(ELayout)
  layout: ELayout;

  @IsString()
  background: string;

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  visible: boolean;

  @IsInt()
  @IsOptional()
  weight: number;

  @IsOptional()
  @IsString()
  html?: string;

  @IsOptional()
  @IsUUID()
  photoId?: string;

  @IsOptional()
  @ValidateNested()
  cta?: BottomCtaDto;

  @IsOptional()
  @IsUUID()
  locationId?: string;
}

export class CreateManySectionItemDto extends CreateSectionDto {
  @IsUUID()
  @IsOptional()
  id: string;
}

export class CreateManySectionBodyDto extends LanguageQueryDto {
  @ValidateNested({ each: true })
  @IsArray()
  data: CreateManySectionItemDto[];
}
