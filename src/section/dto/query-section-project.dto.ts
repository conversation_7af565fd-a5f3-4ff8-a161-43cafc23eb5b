import { OmitType, PartialType } from '@nestjs/mapped-types';
import { CreateSectionDto } from './create-section.dto';
import { ELangCode } from 'src/core/enums/lang.enum';
import { IsEnum, IsOptional } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class QuerySectionProjectDto extends OmitType(PaginationQueryDto, [
  'lang',
]) {
  name: string;
}
