import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Headers,
} from '@nestjs/common';
import { SectionService } from './section.service';
import {
  CreateManySectionBodyDto,
  CreateSectionDto,
} from './dto/create-section.dto';
import {
  ChangeWeightDto,
  ProjectChangeWeightDto,
  UpdateSectionDto,
} from './dto/update-section.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { AddProjectToSectionBodyDto } from './dto/create-section-project.dto';
import { QuerySectionProjectDto } from './dto/query-section-project.dto';
import { RemoveSectionProject } from './dto/remove-section-project.dto';
import { ListingPromotionBannerDto } from './dto/listing-promotion-banner.dto';
import { CreateSectionPromotionBannerItemDto } from './dto/create-section-promotion-banner.dto';
import { RemoveSectionPromotionBannerDto } from './dto/remove-section-promotion-banner.dto';
import { UpdatePromotionBannerDto } from './dto/update-promotion-banner.dto';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';

@Controller('section')
export class SectionController {
  constructor(private readonly sectionService: SectionService) {}

  @Post('update-weights/all')
  async updateWeightsForAllConfigs(): Promise<void> {
    await this.sectionService.updateWeightsForAllConfigs();
    return;
  }

  @Post('reorder-projects/all')
  async reorderProjectsForAllSections(): Promise<void> {
    await this.sectionService.reorderProjectsForAllSections();
    return;
  }
  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createSectionDto: CreateSectionDto,
  ) {
    return this.sectionService.create(userId, createSectionDto);
  }

  @Post('many')
  createOrUpdateMany(
    @ActiveUser('sub') userId: string,
    @Body() body: CreateManySectionBodyDto,
  ) {
    return this.sectionService.createOrUpdateMany(userId, body);
  }

  @Get()
  findAll(
    @ActiveUser('sub') userId: string,
    @Query() query: PaginationQueryDto,
  ) {
    return this.sectionService.listing(userId, query);
  }

  @Get(':id')
  findOne(@ActiveUser('sub') userId: string, @Param('id') id: string) {
    return this.sectionService.findOneById(userId, id);
  }

  @Patch(':id/project/change-weight/:sectionProjectId')
  projectChangeWeight(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Param('sectionProjectId') sectionProjectId: string,
    @Body() body: ProjectChangeWeightDto,
  ) {
    return this.sectionService.projectChangeWeight(
      userId,
      id,
      sectionProjectId,
      body,
    );
  }

  @Patch('/change-weight/:id')
  changeWeight(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() body: ChangeWeightDto,
  ) {
    return this.sectionService.changeWeight(userId, id, body);
  }

  @Patch(':id')
  update(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() updateSectionDto: UpdateSectionDto,
  ) {
    return this.sectionService.update(userId, id, updateSectionDto);
  }

  @Delete(':id')
  remove(@ActiveUser('sub') userId: string, @Param('id') id: string) {
    return this.sectionService.remove(userId, id);
  }

  @Post(':id/projects')
  addOrUpdateProjects(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() body: AddProjectToSectionBodyDto,
  ) {
    return this.sectionService.addProjectsToSection(userId, id, body);
  }

  @Get(':id/projects')
  listingSectionProjects(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Query() query: QuerySectionProjectDto,
  ) {
    return this.sectionService.listingSectionProject(userId, id, query);
  }

  @Get(':id/projects/by-domain')
  @RequireHeader('User-Domain')
  @Public()
  listingSectionProjectsByDomain(
    @Headers('User-Domain') domain: string,
    @Param('id') id: string,
    @Query() query: QuerySectionProjectDto,
  ) {
    return this.sectionService.listingSectionProjectsByDomain(
      domain,
      id,
      query,
    );
  }

  @Delete(':id/projects')
  removeSectionProjects(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() body: RemoveSectionProject,
  ) {
    return this.sectionService.removeSectionProject(userId, id, body.projectId);
  }

  @Post(':id/promotion-banners')
  addOrUpdatePromotionBanners(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Body() body: CreateSectionPromotionBannerItemDto,
  ) {
    return this.sectionService.createPromotionBanner(userId, id, body);
  }

  @Get(':id/promotion-banners')
  listingSectionPromotionBanners(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Query() query: ListingPromotionBannerDto,
  ) {
    return this.sectionService.listingPromotionBanner(userId, id, query);
  }

  @Get(':id/promotion-banners/by-domain')
  @RequireHeader('user-domain')
  @Public()
  listingSectionPromotionBannersByDomain(
    @Headers('user-domain') domain: string,
    @Param('id') id: string,
    @Query() query: ListingPromotionBannerDto,
  ) {
    return this.sectionService.listingPromotionBannerByDomain(
      domain,
      id,
      query,
    );
  }

  @Get(':id/promotion-banners/:bannerId')
  getPromotionBannerDetail(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Param('bannerId') bannerId: string,
  ) {
    return this.sectionService.findOnePromotionBanner(userId, id, bannerId);
  }

  @Patch(':id/promotion-banners/change-weight/:bannerId')
  changeWeightSectionPromotionBanners(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Param('bannerId') bannerId: string,
    @Body() body: ChangeWeightDto,
  ) {
    return this.sectionService.changeWeightPromotionBanner(
      userId,
      id,
      bannerId,
      body,
    );
  }

  @Patch(':id/promotion-banners/:bannerId')
  updateSectionPromotionBanners(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Param('bannerId') bannerId: string,
    @Body() body: UpdatePromotionBannerDto,
  ) {
    return this.sectionService.updatePromotionBanner(
      userId,
      id,
      bannerId,
      body,
    );
  }

  @Delete(':id/promotion-banners/:bannerId')
  removeSectionPromotionBanners(
    @ActiveUser('sub') userId: string,
    @Param('id') id: string,
    @Param('bannerId') bannerId: string,
  ) {
    return this.sectionService.removeSectionPromotionBanner(
      userId,
      id,
      bannerId,
    );
  }
}
