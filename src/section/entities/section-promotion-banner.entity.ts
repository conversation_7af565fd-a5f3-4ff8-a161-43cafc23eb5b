import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Section } from 'src/section/entities/section.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class SectionPromotionBanner extends CrudEntity {
  @Column({ type: 'uuid' })
  photoId: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo: Asset;

  @Column()
  url: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'uuid' })
  sectionId: string;

  @ManyToOne(() => Section)
  @JoinColumn({ name: 'sectionId' })
  section: Section;

  @Column({ default: 1 })
  weight: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  weightTimestamp: Date;
}
