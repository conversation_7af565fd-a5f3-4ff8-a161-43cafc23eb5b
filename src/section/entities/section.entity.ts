import CrudEntity from 'src/core/entities/crud.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { ELayout } from '../enums/layout.enum';
import { Asset } from 'src/asset/entities/asset.entity';
import { BottomCtaDto } from '../dto/bottom-cta.dto';
import { Location } from 'src/location/entities/location.entity';

@Entity()
export class Section extends CrudEntity {
  @Column({
    type: 'uuid',
  })
  configId: string;

  @ManyToOne(() => UserConfig, (userConfig) => userConfig.sections)
  @JoinColumn({
    name: 'configId',
  })
  config: UserConfig;

  @Column({ default: 1, type: 'smallint' })
  weight: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  weightTimestamp: Date;

  @Column({ type: 'jsonb' })
  name: Record<string, string>;

  @Column({
    type: 'enum',
    enum: ELayout,
    default: ELayout.SectionGalleryThree,
  })
  layout: ELayout;

  @Column()
  background: string;

  @Column({ default: true })
  visible: boolean;

  @Column({ nullable: true, type: 'text' })
  html?: string;

  @Column({ nullable: true, type: 'uuid' })
  photoId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo?: Asset;

  @Column({ nullable: true, type: 'jsonb' })
  cta?: BottomCtaDto;

  @ManyToOne(() => Location, (localtion) => localtion.sections)
  @JoinColumn({
    name: 'locationId',
  })
  location: Location;
}
