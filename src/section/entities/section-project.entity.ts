import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { Section } from 'src/section/entities/section.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class SectionProject extends CrudEntity {
  @Column({ type: 'uuid' })
  sectionId: string;

  @ManyToOne(() => Section)
  @JoinColumn({ name: 'sectionId' })
  section: Section;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project)
  project: Project;

  @Column({ type: 'smallint', default: 1 })
  weight: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  weightTimestamp: Date;
}
