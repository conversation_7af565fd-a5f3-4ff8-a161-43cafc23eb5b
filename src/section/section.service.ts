import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  CreateManySectionBodyDto,
  CreateSectionDto,
} from './dto/create-section.dto';
import {
  ChangeWeightDto,
  ProjectChangeWeightDto,
  UpdateSectionDto,
} from './dto/update-section.dto';
import { Section } from './entities/section.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { In, LessThan, MoreThanOrEqual, Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { IsUUID, isUUID } from 'class-validator';
import {
  AddOrUpdateProjectSectionBodyDto,
  AddProjectToSectionBodyDto,
} from './dto/create-section-project.dto';
import { SectionProject } from './entities/section-project.entity';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { QuerySectionProjectDto } from './dto/query-section-project.dto';
import { ELayout } from './enums/layout.enum';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { RedisService } from 'src/redis/redis.service';
import { SectionPromotionBanner } from './entities/section-promotion-banner.entity';
import { CreateSectionPromotionBannerItemDto } from './dto/create-section-promotion-banner.dto';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import {
  EPromotionBannerSortBy,
  ListingPromotionBannerDto,
} from './dto/listing-promotion-banner.dto';
import { EOrderType } from 'src/core/enums/sort.enum';
import { UpdatePromotionBannerDto } from './dto/update-promotion-banner.dto';
import { LocationService } from 'src/location/location.service';
import { QueryLocationDto } from 'src/location/dto/query-location.dto';
import { ProjectService } from 'src/project/project.service';
import { PropertyService } from 'src/property/property.service';
import { Domain } from 'src/domains/entities/domain.entity';
import {
  EDynamicLayoutMode,
  UserFeature,
} from 'src/user-feature/entities/user-feature.entity';
import { EFeature } from 'src/feature/feature.enum';

@Injectable()
export class SectionService {
  constructor(
    @InjectRepository(Section)
    private readonly repo: Repository<Section>,
    @InjectRepository(SectionProject)
    private readonly sectionProjectRepo: Repository<SectionProject>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
    @InjectRepository(UserProject)
    private readonly userProject: Repository<UserProject>,
    @InjectRepository(SectionPromotionBanner)
    private readonly sectionPromotionBannerRepo: Repository<SectionPromotionBanner>,
    private readonly redisService: RedisService,
    private readonly locationService: LocationService,
    private readonly assetService: AssetService,
    private readonly projectService: ProjectService,

    @InjectRepository(Domain)
    private readonly domain: Repository<Domain>,

    @InjectRepository(UserFeature)
    private readonly userFeature: Repository<UserFeature>,
  ) {}

  async updateWeightsByConfigAndLocation(configId: string): Promise<void> {
    const sections = await this.repo
      .createQueryBuilder('section')
      .leftJoinAndSelect('section.location', 'location')
      .where('section.configId = :configId', { configId })
      .andWhere('section.weight = 0')
      .getMany();

    if (sections.length === 0) {
      console.log(
        `No sections found with weight = 0 for the given configId: ${configId}`,
      );
      return;
    }

    sections.sort((a, b) => {
      const locationNameA = a.location?.name?.['en']?.trim() || null;
      const locationNameB = b.location?.name?.['en']?.trim() || null;
      const sectionNameA = a.name?.['en']?.trim() || '';
      const sectionNameB = b.name?.['en']?.trim() || '';

      if (locationNameA === null && locationNameB !== null) {
        return 1;
      }
      if (locationNameA !== null && locationNameB === null) {
        return -1;
      }
      if (locationNameA === null && locationNameB === null) {
        return sectionNameA.localeCompare(sectionNameB);
      }

      const locationComparison = locationNameA.localeCompare(locationNameB);
      if (locationComparison !== 0) {
        return locationComparison;
      }

      return sectionNameA.localeCompare(sectionNameB);
    });

    for (let i = 0; i < sections.length; i++) {
      sections[i].weight = i + 1;
      sections[i].weightTimestamp = new Date();
    }

    await this.repo.save(sections);
  }

  async updateWeightsForAllConfigs(): Promise<void> {
    const configIds = await this.repo
      .createQueryBuilder('section')
      .select('DISTINCT section.configId', 'configId')
      .getRawMany();

    if (configIds.length === 0) {
      throw new NotFoundException('No configIds found.');
    }

    await Promise.all(
      configIds.map(({ configId }) =>
        this.updateWeightsByConfigAndLocation(configId),
      ),
    );
  }

  async reorderProjectsForAllSections(): Promise<void> {
    const sectionIds = await this.sectionProjectRepo
      .createQueryBuilder('sectionProject')
      .select('DISTINCT sectionProject.sectionId', 'sectionId')
      .getRawMany();

    if (sectionIds.length === 0) {
      throw new NotFoundException('No sections found.');
    }

    await Promise.all(
      sectionIds.map(({ sectionId }) =>
        this.reorderProjectsInSection(sectionId),
      ),
    );
  }

  async reorderProjectsInSection(sectionId: string): Promise<void> {
    const projects = await this.sectionProjectRepo.find({
      where: { section: { id: sectionId } },
      order: { weight: 'ASC', weightTimestamp: 'DESC' },
    });

    if (projects.length === 0) {
      console.log(`No projects found for sectionId: ${sectionId}`);
    }

    const weightGroups: Record<number, typeof projects> = {};
    for (const project of projects) {
      if (!weightGroups[project.weight]) {
        weightGroups[project.weight] = [];
      }
      weightGroups[project.weight].push(project);
    }

    let updatedWeight = 1;

    for (const weight in weightGroups) {
      const group = weightGroups[weight];
      if (group.length > 1) {
        group.sort(
          (a, b) => b.weightTimestamp.getTime() - a.weightTimestamp.getTime(),
        );
      }

      for (const project of group) {
        project.weight = updatedWeight++;
        project.weightTimestamp = new Date();
      }
    }

    await this.sectionProjectRepo.save(projects);
  }

  async create(userId: string, body: CreateSectionDto) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const data: Partial<Section> = {
      ...body,
      configId: userConfig.id,
      name: {
        [ELangCode.en]: body.name,
      },
      weightTimestamp: new Date(),
    };
    await this.redisService.deletePattern('section@*');

    const res = await this.repo.save(data);

    if (res.photoId) {
      await this.assetService
        .addAssetRelation(body.photoId, EAssetRelation.Section, res.id)
        .catch(async (e) => {
          await this.repo.remove(res);
          throw e;
        });
    }

    return this.findOneById(userId, res.id);
  }

  async checkIsDynamicLayoutAdvanceMode(userId: string): Promise<boolean> {
    try {
      const dynamicLayoutFreature = await this.userFeature.findOneBy({
        user: { id: userId },
        feature: { type: EFeature.DynamicLayout },
      });

      if (!dynamicLayoutFreature) return false;

      return (
        dynamicLayoutFreature.dynamicLayoutMode === EDynamicLayoutMode.ADVANCE
      );
    } catch (error) {
      console.log('error', error);

      return false;
    }
  }

  async listing(userId: string, query: PaginationQueryDto) {
    const pagination = getPaginationOption(query);

    const locationQuery: QueryLocationDto = {
      lang: ELangCode.en,
      sort: {},
    };

    const isDynamicLayoutAdvanceMode =
      await this.checkIsDynamicLayoutAdvanceMode(userId);

    const locationList = await this.locationService.getAll(locationQuery);

    const locationIds = locationList.map((location) => location.id);

    let queryBuilder;

    if (isDynamicLayoutAdvanceMode) {
      queryBuilder = this.repo
        .createQueryBuilder('section')
        .leftJoin('section.config', 'config')
        .leftJoinAndSelect('section.location', 'location')
        .leftJoinAndSelect('section.photo', 'photo')
        .where('config.userId = :userId', { userId })
        .orWhere('location.id IN (:...locationIds)', { locationIds })
        .andWhere('location.id IS NULL')
        .orderBy('section.weight', 'ASC')
        .addOrderBy('section.weightTimestamp', 'DESC')
        .take(pagination.limit)
        .skip(pagination.offset);
    } else {
      queryBuilder = this.repo
        .createQueryBuilder('section')
        .leftJoin('section.config', 'config')
        .leftJoinAndSelect('section.location', 'location')
        .leftJoinAndSelect('section.photo', 'photo')
        .where('config.userId = :userId', { userId })
        .andWhere('location.id IS NOT NULL')
        .orderBy('location.name', 'ASC')
        .take(pagination.limit)
        .skip(pagination.offset);
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(
      data.map((item, i) => {
        item['index'] = pagination.offset + i;
        return item;
      }),
      total,
      pagination,
    );
  }

  async findOneById(userId: string, id: string) {
    if (!isUUID(id)) {
      throw new BadRequestException(`'id' must be an uuid string`);
    }
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const item = await this.repo.findOne({
      where: {
        id,
        configId: userConfig.id,
      },
      relations: {
        config: true,
        photo: true,
      },
    });

    if (!item) {
      throw new NotFoundException('Section is not found');
    }

    return item;
  }

  async update(userId: string, id: string, body: UpdateSectionDto) {
    if (!isUUID(id)) {
      throw new BadRequestException(`'id' must be an uuid string`);
    }

    const item = await this.findOneById(userId, id);

    const lang = body.lang || ELangCode.en;

    const data: Partial<Section> = {
      ...item,
      ...body,
      name: {
        ...item.name,
        [lang]: body.name || item.name?.[lang],
      },
    };

    const res = await this.repo.save(data);
    try {
      if (body.photoId !== item.photoId) {
        if (item.photoId) {
          await this.assetService.remove(item.photoId);
        }
        await this.assetService.addAssetRelation(
          res.photoId,
          EAssetRelation.Section,
          res.id,
        );
      }
    } catch (e) {
      console.error(e);
    }

    return this.findOneById(userId, id);
  }

  async changeWeight(userId: string, id: string, body: ChangeWeightDto) {
    const { newPosition, beforeId } = body;

    if (!isUUID(id)) {
      throw new BadRequestException(`'id' must be an uuid string`);
    }

    await this.findOneById(userId, id);

    if (beforeId) {
      const itemBefore = await this.findOneById(userId, beforeId);
      await this.repo.update(
        {
          weight: itemBefore.weight,
          config: {
            id: itemBefore.config.id,
          },
          weightTimestamp: LessThan(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition,
        },
      );

      await this.repo.update(
        {
          weight: itemBefore.weight,
          config: {
            id: itemBefore.config.id,
          },
          weightTimestamp: MoreThanOrEqual(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition > 1 ? newPosition - 1 : newPosition,
        },
      );
    }

    return await this.repo.save({
      id,
      weight: newPosition,
      weightTimestamp: new Date(),
    });
  }

  async createOrUpdateMany(userId: string, body: CreateManySectionBodyDto) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const existedItems = await this.repo.find({
      where: {
        id: In(body.data.map((item) => item.id).filter(Boolean)),
        configId: userConfig.id,
      },
    });

    const lang = body.lang || ELangCode.en;

    const records = body.data.map((item) => {
      const existed = item.id
        ? existedItems.find((i) => i.id === item.id)
        : undefined;

      const data: Partial<Section> = {
        ...existed,
        ...item,
        id: existed?.id,
        name: {
          ...existed?.name,
          [lang]: item.name || existed?.name?.[lang],
        },
        configId: userConfig.id,
      };

      return data;
    });

    return await this.repo.save(records);
  }

  async remove(userId: string, id: string) {
    const item = await this.findOneById(userId, id);

    return await this.repo.softRemove(item);
  }

  async addDefaultSectionAndSectionProjects(userId: string) {
    const query: QueryLocationDto = {
      lang: ELangCode.en,
      sort: {},
    };
    const locationList = await this.locationService.getAll(query);
    if (locationList.length > 0) {
      locationList
        .sort((a, b) => `${a.name}`.localeCompare(b.name))
        .forEach(async (location) => {
          const body = {
            name: location.name,
            layout: ELayout.SectionGalleryThree,
            background: 'transparent',
            visible: true,
            weight: 0,
            location: { id: location.id },
          };

          const section = await this.create(userId, body);
          if (section) {
            const projects =
              await this.projectService.getDefaultProjectsByLocationId(
                location.id,
              );
            try {
              await this.addProjectArrayToUserProject(
                { data: projects },
                userId,
              );
            } catch (error) {
              console.log(error.message);
            }

            const projectIds = projects.map((item) => ({ projectId: item.id }));

            await this.addProjectsToSection(userId, section.id, {
              data: projectIds,
            });

            await this.reorderProjectsInSection(section.id);
          }
        });

      const userConfig = await this.userConfigRepo.findOne({
        where: { user: { id: userId } },
      });

      await this.updateWeightsByConfigAndLocation(userConfig.id);
    }
  }

  async addProjectArrayToUserProject(
    project: AddOrUpdateProjectSectionBodyDto,
    userId: string,
  ) {
    const projects = project.data.map((item) => ({
      projectId: item.id,
      userId,
      promotion: item.promotion,
      featured: item.featured,
      upcomingLaunch: item.upcomingLaunch,
      weight: 1,
    }));

    const existedProjects = await this.userProject.find({
      where: {
        projectId: In(projects.map((i) => i.projectId)),
        userId,
      },
    });

    const notExistedProjects = projects.filter((item) => {
      return !existedProjects.some(
        (existedProject) => existedProject.projectId === item.projectId,
      );
    });

    if (notExistedProjects.length === 0) {
      throw new UnprocessableEntityException('All projects are existed');
    }

    return await this.userProject.insert(notExistedProjects);
  }

  async addProjectsToSection(
    userId: string,
    sectionId: string,
    body: AddProjectToSectionBodyDto,
  ) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const section = await this.repo.findOneBy({
      id: sectionId,
      // configId: userConfig.id,
    });

    if (!section) {
      throw new NotFoundException('Section is not found');
    }

    if (section.layout === ELayout.SectionHTMLContent) {
      throw new UnprocessableEntityException(
        `Unable to add project to a section with layout '${ELayout.SectionHTMLContent}'`,
      );
    }

    const projectIds = body.data.map((i) => i.projectId);

    const userProjects = await this.userProject.find({
      where: {
        projectId: In(projectIds),
        userId: userId,
      },
    });

    if (body.data.length !== userProjects.length) {
      throw new UnprocessableEntityException(
        'Have one or many projects are not in your site',
      );
    }

    const existed = await this.sectionProjectRepo.find({
      where: {
        sectionId,
        projectId: In(projectIds),
      },
    });

    const records = body.data.map((item) => {
      const existedItem = existed.find((i) => i.projectId === item.projectId);
      const data: Partial<SectionProject> = {
        ...existedItem,
        ...item,
        sectionId,
      };

      return data;
    });

    return await this.sectionProjectRepo.save(records);
  }

  async projectChangeWeight(
    userId: string,
    id: string,
    sectionProjectId: string,
    body: ProjectChangeWeightDto,
  ) {
    const { newPosition, beforeId } = body;

    if (!isUUID(id)) {
      throw new BadRequestException(`'id' must be an uuid string`);
    }

    if (!isUUID(sectionProjectId)) {
      throw new BadRequestException(
        `'sectionProjectId' must be an uuid string`,
      );
    }

    await this.findOneById(userId, id);

    const item = await this.sectionProjectRepo.findOne({
      where: {
        id: sectionProjectId,
      },
    });

    if (!item) {
      throw new NotFoundException('Section project is not found');
    }

    if (beforeId) {
      const itemBefore = await this.sectionProjectRepo.findOne({
        where: {
          id: beforeId,
        },
      });

      await this.sectionProjectRepo.update(
        {
          weight: itemBefore.weight,
          section: {
            id,
          },
          weightTimestamp: LessThan(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition,
        },
      );

      await this.sectionProjectRepo.update(
        {
          weight: itemBefore.weight,
          section: {
            id,
          },
          weightTimestamp: MoreThanOrEqual(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition > 1 ? newPosition - 1 : newPosition,
        },
      );
    }

    return await this.sectionProjectRepo.save({
      id: sectionProjectId,
      section: {
        id,
      },
      weight: newPosition,
      weightTimestamp: new Date(),
    });
  }

  async removeSectionProject(
    userId: string,
    sectionId: string,
    projectId: string,
  ) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const section = await this.repo.findOneBy({
      id: sectionId,
      configId: userConfig.id,
    });

    if (!section) {
      throw new NotFoundException('Section is not found');
    }

    if (section.layout === ELayout.SectionHTMLContent) {
      throw new UnprocessableEntityException(
        `Unable to remove project from a section with layout '${ELayout.SectionHTMLContent}'`,
      );
    }

    const sectionProject = await this.sectionProjectRepo.findOneBy({
      sectionId,
      projectId,
    });

    if (!sectionProject) {
      throw new UnprocessableEntityException('Project is not in this section');
    }

    return await this.sectionProjectRepo.remove(sectionProject);
  }

  async listingSectionProject(
    userId: string,
    sectionId: string,
    query: QuerySectionProjectDto,
  ) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const section = await this.repo.findOneBy({
      id: sectionId,
      configId: userConfig.id,
    });

    if (!section) {
      throw new NotFoundException('Section is not found');
    }

    const pagination = getPaginationOption(query);

    const queryBuilder = this.sectionProjectRepo
      .createQueryBuilder('sectionProject')
      .leftJoinAndSelect('sectionProject.project', 'project')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoinAndSelect('project.developer', 'developer')
      .leftJoinAndSelect('project.category', 'category')
      .where('sectionProject.sectionId = :sectionId', { sectionId })
      .orderBy('sectionProject.weight', 'ASC')
      .addOrderBy('sectionProject.weightTimestamp', 'DESC')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.name) {
      queryBuilder.andWhere(
        `"project"."name"->>'${ELangCode.en}' ILIKE :name`,
        { name: `%${query.name}%` },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async listingSectionProjectsByDomain(
    domainName: string,
    sectionId: string,
    query: QuerySectionProjectDto,
  ) {
    const domain = await this.domain.findOneBy({ name: domainName });

    if (!domain) {
      throw new UnprocessableEntityException('Domain name not brian');
    }

    const userConfig = await this.userConfigRepo.findOneBy({
      id: domain.configId,
    });

    if (!userConfig) {
      throw new UnprocessableEntityException(
        'You must create UserConfig first',
      );
    }

    const section = await this.repo.findOneBy({
      id: sectionId,
      configId: userConfig.id,
    });

    if (!section) {
      throw new NotFoundException('Section is not found');
    }

    const pagination = getPaginationOption(query);

    const queryBuilder = this.sectionProjectRepo
      .createQueryBuilder('sectionProject')
      .leftJoinAndSelect('sectionProject.project', 'project')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('project.photo', 'photo')
      .leftJoinAndSelect('project.developer', 'developer')
      .leftJoinAndSelect('project.category', 'category')
      .where('sectionProject.sectionId = :sectionId', { sectionId })
      .orderBy('sectionProject.weight', 'ASC')
      .addOrderBy('sectionProject.weightTimestamp', 'DESC')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.name) {
      queryBuilder.andWhere(
        `"project"."name"->>'${ELangCode.en}' ILIKE :name`,
        { name: `%${query.name}%` },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async createPromotionBanner(
    userId: string,
    sectionId: string,
    body: CreateSectionPromotionBannerItemDto,
  ) {
    if (!isUUID(sectionId)) {
      throw new BadRequestException('sectionId should be an uuid format');
    }

    const userConfig = await this.userConfigRepo.findOneBy({ userId });
    if (!userConfig) {
      throw new UnprocessableEntityException(
        'Have to create user config first',
      );
    }

    const section = await this.findOneById(userId, sectionId);

    if (section.layout !== ELayout.SectionPromotionBanner) {
      throw new BadRequestException(
        `Section layout must be '${ELayout.SectionPromotionBanner}'`,
      );
    }

    const res = await this.sectionPromotionBannerRepo.save({
      ...body,
      sectionId: sectionId,
    });

    await this.assetService
      .addAssetRelation(body.photoId, EAssetRelation.PromotionBanner, res.id)
      .catch(async (e) => {
        await this.sectionPromotionBannerRepo.remove(res);
        throw e;
      });

    return res;
  }

  async findOnePromotionBanner(
    userId: string,
    sectionId: string,
    bannerId: string,
  ) {
    const section = await this.findOneById(userId, sectionId);

    if (section.layout !== ELayout.SectionPromotionBanner) {
      throw new BadRequestException(
        `Section layout must be '${ELayout.SectionPromotionBanner}'`,
      );
    }

    const banner = await this.sectionPromotionBannerRepo.findOneBy({
      sectionId: section.id,
      id: bannerId,
    });

    if (!banner) {
      throw new NotFoundException('Promotion banner is not found');
    }

    return banner;
  }

  async listingPromotionBanner(
    userId: string,
    sectionId: string,
    query: ListingPromotionBannerDto,
  ) {
    const section = await this.findOneById(userId, sectionId);

    if (section.layout !== ELayout.SectionPromotionBanner) {
      throw new BadRequestException(
        `Section layout must be '${ELayout.SectionPromotionBanner}'`,
      );
    }

    const orderBy = query.sortBy || EPromotionBannerSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const pagination = getPaginationOption(query);

    const queryBuilder = this.sectionPromotionBannerRepo
      .createQueryBuilder('promotionBanner')
      .leftJoinAndSelect('promotionBanner.photo', 'photo')
      .where('promotionBanner.sectionId = :sectionId', {
        sectionId: section.id,
      })
      .orderBy(`promotionBanner.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.name) {
      queryBuilder.andWhere('promotionBanner.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async listingPromotionBannerByDomain(
    domain: string,
    sectionId: string,
    query: ListingPromotionBannerDto,
  ) {
    const userConfig = await this.userConfigRepo
      .createQueryBuilder('userConfig')
      .leftJoin('userConfig.domains', 'domains')
      .where('domains.name = :domain', { domain })
      .getOne();

    if (!userConfig) {
      throw new BadRequestException('Domain not found');
    }

    return this.listingPromotionBanner(userConfig.userId, sectionId, query);
  }

  async updatePromotionBanner(
    userId: string,
    sectionId: string,
    bannerId: string,
    body: UpdatePromotionBannerDto,
  ) {
    const item = await this.findOnePromotionBanner(userId, sectionId, bannerId);

    const res = await this.sectionPromotionBannerRepo.save({
      ...item,
      ...body,
    });

    try {
      if (body.photoId !== item.photoId) {
        if (item.photoId) {
          await this.assetService.remove(item.photoId);
        }
        await this.assetService.addAssetRelation(
          res.photoId,
          EAssetRelation.PromotionBanner,
          res.id,
        );
      }
    } catch {}

    return res;
  }

  async changeWeightPromotionBanner(
    userId: string,
    sectionId: string,
    bannerId: string,
    body: ChangeWeightDto,
  ) {
    const { newPosition, beforeId } = body;

    if (!isUUID(bannerId)) {
      throw new BadRequestException(
        `'sectionProjectId' must be an uuid string`,
      );
    }

    const section = await this.findOneById(userId, sectionId);

    if (section.layout !== ELayout.SectionPromotionBanner) {
      throw new BadRequestException(
        `Section layout must be '${ELayout.SectionPromotionBanner}'`,
      );
    }

    if (beforeId) {
      const itemBefore = await this.sectionProjectRepo.findOne({
        where: {
          id: beforeId,
        },
      });

      await this.sectionProjectRepo.update(
        {
          weight: itemBefore.weight,
          section: {
            id: section.id,
          },
          weightTimestamp: LessThan(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition,
        },
      );

      await this.sectionProjectRepo.update(
        {
          weight: itemBefore.weight,
          section: {
            id: section.id,
          },
          weightTimestamp: MoreThanOrEqual(itemBefore.weightTimestamp),
        },
        {
          weight: newPosition > 1 ? newPosition - 1 : newPosition,
        },
      );
    }

    return await this.sectionProjectRepo.save({
      id: bannerId,
      section: {
        id: bannerId,
      },
      weight: newPosition,
      weightTimestamp: new Date(),
    });
  }

  async removeSectionPromotionBanner(
    userId: string,
    sectionId: string,
    bannerId: string,
  ) {
    const banner = await this.findOnePromotionBanner(
      userId,
      sectionId,
      bannerId,
    );

    await this.assetService.removeAllByRelatedId(bannerId);
    return await this.sectionPromotionBannerRepo.softRemove(banner);
  }

  formatResponse(item: Section, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
    };
  }
}
