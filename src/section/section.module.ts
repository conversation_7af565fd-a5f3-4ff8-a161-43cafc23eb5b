import { Module } from '@nestjs/common';
import { SectionService } from './section.service';
import { SectionController } from './section.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Section } from './entities/section.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { SectionProject } from './entities/section-project.entity';
import { UserProject } from 'src/user-project/entities/user-project.entity';
import { RedisModule } from 'src/redis/redis.module';
import { SectionPromotionBanner } from './entities/section-promotion-banner.entity';
import { AssetModule } from 'src/asset/asset.module';
import { LocationModule } from 'src/location/location.module';
import { ProjectModule } from 'src/project/project.module';
import { Domain } from 'src/domains/entities/domain.entity';
import { UserFeature } from 'src/user-feature/entities/user-feature.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Section,
      SectionProject,
      UserProject,
      UserConfig,
      SectionPromotionBanner,
      Domain,
      UserFeature,
    ]),
    RedisModule,
    AssetModule,
    LocationModule,
    ProjectModule,
  ],
  controllers: [SectionController],
  providers: [SectionService],
  exports: [SectionService],
})
export class SectionModule {}
