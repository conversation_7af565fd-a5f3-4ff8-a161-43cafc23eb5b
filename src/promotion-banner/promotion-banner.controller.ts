import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Headers,
  UseInterceptors,
} from '@nestjs/common';
import { PromotionBannerService } from './promotion-banner.service';
import { CreatePromotionBannerDto } from './dto/create-promotion-banner.dto';
import { UpdatePromotionBannerDto } from './dto/update-promotion-banner.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { ListingPromotionBannerDto } from './dto/listing-promotion-banner.dto';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';

@Controller('promotion-banner')
@CacheOption('promotion-banner')
export class PromotionBannerController {
  constructor(
    private readonly promotionBannerService: PromotionBannerService,
  ) {}

  @Post()
  create(
    @ActiveUser('sub') userId: string,
    @Body() createPromotionBannerDto: CreatePromotionBannerDto,
  ) {
    return this.promotionBannerService.create(userId, createPromotionBannerDto);
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  listing(
    @ActiveUser('sub') userId: string,
    @Query() query: ListingPromotionBannerDto,
  ) {
    return this.promotionBannerService.findAll(userId, query);
  }

  @Get('/by-domain')
  @RequireHeader('User-Domain')
  @Public()
  @UseInterceptors(CacheInterceptor)
  getByDomain(
    @Headers('User-Domain') domain: string,

    @Query() query: ListingPromotionBannerDto,
  ) {
    return this.promotionBannerService.listingByDomain(domain, query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.promotionBannerService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updatePromotionBannerDto: UpdatePromotionBannerDto,
  ) {
    return this.promotionBannerService.update(id, updatePromotionBannerDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.promotionBannerService.remove(id);
  }
}
