import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { CreatePromotionBannerDto } from './dto/create-promotion-banner.dto';
import { UpdatePromotionBannerDto } from './dto/update-promotion-banner.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { PromotionBanner } from './entities/promotion-banner.entity';
import { Repository } from 'typeorm';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import {
  EPromotionBannerSortBy,
  ListingPromotionBannerDto,
} from './dto/listing-promotion-banner.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { AssetService } from 'src/asset/asset.service';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { EOrderType } from 'src/core/enums/sort.enum';
import { RedisService } from 'src/redis/redis.service';

@Injectable()
export class PromotionBannerService {
  constructor(
    @InjectRepository(PromotionBanner)
    private readonly promotionBannerRepo: Repository<PromotionBanner>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    private readonly assetService: AssetService,
    private readonly redisService: RedisService,
  ) {}

  async create(
    userId: string,
    createPromotionBannerDto: CreatePromotionBannerDto,
  ) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });
    if (!userConfig) {
      throw new UnprocessableEntityException(
        'Have to create user config first',
      );
    }

    const res = await this.promotionBannerRepo.save({
      ...createPromotionBannerDto,
      configId: userConfig.id,
    });

    await this.assetService
      .addAssetRelation(
        createPromotionBannerDto.photoId,
        EAssetRelation.PromotionBanner,
        res.id,
      )
      .catch(async (e) => {
        await this.promotionBannerRepo.remove(res);
        throw e;
      });

    await this.redisService.deletePattern('promotion-banner*');
    return res;
  }

  async findAll(userId: string, query: ListingPromotionBannerDto) {
    const userConfig = await this.userConfigRepo.findOneBy({ userId });
    if (!userConfig) {
      throw new UnprocessableEntityException(
        'Have to create user config first',
      );
    }
    const orderBy = query.sortBy || EPromotionBannerSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const pagination = getPaginationOption(query);

    const queryBuilder = this.promotionBannerRepo
      .createQueryBuilder('promotionBanner')
      .leftJoinAndSelect('promotionBanner.photo', 'photo')
      .where('promotionBanner.configId = :configId', {
        configId: userConfig.id,
      })
      .orderBy(`promotionBanner.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.name) {
      queryBuilder.andWhere('promotionBanner.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async listingByDomain(domain: string, query: ListingPromotionBannerDto) {
    const userConfig = await this.userConfigRepo
      .createQueryBuilder('userConfig')
      .leftJoin('userConfig.domains', 'domains')
      .where('domains.name = :domain', { domain })
      .getOne();

    if (!userConfig) {
      throw new BadRequestException('Domain not found');
    }
    const pagination = getPaginationOption(query);

    const queryBuilder = this.promotionBannerRepo
      .createQueryBuilder('promotionBanner')
      .leftJoinAndSelect('promotionBanner.photo', 'photo')
      .where('promotionBanner.configId = :configId', {
        configId: userConfig.id,
      })
      .orderBy('promotionBanner.createdAt', 'DESC')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (query.name) {
      queryBuilder.andWhere('promotionBanner.name ILIKE :name', {
        name: `%${query.name}%`,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async findOne(id: string) {
    const item = await this.promotionBannerRepo.findOne({
      where: { id },
      relations: { photo: true },
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    return item;
  }

  async update(id: string, updatePromotionBannerDto: UpdatePromotionBannerDto) {
    const item = await this.findOne(id);

    const res = await this.promotionBannerRepo.save({
      ...item,
      ...updatePromotionBannerDto,
    });

    try {
      if (updatePromotionBannerDto.photoId !== item.photoId) {
        if (item.photoId) {
          await this.assetService.remove(item.photoId);
        }
        await this.assetService.addAssetRelation(
          res.photoId,
          EAssetRelation.PromotionBanner,
          res.id,
        );
      }
    } catch {}

    await this.redisService.deletePattern('promotion-banner*');
    return res;
  }

  async remove(id: string) {
    const item = await this.findOne(id);
    await this.assetService.removeAllByRelatedId(id);
    await this.redisService.deletePattern('promotion-banner*');
    return await this.promotionBannerRepo.softRemove(item);
  }
}
