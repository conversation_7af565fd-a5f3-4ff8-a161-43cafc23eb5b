import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class PromotionBanner extends CrudEntity {
  @Column({ type: 'uuid' })
  photoId: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo: Asset;

  @Column()
  url: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'uuid' })
  configId: string;

  @ManyToOne(() => UserConfig)
  config: UserConfig;
}
