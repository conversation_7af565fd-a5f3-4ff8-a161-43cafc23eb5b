import { Modu<PERSON> } from '@nestjs/common';
import { PromotionBannerService } from './promotion-banner.service';
import { PromotionBannerController } from './promotion-banner.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PromotionBanner } from './entities/promotion-banner.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { AssetModule } from 'src/asset/asset.module';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PromotionBanner, UserConfig]),
    AssetModule,
    RedisModule,
  ],
  controllers: [PromotionBannerController],
  providers: [PromotionBannerService],
})
export class PromotionBannerModule {}
