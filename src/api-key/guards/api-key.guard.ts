import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { ApiKeyService } from '../api-key.service';
import { Reflector } from '@nestjs/core';
import { API_KEY_METADATA } from '../decorators/api-key.decorator';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly apiKeyService: ApiKeyService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const permissionsMetadata =
      this.reflector.get<string[]>(API_KEY_METADATA, context.getHandler()) ||
      [];

    const request = context.switchToHttp().getRequest();
    const apiKeyEncode = request.headers['x-api-key'];

    if (!apiKeyEncode) {
      throw new ForbiddenException('Missing API key headers');
    }

    const apiKeyData = await this.apiKeyService.getApiKey(apiKeyEncode);
    if (!apiKeyData) {
      throw new UnauthorizedException('Invalid API key');
    }

    if (
      !(apiKeyData.permissions || []).find((permission) =>
        permissionsMetadata.includes(permission),
      )
    ) {
      throw new ForbiddenException('Permission denied');
    }
    if (apiKeyData?.noLimitIp === false) {
      // Check ipAddress
      const clientIP = request.ip || request?.connection?.remoteAddress;
      const allowedIPs = apiKeyData.allowedIPs || [];
      const matched = allowedIPs.find((ipOrRange) => ipOrRange === clientIP);
      if (!matched) {
        throw new ForbiddenException('IP address not allowed');
      }
    }

    // Attach the validated key to the request if needed
    request.apiKey = apiKeyData;

    return true;
  }
}
