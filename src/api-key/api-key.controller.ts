import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiKeyService } from './api-key.service';
import { QueryApiKeysDto } from './dto/query-api-keys.entity';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { CreateApiKeyDto } from './dto/create-api-key.entity';
import { UpdateApiKeyDto } from './dto/update-api-key.entity';

@Controller('api-key')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class ApiKeyController {
  constructor(private readonly apiKeyService: ApiKeyService) {}

  @Get()
  async getAll(@Query() dto: QueryApiKeysDto) {
    return await this.apiKeyService.getAll(dto);
  }

  @Get(':id')
  async getOne(@Param('id') id: string) {
    return await this.apiKeyService.getOne({ id });
  }

  @Post()
  async create(@Body() dto: CreateApiKeyDto) {
    return await this.apiKeyService.create(dto);
  }

  @Patch(':id')
  async updateById(@Param('id') id: string, @Body() dto: UpdateApiKeyDto) {
    return await this.apiKeyService.update({ id }, dto);
  }

  @Delete(':id')
  async deleteById(@Param('id') id: string) {
    return await this.apiKeyService.delete({ id });
  }
}
