import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiKey } from './entities/api-key.entity';
import { ApiKeyService } from './api-key.service';
import { HashPasswordFactory } from 'src/core/decorators/hash-password.decorator';
import { ModuleRef } from '@nestjs/core';
import { ApiKeyController } from './api-key.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ApiKey])],
  providers: [
    ApiKeyService,
    {
      provide: 'HashPassword',
      useFactory: HashPasswordFactory,
      inject: [ModuleRef],
    },
  ],
  controllers: [ApiKeyController],
  exports: [ApiKeyService],
})
export class ApiKeyModule {}
