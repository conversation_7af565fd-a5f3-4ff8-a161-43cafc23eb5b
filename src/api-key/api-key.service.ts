import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Api<PERSON>ey } from './entities/api-key.entity';
import { FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { QueryApiKeysDto } from './dto/query-api-keys.entity';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { CreateApiKeyDto } from './dto/create-api-key.entity';
import { HashingService } from 'src/iam/hashing/hashing.service';
import { UpdateApiKeyDto } from './dto/update-api-key.entity';

@Injectable()
export class ApiKeyService {
  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    private readonly configService: ConfigService,
    private readonly hashService: HashingService,
  ) {}

  async getAll(dto: QueryApiKeysDto) {
    const { filter, sort, search } = dto;
    const pagination = getPaginationOption(dto);

    const queryBuilder = this.apiKeyRepository
      .createQueryBuilder('apiKey')
      .skip(pagination.offset)
      .take(pagination.limit);
    if (filter) {
      if (filter.name)
        queryBuilder.andWhere('apiKey.name = :name', { name: filter.name });
    }

    if (search) {
      queryBuilder.andWhere(`apiKey.name LIKE :search`, {
        search: `%${search}%`,
      });
    }

    if (sort) {
      Object.entries(sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`apiKey.${key}`, order);
      });
    }

    const [apiKeys, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(apiKeys, total, pagination);
  }

  async getOne(query: Partial<ApiKey>) {
    const { id } = query;
    const where: FindOptionsWhere<ApiKey> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const result = await this.apiKeyRepository.findOneBy(where);
    if (!result) {
      throw new NotFoundException('ApiKey not found');
    }
    return result;
  }

  async getApiKey(apiKeyEncode: string) {
    try {
      const apiKeyJSON = this.hashService.decrypt(apiKeyEncode);
      if (!apiKeyJSON) {
        throw new NotFoundException('ApiKey not found');
      }

      const apiKeyParse: { id: string; apiKey: string } =
        JSON.parse(apiKeyJSON);

      const where: FindOptionsWhere<ApiKey> = {
        deletedAt: IsNull(),
        id: apiKeyParse.id,
      };

      const result = await this.apiKeyRepository.findOneBy(where);
      if (!result) {
        throw new NotFoundException('ApiKey not found');
      }

      const apiKeyIsValid = await this.hashService.compare(
        apiKeyParse.apiKey,
        result.apiKey,
      );

      if (!apiKeyIsValid) {
        throw new BadRequestException('ApiKey is incorrectly');
      }
      return result;
    } catch (error) {
      console.log('Error', error);
      throw new UnauthorizedException('Invalid API key');
    }
  }

  async create(dto: CreateApiKeyDto) {
    const randomKey = this.generateRandomKey(20);
    const apiKeyHash = await this.hashService.hash(randomKey);
    const result = await this.apiKeyRepository.save({
      ...dto,
      apiKey: apiKeyHash,
    });
    const apiKeyEncode = this.hashService.encrypt(
      JSON.stringify({ id: result.id, apiKey: randomKey }),
    );
    return { ...result, apiKey: apiKeyEncode };
  }

  async update(query: Partial<ApiKey>, dto: UpdateApiKeyDto) {
    const { id } = query;
    const where: FindOptionsWhere<ApiKey> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }

    const result = await this.apiKeyRepository.update(where, dto);
    return result;
  }

  async delete(query: Partial<ApiKey>) {
    const { id } = query;
    const where: FindOptionsWhere<ApiKey> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    const apiKeyData = await this.apiKeyRepository.findOneBy(where);
    if (!apiKeyData) {
      throw new NotFoundException('ApiKey not found');
    }

    const result = await this.apiKeyRepository.softDelete(where);
    return result;
  }

  generateRandomKey(length: number = 10): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}
