import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity } from 'typeorm';
import { EPermissionApiKey } from '../enums/permission-api-key.enum';

@Entity()
export class ApiKey extends CrudEntity {
  @Column()
  name: string;

  @Column()
  apiKey: string;

  @Column({ type: 'enum', enum: EPermissionApiKey, array: true })
  permissions: EPermissionApiKey[];

  @Column({ type: 'varchar', array: true })
  allowedIPs: string[];

  @Column({ nullable: true })
  noLimitIp?: boolean;
}
