import {
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Api<PERSON><PERSON> } from '../entities/api-key.entity';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { Type } from 'class-transformer';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';

class ApiKeyFilter implements Partial<Pick<ApiKey, 'name'>> {
  @IsOptional()
  @IsString()
  name?: string;
}

export class QueryApiKeysDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => ApiKeyFilter)
  filter?: ApiKeyFilter;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
