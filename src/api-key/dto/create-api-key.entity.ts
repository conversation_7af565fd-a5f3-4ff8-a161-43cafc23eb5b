import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsIP,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { EPermissionApiKey } from '../enums/permission-api-key.enum';

export class CreateApiKeyDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEnum(EPermissionApiKey, { each: true })
  permissions: EPermissionApiKey[];

  @IsNotEmpty()
  @IsIP('4', { each: true })
  allowedIPs: string[];

  @IsOptional()
  @IsBoolean()
  noLimitIp?: boolean;
}
