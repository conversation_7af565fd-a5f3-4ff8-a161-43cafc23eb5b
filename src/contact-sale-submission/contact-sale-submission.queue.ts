import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment-timezone';
import { formatSgPhoneNumber } from 'src/core/utils/common.util';
import { WebhookService } from 'src/core/webhook/webhook.service';
import { Project } from 'src/project/entities/project.entity';
import { TENURE_ITEM } from 'src/project/enums/tenure.enum';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { User } from 'src/users/entities/user.entity';
import { Repository } from 'typeorm';
import appConfig from '../core/configs/app.config';
import { EMAIL_TEMPLATE } from '../mailer/enums/mailer.enum';
import { MailerService } from '../mailer/mailer.service';
import { CreateContactSaleSubmissionDto } from './dto/create-contact-sale-submission.dto';
import { ContactSaleSubmission } from './entities/contact-sale-submission.entity';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { IResult } from 'ua-parser-js';
import { ContactSaleSubmissionService } from './contact-sale-submission.service';
import { LoggerAppService } from 'src/logger-app/logger-app.service';

@Processor('contact_sale_submission_queue')
export class ContactSaleSubmissionQueue {
  constructor(
    @InjectRepository(ContactSaleSubmission)
    private readonly repo: Repository<ContactSaleSubmission>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
    private readonly mailerService: MailerService,
    private readonly webhooksService: WebhookService,
    private readonly contactSaleSubmissionService: ContactSaleSubmissionService,
    private readonly loggerAppService: LoggerAppService,
  ) {}

  @Process({
    name: 'handleCreateFromQueue',
    concurrency: 1,
  })
  async handleCreateFromQueue(
    job: Job<{
      userDomain: string;
      body: CreateContactSaleSubmissionDto;
      ua?: IResult;
      queueId: string;
    }>,
  ) {
    const { data } = job;
    const { queueId, body, userDomain, ua } = data;
    this.loggerAppService.logger.info(
      `Queue ${queueId} create submission step 3: Handle in queue`,
    );
    await this.contactSaleSubmissionService.handleCreateFromQueue(data);
  }
}
