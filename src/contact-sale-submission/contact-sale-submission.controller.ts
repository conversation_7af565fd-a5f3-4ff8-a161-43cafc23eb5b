import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { ActiveUser } from '../iam/authentication/decorators/active-user.decorator';
import { AccessRoles } from '../iam/authentication/decorators/role.decorator';
import { RolesGuard } from '../iam/authentication/guards/authentication/role.guard';
import { Roles } from '../users/entities/user.entity';
import { ContactSaleSubmissionService } from './contact-sale-submission.service';
import {
  ContactSaleActionLogQueryDto,
  ContactSubmissionQueryDto,
} from './dto/contact-sale-submission.query.dto';
import { CreateContactSaleActionLogDto } from './dto/create-contact-sale-action-log.dto';
import { CreateContactSaleSubmissionDto } from './dto/create-contact-sale-submission.dto';
import { UpdateContactSaleSubmissionDto } from './dto/update-contact-sale-submission.dto';
import { ContactSaleSubmission } from './entities/contact-sale-submission.entity';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';
import { HCaptchaAppGuard } from 'src/hcaptcha-app/guards/hcaptcha-app.guard';
import { UseHCaptchaApp } from 'src/hcaptcha-app/decorators/hcaptcha-app.decorator';

@Controller('contact-sale-submission')
@UseGuards(RolesGuard, HCaptchaAppGuard)
export class ContactSaleSubmissionController {
  constructor(
    private readonly contactSaleSubmissionService: ContactSaleSubmissionService,
  ) {}

  @Post()
  @Public()
  @RequireHeader('user-domain')
  @UseHCaptchaApp()
  async create(
    @Headers('user-domain') domain: string,
    @Body() createContactSaleSubmissionDto: CreateContactSaleSubmissionDto,
    @Req() req: Request,
  ) {
    return await this.contactSaleSubmissionService.create(
      domain,
      createContactSaleSubmissionDto,
      req,
    );
  }

  @Post('share')
  @Public()
  @RequireHeader('user-domain')
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.POST_LEAD])
  share(
    @Headers('user-domain') domain: string,
    @Body() createContactSaleSubmissionDto: CreateContactSaleSubmissionDto,
    @Req() req: Request,
  ) {
    createContactSaleSubmissionDto.tracking =
      createContactSaleSubmissionDto.tracking || '{}';
    return this.contactSaleSubmissionService.create(
      domain,
      createContactSaleSubmissionDto,
      req,
    );
  }

  @Post('get-cea-details')
  @Public()
  getCeaDetails(@Body('phoneNumber') phoneNumber: string) {
    return this.contactSaleSubmissionService.getCEADetail(phoneNumber);
  }

  @Get()
  // @AccessRoles(Roles.ADMIN)
  findAll(
    @Query() query: ContactSubmissionQueryDto,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: string,
  ) {
    return this.contactSaleSubmissionService.findAll(query, role, userId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateContactSaleSubmissionDto: UpdateContactSaleSubmissionDto,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: Roles,
  ) {
    if (role !== Roles.ADMIN) {
      updateContactSaleSubmissionDto.adminNote = undefined;
    }
    return this.contactSaleSubmissionService.update(
      id,
      updateContactSaleSubmissionDto,
      userId,
    );
  }

  @Get('by-agency')
  @AccessRoles(Roles.AGENCY)
  findAllByAgency(
    @Query() query: ContactSubmissionQueryDto,
    @ActiveUser('sub') userId: string,
  ) {
    return this.contactSaleSubmissionService.findAllByAgency(userId, query);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  delete(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: string,
    @Req() req: Request,
  ) {
    return this.contactSaleSubmissionService.delete(userId, role, id, req);
  }

  // LOGS
  @Get('/logs/:id') // Log submission action user
  findAllLogs(@Param('id') id: string) {
    return this.contactSaleSubmissionService.getSubmissionLog(id);
  }

  @Get('/action-logs')
  findAllActionLogs(@Query() query: ContactSaleActionLogQueryDto) {
    // If submissionId is not provided, return all action logs
    return this.contactSaleSubmissionService.findAllActionLogsBySubmission({
      query,
    });
  }

  @Post('/action-logs/:submissionId')
  createActionLog(
    @Param('submissionId') submissionId: string,
    @Body()
    {
      actionTrackingType,
    }: Omit<CreateContactSaleActionLogDto, 'submissionId' | 'userId'>,
    @ActiveUser('sub') userId: string,
  ) {
    return this.contactSaleSubmissionService.createActionLog({
      submissionId,
      userId,
      actionTrackingType,
    });
  }
}
