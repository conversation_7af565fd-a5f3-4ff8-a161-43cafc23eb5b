import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/users/entities/user.entity';
import { Project } from 'src/project/entities/project.entity';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { ContactSaleSubmissionService } from './contact-sale-submission.service';
import { ContactSaleSubmissionController } from './contact-sale-submission.controller';
import { ContactSaleSubmission } from './entities/contact-sale-submission.entity';
import { MailerModule } from '../mailer/mailer.module';
import { AnalyticModule } from 'src/analytic/analytic.module';
import { WebhookModule } from 'src/core/webhook/webhook.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { EmailTemplate } from 'src/email-template/entities/email-template.entity';
import { IpTrackingModule } from 'src/ip-tracking/ip-tracking.module';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { ContactSaleSubmissionLog } from './entities/contact-sale-submission-log.entity';
import { ProjectModule } from 'src/project/project.module';
import { ContactSaleActionLog } from './entities/contact-sale-submission-action-log.entity';
import { ApiKeyModule } from 'src/api-key/api-key.module';
import { BullModule } from '@nestjs/bull';
import { ContactSaleSubmissionQueue } from './contact-sale-submission.queue';
import { LoggerAppModule } from 'src/logger-app/logger-app.module';
import { HCaptchaAppModule } from 'src/hcaptcha-app/hcaptcha-app.module';
import { EmailTemplateModule } from 'src/email-template/email-template.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      ContactSaleSubmission,
      Project,
      UserConfig,
      EmailTemplate,
      IpTracking,
      ContactSaleSubmissionLog,
      ContactSaleActionLog,
    ]),
    ProjectModule,
    UnitTypeModule,
    MailerModule,
    AnalyticModule,
    WebhookModule,
    IpTrackingModule,
    forwardRef(() => ApiKeyModule),
    BullModule.registerQueue({
      name: 'contact_sale_submission_queue',
    }),
    forwardRef(() => LoggerAppModule),
    forwardRef(() => HCaptchaAppModule),
    forwardRef(() => EmailTemplateModule),
  ],
  controllers: [ContactSaleSubmissionController],
  providers: [ContactSaleSubmissionService, ContactSaleSubmissionQueue],
  exports: [ContactSaleSubmissionService],
})
export class ContactSaleSubmissionModule {}
