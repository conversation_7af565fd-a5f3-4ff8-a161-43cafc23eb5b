import {
  <PERSON><PERSON>num,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  <PERSON>S<PERSON>,
  IsUUI<PERSON>,
} from 'class-validator';
import { EActionTrackingType } from '../entities/contact-sale-submission-action-log.entity';

export class CreateContactSaleActionLogDto {
  @IsEnum(EActionTrackingType)
  @IsNotEmpty()
  actionTrackingType: EActionTrackingType;

  @IsUUID()
  @IsNotEmpty()
  submissionId: string;

  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsOptional()
  content?: string;
}
