import {
  IsEmail,
  IsEnum,
  IsIP,
  IsMobilePhone,
  IsNotEmpty,
  IsOptional,
  IsString,
  Validate,
} from 'class-validator';
import {
  IsJsonArrayString,
  IsJsonParsableAsObject,
} from 'src/core/utils/validator.util';
import { ESubmitSubmissionType } from '../enums/submit-submission-type.enum';
import { SanitizeHtml } from 'src/common/decorators/sanitize-html.decorator';

export class CreateContactSaleSubmissionDto {
  @IsNotEmpty()
  @IsString()
  projectSlug: string;

  @IsOptional()
  @IsString()
  @IsJsonArrayString({ message: 'interestedIn must be a JSON array string' })
  interestedIn?: string;

  @IsNotEmpty()
  @IsString()
  @IsMobilePhone()
  phone: string;

  @IsOptional()
  @IsString()
  @SanitizeHtml()
  name?: string;

  @IsOptional()
  @IsString()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  appointment?: string;

  @IsOptional()
  @IsString()
  @IsJsonArrayString({ message: 'unitTypes must be a JSON array string' })
  unitTypes?: string;

  @IsIP()
  @IsOptional()
  ip?: string;

  @IsString()
  @IsOptional()
  browser?: string;

  @IsString()
  @IsOptional()
  @IsJsonParsableAsObject({ message: 'tracking must be a JSON array string' })
  tracking?: string;

  @IsString()
  @IsOptional()
  landingPageDomain?: string;

  @IsOptional()
  @IsEnum(ESubmitSubmissionType)
  submitFrom?: ESubmitSubmissionType;
}
