import { Type } from 'class-transformer';
import {
  IsDate,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class ContactSubmissionQueryDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  search?: string;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @IsOptional()
  fromScore?: number;

  @IsOptional()
  toScore?: number;

  @IsOptional()
  userId?: string;
}

export class ContactSaleActionLogQueryDto extends PaginationQueryDto {
  @IsUUID()
  @IsOptional()
  submissionId?: string;

  @IsOptional()
  search?: string;

  @IsOptional()
  sort: { [key: string]: 'ASC' | 'DESC' };
}
