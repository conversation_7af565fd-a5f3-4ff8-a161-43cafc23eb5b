export interface ICEAResponse<T> {
  status: number;
  data: T;
  totalItems?: number;
  pageNumber?: number;
  totalPages?: number;
}

export interface ICEAItem {
  id: string;
  name: string;
  businessName: string;
  licenseNumber: string;
  validityDateStart: string;
  validityDateEnd: string;
  awards: null | any[];
  disciplinaryActions: null | any[];
  registrationNumber: string;
  photoUrl: string | null;
  currentEa: string;
}
