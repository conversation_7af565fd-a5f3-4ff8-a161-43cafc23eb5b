import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { isUUID } from 'class-validator';
import { Request } from 'express';
import * as geoip from 'geoip-lite';
import * as moment from 'moment-timezone';
import { AnalyticService } from 'src/analytic/analytic.service';
import { ECountryAlpha2Code } from 'src/core/enums/countryFlags.enum';
import { ELangCode } from 'src/core/enums/lang.enum';
import { formatSgPhoneNumber } from 'src/core/utils/common.util';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { WebhookService } from 'src/core/webhook/webhook.service';
import { EmailTemplate } from 'src/email-template/entities/email-template.entity';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { Project } from 'src/project/entities/project.entity';
import { TENURE_ITEM } from 'src/project/enums/tenure.enum';
import { ProjectService } from 'src/project/project.service';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Roles, User } from 'src/users/entities/user.entity';
import { Repository } from 'typeorm';
import { UAParser, IResult } from 'ua-parser-js';
import appConfig from '../core/configs/app.config';
import { EMAIL_TEMPLATE } from '../mailer/enums/mailer.enum';
import { MailerService } from '../mailer/mailer.service';
import {
  ContactSaleActionLogQueryDto,
  ContactSubmissionQueryDto,
} from './dto/contact-sale-submission.query.dto';
import { CreateContactSaleActionLogDto } from './dto/create-contact-sale-action-log.dto';
import { CreateContactSaleSubmissionDto } from './dto/create-contact-sale-submission.dto';
import { UpdateContactSaleSubmissionDto } from './dto/update-contact-sale-submission.dto';
import {
  ContactSaleActionLog,
  EActionTrackingType,
} from './entities/contact-sale-submission-action-log.entity';
import {
  ContactSaleSubmissionLog,
  EContactSaleSubmissionType,
} from './entities/contact-sale-submission-log.entity';
import {
  ContactSaleSubmission,
  EContactStatus,
} from './entities/contact-sale-submission.entity';
import { ICEAItem, ICEAResponse } from './interfaces/cea.interface';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { LoggerAppService } from 'src/logger-app/logger-app.service';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { EmailTemplateService } from 'src/email-template/email-template.service';

export const MAPPING_TEXT_CONTACT_STATUS = {
  [EContactStatus.NEW]: 'New',
  [EContactStatus.OPEN]: 'Open',
  [EContactStatus.IN_PROGRESS]: 'In Progress',
  [EContactStatus.UNQUALIFIED]: 'Unqualified',
  [EContactStatus.ATTEMPTED_TO_CONTACT]: 'Attempted to Contact',
  [EContactStatus.CONNECTED]: 'Connected',
};

@Injectable()
export class ContactSaleSubmissionService {
  constructor(
    @InjectRepository(ContactSaleSubmission)
    private readonly repo: Repository<ContactSaleSubmission>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,
    @InjectRepository(IpTracking)
    private readonly ipTrackingRepo: Repository<IpTracking>,
    @InjectRepository(EmailTemplate)
    private readonly emailTemplateRepo: Repository<EmailTemplate>,
    @InjectRepository(ContactSaleSubmissionLog)
    private readonly contactSaleSubmissionLogRepo: Repository<ContactSaleSubmissionLog>,
    @InjectRepository(ContactSaleActionLog)
    private readonly contactSaleActionLogRepo: Repository<ContactSaleActionLog>,
    @InjectQueue('contact_sale_submission_queue')
    private contactSaleSubmissionQueue: Queue,

    private readonly webhooksService: WebhookService,
    private readonly unitTypeService: UnitTypeService,
    private readonly mailerService: MailerService,
    private readonly analyticService: AnalyticService,
    private readonly projectService: ProjectService,
    private readonly loggerAppService: LoggerAppService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  async create(
    userDomain: string,
    body: CreateContactSaleSubmissionDto,
    req: Request,
  ) {
    const ua = new UAParser(req.headers['user-agent']);
    const queueId = uuidv4();
    this.loggerAppService.logger.info(
      `Queue ${queueId} create submission step 1: ${JSON.stringify({
        body,
        userDomain,
        ua: ua.getResult(),
      })}`,
    );
    this.contactSaleSubmissionQueue.add('handleCreateFromQueue', {
      body,
      userDomain,
      ua: ua.getResult(),
      queueId,
    });
    this.loggerAppService.logger.info(
      `Queue ${queueId} create submission step 2: Response message`,
    );

    return { message: 'Data was in queue' };
  }

  async handleCreateFromQueue(data: {
    userDomain: string;
    body: CreateContactSaleSubmissionDto;
    ua?: IResult;
    queueId: string;
  }): Promise<ContactSaleSubmission> {
    const { body, userDomain, ua, queueId } = data;

    try {
      this.loggerAppService.logger.info(
        `Queue ${queueId} create submission step 4: ${JSON.stringify({ body, userDomain, ua })}`,
      );
      // Run initial queries in parallel
      const [user, ceaInfo] = await Promise.all([
        this.userRepo.findOne({
          where: {
            config: {
              domains: { name: userDomain },
            },
          },
        }),
        this.getCEADetail(body.phone),
      ]);

      if (!user) {
        throw new BadRequestException('User not found');
      }
      this.loggerAppService.logger.info(
        `Queue ${queueId} create submission step 5: ${JSON.stringify({ user, ceaInfo })}}`,
      );
      // Parse and validate data early
      const parsedUnitTypes = JSON.parse(body.unitTypes);
      const parsedInterestedIn = JSON.parse(body.interestedIn);

      // Run project and unit type queries in parallel
      const [project, unitTypes, userConfiguration] = await Promise.all([
        body.projectSlug
          ? this.projectRepo.findOne({
              where: { slug: body.projectSlug },
              relations: ['location', 'category'],
            })
          : null,
        this.unitTypeService.getBySlugs(parsedUnitTypes),
        this.userConfigRepo.findOne({
          where: { userId: user.id },
        }),
      ]);

      if (body.projectSlug && !project) {
        throw new BadRequestException('Project not found');
      }

      // Get project stats if needed
      const totalUnitTypes = project
        ? (await this.projectService.countUnitsStatistic(project.id))
            .totalUnitsCount
        : 0;

      // Process user agent and IP info
      const ipAddress = Array.isArray(body.ip) ? body.ip[0] : body.ip;
      const geo = geoip.lookup(ipAddress);
      const agencyEmails = [user.email, ...(user.additionalEmail ?? [])];

      // Process unit types
      const unitTypeIds = unitTypes
        .map((item) => (typeof item === 'string' ? undefined : item.id))
        .filter(Boolean);

      const unitTypeTitles = unitTypes.map((item) =>
        typeof item === 'string'
          ? item
          : item.title?.[ELangCode.en] ?? item.slug,
      );

      // Form submitted from ldp
      const isSubmittedFromLdp = !!body?.landingPageDomain;

      // Calculate score components in parallel
      const [
        appointment,
        projectViewed,
        interested,
        unitTypePoint,
        projectViewDuration,
        redFlag,
      ] = await Promise.all([
        this.getAppointmentPoints(body.appointment),
        this.getProjectViewedPoints(body.ip, isSubmittedFromLdp),
        Promise.resolve(this.getInterestedPoints(body.interestedIn)),
        Promise.resolve(this.getUnitTypePoints(unitTypeTitles)),
        Promise.resolve(this.getProjectViewDurationPoints(body.tracking)),
        this.checkIsAgentSubmitOrDupplicate(
          agencyEmails,
          body.email,
          ipAddress,
          ECountryAlpha2Code[geo?.country] || geo?.country,
          body.browser || ua?.browser?.name,
          {
            model: ua?.device?.model,
            type: ua?.device?.type,
            vendor: ua?.device?.vendor,
          },
          body.phone,
        ),
      ]);

      // Calculate final score
      let score = 0;

      if (!redFlag.result && !ceaInfo) {
        score =
          appointment.score +
          projectViewed.score +
          interested.score +
          unitTypePoint.score +
          projectViewDuration.score;
      }

      // Domain submitted from
      const clientLink = isSubmittedFromLdp
        ? body?.landingPageDomain
        : `${userDomain}/projects/${project.slug}`;

      // Create submission entity
      const contactSaleSubmission = this.repo.create({
        name: body.name,
        email: body.email,
        phone: body.phone,
        appointment: moment(body.appointment).format('MMMM DD, YYYY hh:mm A'),
        interestedIn: body.interestedIn,
        unitTypes: JSON.stringify(unitTypeTitles.join(', ')),
        user,
        project,
        browser: body.browser || ua?.browser?.name,
        ip: ipAddress,
        tracking: body.tracking,
        ceaInfo: ceaInfo,
        country: ECountryAlpha2Code[geo?.country] || geo?.country,
        device: {
          model: ua?.device?.model,
          type: ua?.device?.type,
          vendor: ua?.device?.vendor,
        },
        score,
        submittedFromDomain: clientLink,
        isSubmittedFromLdp: isSubmittedFromLdp,
      });

      const date = moment().startOf('date').toDate();

      // Save submission and logs in parallel
      const savedResult = await this.repo.save(contactSaleSubmission);

      this.loggerAppService.logger.info(
        `Queue ${queueId} create submission step 6`,
      );

      const logs = this.createSubmissionLogs(
        contactSaleSubmission,
        appointment,
        projectViewed,
        interested,
        unitTypePoint,
        projectViewDuration,
        redFlag,
        !!ceaInfo,
      );

      // Create submission logs
      await this.contactSaleSubmissionLogRepo.save(logs);

      await Promise.all([
        // Increment analytics in parallel
        ...(project
          ? [
              this.analyticService.incrementProjectEntriesCount(
                project.id,
                savedResult.id,
                date,
              ),
            ]
          : []),
        ...unitTypeIds.map((id) =>
          this.analyticService.incrementUnitTypeEntriesCount(
            id,
            savedResult.id,
            date,
          ),
        ),
      ]);
      this.loggerAppService.logger.info(
        `Queue ${queueId} create submission step 7: Send mail...`,
      );
      if (score > 0 && !agencyEmails.includes(body.email)) {
        await this.sendMails({
          body,
          clientLink,
          project,
          user,
          userConfiguration,
          agencyEmails,
          unitTypeTitles,
          contactSaleSubmission,
          parsedInterestedIn,
          totalUnitTypes,
          userDomain,
        });
      }

      this.loggerAppService.logger.info(
        `Queue ${queueId} create submission step 8: Create completed with contact sale submission ${savedResult.id}`,
      );

      return savedResult;
    } catch (error) {
      console.log(error);
      this.loggerAppService.logger.error(
        `Queue ${queueId} create submission error: ${JSON.stringify({
          response: error?.response,
          status: error?.status,
          message: error?.message,
          stack: error?.stack,
        })}`,
      );
      throw new BadRequestException(error.message);
    }
  }

  async sendMails(data: {
    body: CreateContactSaleSubmissionDto;
    clientLink: string;
    project: Project;
    user: User;
    userConfiguration: UserConfig;
    agencyEmails: string[];
    unitTypeTitles: string[];
    parsedInterestedIn: any;
    totalUnitTypes: number;
    contactSaleSubmission: ContactSaleSubmission;
    userDomain: string;
  }) {
    try {
      const {
        body,
        clientLink,
        project,
        user,
        userConfiguration,
        agencyEmails,
        unitTypeTitles,
        contactSaleSubmission,
        parsedInterestedIn,
        totalUnitTypes,
        userDomain,
      } = data;

      // Prepare email data
      const submissionDate = moment()
        .utcOffset(8)
        .format('HH:mm on MMM DD, YYYY');
      const whatsappNumber = user.whatsapp ?? userConfiguration?.whatsapp;

      const adminLink = project
        ? `${appConfig().adminLink}/sign-in?projectId=${project.id}&projectSlug=${project.slug}`
        : null;

      const emailTemplateParams = {
        name: body.name,
        projectName: project ? project.name?.en : 'our project',
        website: project ? clientLink : '',
        agentWhatsapp:
          formatSgPhoneNumber(whatsappNumber) || 'our contact number',
        saleTeamInfo: userConfiguration?.salesTeamInfo?.name || 'Sales Team',
        domain: userDomain,
      };

      const welcomeToProjectTemplate =
        await this.emailTemplateService.findFirst({
          name: EMAIL_TEMPLATE.WELCOME_TO_PROJECT,
        });

      // Send welcome email to user
      if (welcomeToProjectTemplate) {
        await this.mailerService.sendHtmlStringMail(
          {
            subject: welcomeToProjectTemplate.subject,
            text: '',
            to: body.email,
          },
          welcomeToProjectTemplate.body,
          { ...emailTemplateParams },
        );
      } else {
        await this.mailerService.sendMail(
          {
            to: body.email,
            subject: 'Welcome to Project SG',
            html: EMAIL_TEMPLATE.WELCOME_TO_PROJECT,
          },
          { ...emailTemplateParams },
        );
      }

      // Send notification emails to agents
      await Promise.all(
        agencyEmails.map((email) =>
          this.mailerService.sendMail(
            {
              to: email,
              subject: project
                ? `New Lead from ${project.name?.en ?? '-'}`
                : 'New Lead from [Contact Us]',
              html: project
                ? EMAIL_TEMPLATE.CONTACT_SALE_SUBMISSION
                : EMAIL_TEMPLATE.CONTACT_US,
            },
            project
              ? {
                  fullName: body.name,
                  email: body.email,
                  phone: formatSgPhoneNumber(body.phone),
                  submissionDate,
                  appointmentDateTime: '-',
                  appointment: moment(body.appointment).format(
                    'MMMM DD, YYYY hh:mm A',
                  ),
                  unitType: unitTypeTitles.join(', ') || 'N/A',
                  interestedIn: parsedInterestedIn,
                  projectName: project.name?.en ?? '-',
                  projectLocation: project
                    ? [project.location].map((loc) => loc?.name.en)
                    : '-',
                  projectCategory:
                    project && project.category && [project.category].length > 0
                      ? [project.category]
                          .map((cat) => cat?.name.en)
                          .join(', ') || '-'
                      : '-',
                  projectUnits:
                    project.manualTotalUnitsCount !== 0
                      ? project.manualTotalUnitsCount
                      : totalUnitTypes || '-',
                  projectTenure: project
                    ? TENURE_ITEM[project.tenure]?.name || '-'
                    : '-',
                  clientLink,
                  adminLink,
                  hasCeaInfo: !!contactSaleSubmission.ceaInfo,
                  ceaName: contactSaleSubmission.ceaInfo?.name,
                  ceaRegNo: contactSaleSubmission.ceaInfo?.registrationNumber,
                  ceaAgency: contactSaleSubmission.ceaInfo?.currentEa,
                  agentName: user.firstName + ' ' + user.lastName,
                  whatsappUrl: `https://api.whatsapp.com/send?phone=${formatSgPhoneNumber(
                    body.phone,
                  ).replace(/\D/g, '')}`,
                  currentEa: userConfiguration?.salesTeamInfo?.currentEa,
                }
              : {
                  fullName: body.name,
                  email: body.email,
                  phone: formatSgPhoneNumber(body.phone),
                  submissionDate,
                  appointmentDateTime: '-',
                  appointment: moment(body.appointment).format(
                    'MMMM DD, YYYY hh:mm A',
                  ),
                  unitType: unitTypeTitles.join(', ') || '-',
                  interestedIn: parsedInterestedIn,
                  hasCeaInfo: !!contactSaleSubmission.ceaInfo,
                  ceaName: contactSaleSubmission.ceaInfo?.name,
                  ceaRegNo: contactSaleSubmission.ceaInfo?.registrationNumber,
                  ceaAgency: contactSaleSubmission.ceaInfo?.currentEa,
                },
            project ? 'mjml' : undefined,
          ),
        ),
      );

      // Trigger webhook in background
      this.webhooksService.trigger(user, body, clientLink, user.phone, project);
    } catch (error) {
      console.error('Error sending emails:', error);
    }
  }

  async update(
    id: string,
    body: UpdateContactSaleSubmissionDto,
    userId: string,
  ) {
    const contactSaleSubmission = await this.repo.findOneBy({ id });

    if (!contactSaleSubmission) {
      throw new NotFoundException('Contact sale submission not found');
    }

    await this.repo.update(id, body);

    if (
      body?.contactStatus &&
      body?.contactStatus !== contactSaleSubmission?.contactStatus
    ) {
      const contentTracking = `Contact status updated from ${MAPPING_TEXT_CONTACT_STATUS[contactSaleSubmission?.contactStatus]} to ${MAPPING_TEXT_CONTACT_STATUS[body?.contactStatus]}`;

      await this.createActionLog({
        submissionId: id,
        userId,
        actionTrackingType: EActionTrackingType.UPDATE_CONTACT_STATUS,
        content: contentTracking,
      });
    }

    return {
      ...contactSaleSubmission,
      ...body,
    };
  }

  async findAll(
    query: ContactSubmissionQueryDto,
    role?: string,
    userId?: string,
  ) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.repo
      .createQueryBuilder('contactSubmissionSale')
      .leftJoinAndSelect('contactSubmissionSale.project', 'project')
      .leftJoinAndSelect('contactSubmissionSale.user', 'user')
      .leftJoinAndSelect('contactSubmissionSale.assignedTo', 'assignedTo')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (role == Roles.USER) {
      queryBuilder.andWhere('assignedTo.id = :assignedToId', {
        assignedToId: userId,
      });
    }

    if (query.search) {
      queryBuilder.andWhere(
        '(contactSubmissionSale.name ILIKE :search OR contactSubmissionSale.phone ILIKE :search OR contactSubmissionSale.email ILIKE :search OR ' +
          "(project.name ->> 'en') ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    if (query.startDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt >= :startDate', {
        startDate: query.startDate.toISOString(),
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt <= :endDate', {
        endDate: query.endDate.toISOString(),
      });
    }

    if (query.fromScore) {
      queryBuilder.andWhere('contactSubmissionSale.score >= :fromScore', {
        fromScore: query.fromScore,
      });
    }

    if (query.toScore) {
      queryBuilder.andWhere('contactSubmissionSale.score <= :toScore', {
        toScore: query.toScore,
      });
    }

    if (query.userId) {
      queryBuilder.andWhere('contactSubmissionSale.user.id = :userId', {
        userId: query.userId,
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        if (key === 'project') {
          queryBuilder.addOrderBy(`project.name ->> 'en'`, order);
        } else {
          queryBuilder.addOrderBy(`contactSubmissionSale.${key}`, order);
        }
      });
    }

    const [rawData, total] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(rawData, total, pagination);
  }

  async findAllByAgency(userId: string, query: ContactSubmissionQueryDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.repo
      .createQueryBuilder('contactSubmissionSale')
      .leftJoinAndSelect('contactSubmissionSale.project', 'project')
      .leftJoinAndSelect('contactSubmissionSale.user', 'user')
      .where('user.id = :userId', { userId })
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        '(contactSubmissionSale.name ILIKE :search OR contactSubmissionSale.phone ILIKE :search OR contactSubmissionSale.email ILIKE :search OR ' +
          "(project.name ->> 'en') ILIKE :search)",
        {
          search: `%${query.search}%`,
        },
      );
    }

    if (query.startDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('contactSubmissionSale.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        if (key === 'project') {
          queryBuilder.addOrderBy(`project.name ->> 'en'`, order);
        } else {
          queryBuilder.addOrderBy(`contactSubmissionSale.${key}`, order);
        }
      });
    }

    const [rawData, total] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(rawData, total, pagination);
  }

  async delete(userId: string, role: string, id: string, req: Request) {
    try {
      const ip =
        req.headers['cf-connecting-ip'] ||
        req.headers['x-real-ip'] ||
        req.headers['x-forwarded-for'] ||
        req.socket.remoteAddress ||
        '';

      const queryBuilder = this.repo
        .createQueryBuilder('contactSubmissionSale')
        .leftJoinAndSelect('contactSubmissionSale.project', 'project')
        .leftJoinAndSelect('contactSubmissionSale.user', 'user')
        .where('contactSubmissionSale.id = :id', { id });

      if (role !== Roles.ADMIN) {
        queryBuilder.andWhere('user.id = :userId', { userId });
      }

      const contactSubmission = await queryBuilder.getOne();
      if (!contactSubmission) {
        throw new NotFoundException('Contact submission not found');
      }
      await this.analyticService.deleteProjectEntriesCount(id);
      await this.analyticService.deleteUnitTypeEntriesCount(id);

      return await this.repo.update(
        { id },
        { deletedAt: new Date(), deletedByIP: ip.toString() },
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getCEADetail(phoneNumber?: string) {
    if (!phoneNumber) return undefined;
    try {
      const headers: RawAxiosRequestHeaders = {
        'Content-Type': 'application/json;charset=UTF-8',
        Origin: 'https://www.cea.gov.sg',
        Referer: 'https://www.cea.gov.sg/aceas/public-register/sales/',
      };

      const standardPhone = this.standardCeaPhone(phoneNumber);

      const { data: listRes } = await axios.request<ICEAResponse<ICEAItem[]>>({
        url: 'https://www.cea.gov.sg/aceas/api/internet/profile/v2/public-register/filter',
        headers: headers,
        method: 'POST',
        data: {
          page: 1,
          pageSize: 10,
          sortAscFlag: true,
          contactNumber: standardPhone,
          sort: 'name',
          profileType: 2,
        },
      });

      const id = listRes?.data?.[0]?.id;

      if (id) {
        const { data } = await axios.request<ICEAResponse<ICEAItem>>({
          url: 'https://www.cea.gov.sg/aceas/api/internet/profile/v2/public-register/getById',
          headers: headers,
          method: 'POST',
          data: {
            id,
            profileType: 2,
          },
        });

        return data?.data;
      }
    } catch (e) {
      console.log('CEA ERROR', e);
      return undefined;
    }
  }

  async getSubmissionLog(submissionId: string) {
    if (!isUUID(submissionId)) {
      throw new BadRequestException('submissionId must be an uuid');
    }

    const submissionWithLogs = await this.repo.findOne({
      where: { id: submissionId },
      relations: ['submissionLogs'],
      select: ['id', 'name', 'email', 'phone', 'score', 'createdAt'],
    });

    if (!submissionWithLogs) {
      throw new BadRequestException('Submission not found');
    }

    return submissionWithLogs;
  }

  standardCeaPhone(phone: string) {
    phone = phone.replace(/\s+/g, '');
    if (phone.length == 8) {
      return phone;
    }

    if (phone.startsWith('0')) {
      return phone.substring(1);
    }

    if (phone.startsWith('+65')) {
      return phone.substring('+65'.length);
    }

    return phone;
  }

  getAppointmentPoints(appointmentDate: string | null): {
    score: number;
    reason: string;
  } {
    if (!appointmentDate || !moment(appointmentDate).isValid()) {
      return { score: 0, reason: 'Not Given' };
    }

    // const currentDate = moment();
    // const appointmentMoment = moment(appointmentDate);

    // const differenceInDays = appointmentMoment
    //   .clone()
    //   .startOf('day')
    //   .diff(currentDate.clone().startOf('day'), 'days');

    let score = 0;
    const timeZoneSGT = 'Asia/Singapore';
    const currentDateSGT = moment().utc().tz(timeZoneSGT);
    const appointmentMoment = moment(appointmentDate);
    const differenceInDays = appointmentMoment
      .clone()
      .startOf('day')
      .diff(currentDateSGT.clone().startOf('day'), 'days');
    const reason = `Submit date: ${currentDateSGT.format('MMMM DD, YYYY hh:mm A')} - Appointment date: ${appointmentMoment.format('MMMM DD, YYYY hh:mm A')}`;

    if (differenceInDays <= 3) {
      score = 20;
    } else if (differenceInDays <= 7) {
      score = 15;
    } else if (differenceInDays <= 14) {
      score = 10;
    } else {
      score = 5;
    }

    return { score, reason };
  }

  async getProjectViewedPoints(
    ip: string,
    isFromLdp?: boolean,
  ): Promise<{ score: number; reason: string }> {
    const DEFAULT_POINT = 5;

    if (isFromLdp) {
      return {
        score: DEFAULT_POINT,
        reason: `Total count of unique viewed projects: 1`,
      };
    }
    // Only count when user submitting lead form from the directory page, if they submit from the LDP => count should be = 1
    const query = this.ipTrackingRepo
      .createQueryBuilder('ipTracking')
      .select('COUNT(DISTINCT ipTracking.projectId)', 'distinctCount')
      .where(
        'ipTracking.ip = :ip AND ipTracking.projectLink LIKE :slugProjectDetail',
        {
          ip,
          slugProjectDetail: '%/projects/%', // Just count project detail page, not count landing page
        },
      );

    const { distinctCount } = await query.getRawOne();

    let score = 0;
    let reason = `Total count of unique viewed projects: ${distinctCount}`;
    score = distinctCount > 1 ? 15 : DEFAULT_POINT;

    return { score, reason };
  }

  getInterestedPoints(interestedIn: string): { score: number; reason: string } {
    const totalOptions = 3;
    const interests: string[] = JSON.parse(interestedIn);
    const uncheckedCount = totalOptions - interests.length;

    let score = 0;
    let reason = `Checked interests: ${interests.join(', ')}`;

    if (uncheckedCount >= 3) {
      score = 20;
    } else if (uncheckedCount >= 1 && uncheckedCount <= 2) {
      score = 15;
    } else {
      score = 5;
    }

    return { score, reason };
  }

  getUnitTypePoints(unitTypes: string[]): { score: number; reason: string } {
    const count = unitTypes.length;
    let score = 0;
    let reason = `Unit types selected: ${unitTypes.join(', ')}`;

    if (count === 0) {
      return { score: 0, reason: 'No unit types selected' };
    }

    if (count > 2) {
      score = 20;
    } else if (count === 2) {
      score = 15;
    } else {
      score = 5;
    }

    return { score, reason };
  }

  getProjectViewDurationPoints(tracking: string): {
    score: number;
    reason: string;
  } {
    try {
      const trackingJson = JSON.parse(tracking);
      if (
        !trackingJson ||
        typeof trackingJson !== 'object' ||
        !trackingJson.hasOwnProperty('startTime')
      ) {
        return { score: 0, reason: 'Invalid data' };
      }

      const startTime = moment(trackingJson.startTime).utcOffset(8);
      if (!startTime.isValid()) {
        return { score: 0, reason: 'Invalid data' };
      }

      const now = moment().utcOffset(8);
      const diffInMinutes = now.diff(startTime, 'minutes');

      let score = 0;
      let reason = `View duration: ${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''}`;

      if (diffInMinutes > 10) {
        score = 15;
      } else if (diffInMinutes > 5) {
        score = 10;
      } else if (diffInMinutes > 2) {
        score = 5;
      }

      return { score, reason };
    } catch (error) {
      console.error('Error parsing or processing tracking data:', error);
      return { score: 0, reason: 'Invalid data' };
    }
  }

  async checkIsAgentSubmitOrDupplicate(
    agencyEmails: string[],
    userEmail: string,
    ip: string,
    country: string,
    browser: string,
    device: { model: string; type: string; vendor: string },
    phoneNumber: string,
  ): Promise<{ result: boolean; reason: string }> {
    const isAgentSubmit = agencyEmails.includes(userEmail);

    // const isExistSubmission = await this.repo.exists({
    //   where: {
    //     ip,
    //     browser,
    //     country,
    //     device,
    //   },
    // });

    const isExistSubmission = await this.repo.exists({
      where: {
        phone: phoneNumber,
      },
    });

    let reason = '';
    if (isAgentSubmit) {
      reason = 'User is an agent';
    }

    if (isExistSubmission) {
      reason = 'Submission is duplicated';
    }

    const result = isAgentSubmit || isExistSubmission;

    return { result, reason };
  }

  createSubmissionLogs(
    submission: ContactSaleSubmission,
    appointment: { score: number; reason: string },
    projectViewed: { score: number; reason: string },
    interested: { score: number; reason: string },
    unitTypePoint: { score: number; reason: string },
    projectViewDuration: { score: number; reason: string },
    redFlag: { result: boolean; reason: string },
    haveCeaInfo: boolean,
  ): ContactSaleSubmissionLog[] {
    const logs: ContactSaleSubmissionLog[] = [
      this.contactSaleSubmissionLogRepo.create({
        submission,
        type: EContactSaleSubmissionType.AppointmentDate,
        point: appointment.score,
        reason: appointment.reason,
      }),
      this.contactSaleSubmissionLogRepo.create({
        submission,
        type: EContactSaleSubmissionType.ProjectViewCount,
        point: projectViewed.score,
        reason: projectViewed.reason,
      }),
      this.contactSaleSubmissionLogRepo.create({
        submission,
        type: EContactSaleSubmissionType.Interested,
        point: interested.score,
        reason: interested.reason,
      }),
      this.contactSaleSubmissionLogRepo.create({
        submission,
        type: EContactSaleSubmissionType.UnitType,
        point: unitTypePoint.score,
        reason: unitTypePoint.reason,
      }),
      this.contactSaleSubmissionLogRepo.create({
        submission,
        type: EContactSaleSubmissionType.ProjectViewDuration,
        point: projectViewDuration.score,
        reason: projectViewDuration.reason,
      }),
    ];

    if (redFlag.result) {
      logs.push(
        this.contactSaleSubmissionLogRepo.create({
          submission,
          type: EContactSaleSubmissionType.RedFlag,
          point: 0,
          reason: redFlag.reason,
        }),
      );
    }

    if (haveCeaInfo) {
      logs.push(
        this.contactSaleSubmissionLogRepo.create({
          submission,
          type: EContactSaleSubmissionType.HaveCeaInfo,
          point: 0,
          reason: 'Exists CEA information.',
        }),
      );
    }

    return logs;
  }

  async createActionLog({
    submissionId,
    actionTrackingType,
    userId,
    content,
  }: CreateContactSaleActionLogDto) {
    const [submission, user] = await Promise.all([
      this.repo.findOne({
        where: { id: submissionId },
      }),

      this.userRepo.findOne({
        where: { id: userId },
      }),
    ]);

    if (!submission) {
      throw new BadRequestException('Submission not found');
    }

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const actionLog = this.contactSaleActionLogRepo.create({
      submissionId: submission.id,
      actionTrackingType,
      userId: user.id,
      content,
    });

    await this.contactSaleActionLogRepo.save(actionLog);

    return actionLog;
  }

  async findAllActionLogsBySubmission({
    query,
  }: {
    query: ContactSaleActionLogQueryDto;
  }) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.contactSaleActionLogRepo
      .createQueryBuilder('contactSaleActionLog')
      .leftJoinAndMapOne(
        'contactSaleActionLog.user',
        'user',
        'u',
        'u.id = contactSaleActionLog.userId',
      )
      .limit(pagination.limit)
      .offset(pagination.offset)
      .orderBy('contactSaleActionLog.createdAt', 'DESC');

    if (query?.submissionId) {
      queryBuilder.andWhere(
        'contactSaleActionLog.submissionId = :submissionId',
        {
          submissionId: query.submissionId,
        },
      );
    }

    if (query.search) {
      queryBuilder.andWhere('user.name ILIKE :search', {
        search: `%${query.search}%`,
      });
    }

    if (query.sort) {
      queryBuilder.orderBy(query.sort);
    }

    const [rawData, total] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(rawData, total, pagination);
  }
}
