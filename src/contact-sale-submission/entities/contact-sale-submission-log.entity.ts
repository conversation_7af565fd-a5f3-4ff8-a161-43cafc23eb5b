import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { ContactSaleSubmission } from './contact-sale-submission.entity';

export enum EContactSaleSubmissionType {
  AppointmentDate = 'appointmentDate',
  ProjectViewCount = 'projectViewCount',
  Interested = 'interested',
  UnitType = 'unitType',
  ProjectViewDuration = 'projectViewDuration',
  RedFlag = 'redFlag',
  HaveCeaInfo = 'haveCeaInfo',
}

@Entity()
export class ContactSaleSubmissionLog extends CrudEntity {
  @ManyToOne(
    () => ContactSaleSubmission,
    (submission) => submission.submissionLogs,
  )
  submission: ContactSaleSubmission;

  @Column({
    type: 'enum',
    enum: EContactSaleSubmissionType,
  })
  type: EContactSaleSubmissionType;

  @Column()
  point: number;

  @Column()
  reason: string;
}
