import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity } from 'typeorm';

export enum EActionTrackingType {
  UPDATE_CONTACT_STATUS = 'updateContactStatus',
  VIEW_DETAIL = 'viewDetail',
  CLICK_CONTACT_WHATSAPP_BUTTON = 'clickContactWhatsappButton',
}

@Entity()
export class ContactSaleActionLog extends CrudEntity {
  @Column({
    type: 'enum',
    enum: EActionTrackingType,
  })
  actionTrackingType: EActionTrackingType;

  @Column({ type: 'uuid' })
  userId: string;

  @Column({ type: 'uuid' })
  submissionId: string;

  @Column({ nullable: true })
  content?: string;
}
