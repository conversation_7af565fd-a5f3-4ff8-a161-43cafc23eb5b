import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { ICEAItem } from '../interfaces/cea.interface';
import { ContactSaleSubmissionLog } from './contact-sale-submission-log.entity';
import { ProjectStats } from 'src/analytic/entities/project-stats.entity';
import { UnitTypeStats } from 'src/analytic/entities/unit-type.entity';

export enum EAssignStatus {
  UNASSIGNED = 'Unassigned',
  ASSIGNED = 'Assigned',
}

export enum EContactStatus {
  NEW = 'new',
  OPEN = 'open',
  IN_PROGRESS = 'in-progress',
  UNQUALIFIED = 'unqualified',
  ATTEMPTED_TO_CONTACT = 'attempted-to-contact',
  CONNECTED = 'connected',
}

@Entity()
export class ContactSaleSubmission extends CrudEntity {
  @ManyToOne(() => Project, (project) => project.contactSaleSubmission)
  @JoinColumn()
  project: Project;

  @ManyToOne(() => User, (user) => user.contactSaleSubmission)
  @JoinColumn()
  user: User;

  @ManyToOne(() => User)
  @JoinColumn()
  assignedTo: User;

  @Column({ nullable: true })
  interestedIn?: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ nullable: true })
  appointment?: string;

  @Column({ nullable: true })
  unitTypes?: string;

  @Column({ nullable: true })
  ip?: string;

  @Column({ nullable: true })
  browser?: string;

  @Column({ nullable: true, type: 'text' })
  tracking?: string;

  @Column({ nullable: true, type: 'jsonb' })
  ceaInfo?: ICEAItem;

  @Column({ type: 'jsonb', nullable: true })
  device?: Record<string, string>;

  @Column({ nullable: true })
  country?: string;

  @Column({ default: 0 })
  score: number;

  @OneToMany(() => ContactSaleSubmissionLog, (log) => log.submission)
  submissionLogs: ContactSaleSubmissionLog[];

  @OneToOne(
    () => ProjectStats,
    (projectStats) => projectStats.contactSaleSubmission,
    { nullable: true },
  )
  projectStats?: ProjectStats;

  @OneToMany(
    () => UnitTypeStats,
    (unitTypeStats) => unitTypeStats.contactSaleSubmission,
    { nullable: true },
  )
  unitTypesStats?: UnitTypeStats[];

  @Column({
    type: 'enum',
    enum: EAssignStatus,
    default: EAssignStatus.UNASSIGNED,
  })
  status: EAssignStatus;

  @Column({
    type: 'enum',
    enum: EContactStatus,
    default: EContactStatus.NEW,
  })
  contactStatus: EContactStatus;

  @Column({ nullable: true })
  submittedFromDomain?: string;

  @Column({ nullable: true })
  isSubmittedFromLdp?: boolean;

  @Column({ nullable: true })
  deletedByIP?: string;

  @Column({ nullable: true })
  note?: string;

  @Column({ nullable: true })
  adminNote?: string;
}
