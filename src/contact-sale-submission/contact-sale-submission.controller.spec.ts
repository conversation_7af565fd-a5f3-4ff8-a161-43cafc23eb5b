import { Test, TestingModule } from '@nestjs/testing';
import { ContactSaleSubmissionController } from './contact-sale-submission.controller';
import { ContactSaleSubmissionService } from './contact-sale-submission.service';

describe('ContactSaleSubmissionController', () => {
  let controller: ContactSaleSubmissionController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContactSaleSubmissionController],
      providers: [ContactSaleSubmissionService],
    }).compile();

    controller = module.get<ContactSaleSubmissionController>(ContactSaleSubmissionController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
