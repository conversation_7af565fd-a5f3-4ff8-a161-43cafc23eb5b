import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { AppService } from './app.service';
import { ResponseMessage } from './core/decorators/response-message.decorator';
import { Auth } from './iam/authentication/decorators/auth.decorator';
import { AuthType } from './iam/authentication/enums/auth-type.enum';

@Controller()
@Auth(AuthType.None)
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/')
  @ResponseMessage('Welcome to Property Listing')
  @HttpCode(HttpStatus.OK)
  getHello() {}
}
