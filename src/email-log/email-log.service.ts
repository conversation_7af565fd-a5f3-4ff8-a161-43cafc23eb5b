import { InjectRepository } from '@nestjs/typeorm';
import { EmailLog } from './entities/email-log.entity';
import { Repository } from 'typeorm';
import { CreateEmailLogDto } from './dto/create-email-log.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class EmailLogService {
  constructor(
    @InjectRepository(EmailLog)
    private readonly emailLogRepo: Repository<EmailLog>,
  ) {}

  async create(emailLogDto: CreateEmailLogDto): Promise<EmailLog> {
    try {
      return await this.emailLogRepo.save(emailLogDto);
    } catch (error) {
      console.log('@@@ Error in email log service', error);
    }
  }
}
