import { Column, Entity } from 'typeorm';
import CrudEntity from 'src/core/entities/crud.entity';

@Entity()
export class EmailLog extends CrudEntity {
  @Column()
  sender: string;

  @Column()
  receiver: string;

  @Column()
  subject: string;

  @Column()
  template: string;

  @Column({
    type: 'jsonb',
  })
  params: Record<string, any>;

  @Column({
    nullable: true,
    default: null,
  })
  failedReason?: string;

  @Column({
    nullable: true,
    default: null,
  })
  text?: string;
}
