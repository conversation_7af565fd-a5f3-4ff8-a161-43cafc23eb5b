import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { generateSlug } from 'src/core/utils/slug.util';
import { Location } from './entities/location.entity';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { RedisService } from 'src/redis/redis.service';
import { QueryLocationDto } from './dto/query-location.dto';

@Injectable()
export class LocationService {
  constructor(
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    private readonly redisService: RedisService,
  ) {}

  async create(body: CreateLocationDto) {
    if (body.slug) {
      const existed = await this.locationRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug =
      body.slug ?? (await generateSlug(body.name, this.locationRepo));

    const data: Partial<Location> = {
      ...body,
      slug,
      name: { [ELangCode.en]: body.name },
    };
    await this.redisService.deletePattern('location@*');

    return await this.locationRepo.save(data);
  }

  async getDetail(id: string) {
    const item = await this.locationRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Location is not found');
    }

    return item;
  }

  async getAll(query: QueryLocationDto) {
    const lang = query?.lang ?? ELangCode.en;
    const queryBuilder = this.locationRepo
      .createQueryBuilder('location')
      .select('location.id', 'id')
      .addSelect('location.slug', 'slug')
      .addSelect('location.createdAt', 'createdAt')
      .addSelect('location.updatedAt', 'updatedAt')
      .addSelect('location.deletedAt', 'deletedAt')
      .addSelect(createMultilingualSelect('location', 'name', lang), 'name');

    if (query.search) {
      queryBuilder.andWhere(
        `regexp_replace(${createMultilingualSelect('location', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :name`,
        {
          name: `%${query.search.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%`,
        },
      );
    }
    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`location.${key}`, order);
      });
    }

    return await queryBuilder.getRawMany();
  }

  async update(id: string, body: UpdateLocationDto) {
    const item = await this.locationRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Location is not found');
    }

    const langCode = body.lang ?? ELangCode.en;

    if (body.slug && body.slug !== item.slug) {
      const existed = await this.locationRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== item.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    const data: Partial<Location> = {
      ...item,
      ...body,
      name: {
        ...item.name,
        [langCode]: body.name ?? item.name?.[langCode],
      },
    };

    await this.redisService.deletePattern('location@*');
    return await this.locationRepo.save(data);
  }

  async delete(id: string) {
    const item = await this.locationRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Location is not found');
    }

    await this.redisService.deletePattern('location@*');
    return await this.locationRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  formatResponse(item: Location, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
    };
  }

  async findByName(name: string) {
    return await this.redisService.cacheWrapper(
      `location@name:${name}`,
      async () => {
        return await this.locationRepo.findOneBy({
          name: {
            en: name,
          },
        });
      },
    );
  }

  async getOrCreateByName(name: string) {
    const location = await this.findByName(name);

    if (location) {
      return location;
    }

    return await this.create({ name });
  }
}
