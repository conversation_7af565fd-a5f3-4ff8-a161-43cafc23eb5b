import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { Section } from 'src/section/entities/section.entity';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity()
export class Location extends CrudEntity {
  @Column({
    type: 'jsonb',
  })
  name: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @OneToMany(() => Project, (project) => project.location)
  projects: Project[];

  @OneToMany(() => Section, (section) => section.location)
  sections: Section[];
}
