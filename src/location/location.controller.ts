import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { LocationService } from './location.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { QueryLocationDto } from './dto/query-location.dto';
import { ApiKeyGuard } from 'src/api-key/guards/api-key.guard';
import { ApiKey } from 'src/api-key/decorators/api-key.decorator';
import { EPermissionApiKey } from 'src/api-key/enums/permission-api-key.enum';

@Controller('location')
@CacheOption('location')
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @Post()
  create(@Body() createLocationDto: CreateLocationDto) {
    return this.locationService.create(createLocationDto);
  }

  @Get()
  @Public()
  @UseInterceptors(CacheInterceptor)
  findAll(@Query() query: QueryLocationDto) {
    return this.locationService.getAll(query);
  }

  @Get('share')
  @Public()
  @UseInterceptors(CacheInterceptor)
  @UseGuards(ApiKeyGuard)
  @ApiKey([EPermissionApiKey.GET_ALL_LOCATIONS])
  share(@Query() query: QueryLocationDto) {
    return this.locationService.getAll(query);
  }

  @Get(':id')
  getDetail(@Param('id') id: string) {
    return this.locationService.getDetail(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateLocationDto: UpdateLocationDto,
  ) {
    return this.locationService.update(id, updateLocationDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.locationService.delete(id);
  }
}
