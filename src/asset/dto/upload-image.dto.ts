import { Type } from 'class-transformer';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { ParseOptionalBoolean } from 'src/core/decorators/parse-boolean.decorator';

export class UploadImageDto {
  @IsInt({ each: true })
  @Type(() => Number)
  @IsOptional()
  sizes?: number[];

  @IsBoolean()
  @IsOptional()
  @ParseOptionalBoolean()
  forSEO?: boolean;

  @IsOptional()
  fileName?: string;

  @IsOptional()
  @IsBoolean()
  noCompress?: boolean;
}
