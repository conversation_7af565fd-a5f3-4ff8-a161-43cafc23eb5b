import CrudEntity from 'src/core/entities/crud.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { EAssetRelation, EAssetType } from '../enums/asset.enum';
import { AssetUrlsType } from '../types/image.type';
import { Project } from 'src/project/entities/project.entity';

@Entity()
export class Asset extends CrudEntity {
  @Column({
    type: 'enum',
    enum: EAssetType,
    default: EAssetType.Binary,
  })
  type: EAssetType;

  @Column({
    type: 'enum',
    enum: EAssetRelation,
    default: null,
    nullable: true,
  })
  relation?: EAssetRelation;

  /**
   * @description: distinct for medias field in project
   */
  @Column({
    nullable: true,
    type: 'uuid',
  })
  relationId?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  urls?: AssetUrlsType;

  @Column({
    nullable: true,
  })
  forSEO?: boolean;

  /**
   * @description: distinct for siteplanImages field in project
   */
  @ManyToOne(() => Project, (project) => project.siteplanImages)
  @JoinColumn()
  project: Project;
}
