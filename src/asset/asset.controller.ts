import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseInterceptors,
  MaxFileSizeValidator,
  ParseFilePipe,
  UploadedFile,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { AssetService } from './asset.service';
import { UploadImageDto } from './dto/upload-image.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { Auth } from 'src/iam/authentication/decorators/auth.decorator';
import { AuthType } from 'src/iam/authentication/enums/auth-type.enum';

@Controller('asset')
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @Post('image')
  @UseInterceptors(FileInterceptor('image'))
  uploadImage(
    @Body() createAssetDto: UploadImageDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10485760 })], // 10MB
        fileIsRequired: false,
      }),
    )
    file?: Express.Multer.File,
  ) {
    try {
      return this.assetService.uploadImage(createAssetDto, file);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  @Post('video')
  @UseInterceptors(FileInterceptor('video'))
  uploadVideo(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 209715200 })], // 200MB
        fileIsRequired: false,
      }),
    )
    file?: Express.Multer.File,
  ) {
    try {
      return this.assetService.uploadVideo(file);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  @Get()
  findAll(@Query() query: PaginationQueryDto) {
    return this.assetService.findAll(query);
  }

  @Get(':id')
  @Auth(AuthType.None)
  findOne(@Param('id') id: string) {
    return this.assetService.findOne(id);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    try {
      return this.assetService.remove(id);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }
}
