import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  DeleteObjectsCommand,
  PutObjectCommand,
  S3Client,
  S3,
  UploadPartCommandInput,
} from '@aws-sdk/client-s3';
import * as sharp from 'sharp';
import { UploadImageDto } from './dto/upload-image.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Asset } from './entities/asset.entity';
import { Repository } from 'typeorm';
import { EAssetRelation, EAssetType } from './enums/asset.enum';
import { AssetUrlsType } from './types/image.type';
import * as path from 'path';
import { join, resolve } from 'path';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import awsConfig from 'src/core/configs/aws.config';
import { ConfigType } from '@nestjs/config';
import * as _ from 'lodash';
import {
  createReadStream,
  createWriteStream,
  existsSync,
  mkdirSync,
  readFileSync,
  statSync,
  unlinkSync,
} from 'fs';
import axios, { AxiosProgressEvent, AxiosResponse } from 'axios';
import { Readable, Stream } from 'stream';
import * as ProgressBar from 'progress';
import { WebpOptions } from 'sharp';

const CHUNK_SIZE = 5 * 1024 * 1024;

@Injectable()
export class AssetService {
  private readonly _s3Client: S3Client;
  private readonly _s3: S3;
  private get bucketName() {
    return this.awsConfiguration.bucketName;
  }

  constructor(
    @InjectRepository(Asset) private readonly assetRepo: Repository<Asset>,
    @Inject(awsConfig.KEY)
    private readonly awsConfiguration: ConfigType<typeof awsConfig>,
  ) {
    this._s3Client = new S3Client({
      // credentials: {},
      region: awsConfiguration.bucketRegion,
    });

    this._s3 = new S3({
      region: awsConfiguration.bucketRegion,
    });
  }

  async findAll(query: PaginationQueryDto) {
    const pagination = getPaginationOption(query);
    const [data, total] = await this.assetRepo
      .createQueryBuilder('asset')
      .take(pagination.limit)
      .skip(pagination.offset)
      .orderBy('asset.createdAt')
      .getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async findOne(id: string) {
    const asset = await this.assetRepo.findOneBy({ id });
    if (!asset) {
      throw new NotFoundException(`Asset with id '${id}' is not found`);
    }
    return asset;
  }

  async remove(id: string) {
    const asset = await this.assetRepo.findOneBy({ id });
    if (!asset) {
      throw new NotFoundException(`Asset with id '${id}' is not found`);
    }

    if (asset.urls) {
      const keys = asset.urls.map((item) => {
        const url = typeof item === 'string' ? item : item.url;
        if (url.startsWith('/')) {
          return url.substring(1);
        }
        return url;
      });
      await this.deleteObjectsFromBucket(keys);
    }
    return await this.assetRepo.softDelete({ id });
  }

  async removeAllByRelatedId(relatedId: string) {
    const assets = await this.getByRelatedId(relatedId);
    return await Promise.all(assets.map((asset) => this.remove(asset.id)));
  }

  async getByRelatedId(relationId: string, excludes?: string[]) {
    const queryBuilder = this.assetRepo
      .createQueryBuilder('asset')
      .where('asset.relationId = :relationId', { relationId });

    if (excludes?.length) {
      queryBuilder.andWhere('asset.id NOT IN (:...excludes)', { excludes });
    }

    return await queryBuilder.getMany();
  }

  async addAssetRelation(
    id: string,
    relation?: EAssetRelation,
    relationId?: string,
  ) {
    const asset = await this.assetRepo.findOneBy({ id });
    if (!asset) {
      throw new NotFoundException(`Asset with id '${id}' is not found`);
    }
    if (asset.relation || asset.relationId) {
      throw new UnprocessableEntityException(
        `Asset with id '${id}' already has a relationship`,
      );
    }
    return await this.assetRepo.save({
      ...asset,
      relation,
      relationId,
    });
  }

  async uploadVideo(file?: Express.Multer.File) {
    try {
      if (!file) {
        throw new BadRequestException('video file is required');
      }

      const asset = await this.assetRepo.save({
        type: EAssetType.Video,
      });

      const urls: AssetUrlsType = [];
      const key = `assets/videos/${asset.id}/${file.originalname}`;

      let stream = null;

      if (file?.stream) {
        stream = file.stream;
      } else {
        stream = readFileSync(file.path);
      }

      await this.uploadObjectToBucket({
        body: stream,
        key: key,
      });

      urls.push('/' + key);
      asset.urls = urls;
      return await this.assetRepo.save(asset);
    } catch (error) {
      console.log(error);

      throw new BadRequestException(
        'Error when uploading video, please try again!',
      );
    }
  }

  async uploadImage(
    body: UploadImageDto,
    file?: Express.Multer.File,
    webpConfig?: WebpOptions,
  ) {
    try {
      const tempDir = './.tmp';

      if (!existsSync(tempDir)) {
        mkdirSync(tempDir, { recursive: true });
      }

      if (!file) {
        const filePath = join(
          process.cwd(),
          'src',
          'resources',
          body.fileName || 'defaultImage.jpg',
        );
        if (!existsSync(filePath)) {
          throw new BadRequestException('Image file is required');
        }
        const buffer = readFileSync(filePath);
        file = {
          originalname: body.fileName,
          buffer: buffer,
          path: filePath,
          mimetype: 'image/jpeg',
          size: buffer.length,
          fieldname: '',
          encoding: '',
          stream: createReadStream(filePath),
          destination: '',
          filename: '',
        } as Express.Multer.File;
      }

      const asset = await this.assetRepo.save({
        type: EAssetType.Image,
        forSEO: body.forSEO,
      });

      const urls: AssetUrlsType = [];
      const filename = _.kebabCase(path.parse(file.originalname).name).split(
        '?',
      )[0];

      if (body.noCompress) {
        const extension = path.extname(file.originalname);
        const key = `assets/images/${asset.id}/${filename}${extension}`;

        let fileBuffer: Buffer;
        if (file.buffer) {
          fileBuffer = file.buffer;
        } else {
          fileBuffer = readFileSync(file.path);
        }

        await this.uploadObjectToBucket({
          body: fileBuffer,
          key: key,
        });

        const metadata = await sharp(fileBuffer).metadata();

        urls.push({
          url: '/' + key,
          width: metadata.width,
        });
      } else {
        if (!body.sizes?.length) {
          const image = await this.compressImage(
            filename,
            file.path,
            webpConfig,
          );
          const key = `assets/images/${asset.id}/${image.filename}`;
          await this.uploadObjectToBucket({
            body: image.buffer,
            key: key,
          });
          urls.push({
            url: '/' + key,
            width: image.width,
          });
        } else {
          for (const width of body.sizes) {
            const image = await (body.forSEO
              ? this.resizeImageForSEO(filename, file.path, width)
              : this.resizeImage(filename, file.path, width));
            const key = `assets/images/${asset.id}/${image.filename}`;

            if (!existsSync(image.path)) {
              console.error(`Temporary file missing: ${image.path}`);
              throw new Error(`Temporary file missing for: ${image.filename}`);
            }

            const stream = createReadStream(image.path);

            stream.on('error', (err) => {
              console.error('Stream error:', err.message);
              throw new Error(`Failed to create stream for: ${image.path}`);
            });

            await this.uploadObjectToBucket({
              body: stream,
              key: key,
            });
            urls.push({
              width,
              url: '/' + key,
            });
            unlinkSync(image.path);
          }
        }
      }

      asset.urls = urls;
      return await this.assetRepo.save(asset);
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        'Error when uploading image, please try again!',
      );
    }
  }

  async uploadObjectToBucket(data: { body: Buffer | Readable; key: string }) {
    await this._s3Client.send(
      new PutObjectCommand({
        Bucket: this.bucketName,
        Body: data.body,
        Key: data.key,
        ACL: 'public-read',
      }),
    );
    // const dir = path.join(__dirname, '..', '..', '..', '.tmp');
    // mkdirSync(path.join(dir, path.dirname(data.key)), { recursive: true });
    // writeFileSync(path.join(dir, data.key), data.buffer);
    console.log(`${data.key} uploaded successfully.`);
  }

  async deleteObjectsFromBucket(keys: string[]) {
    const deleteObjectsCommand = new DeleteObjectsCommand({
      Bucket: this.bucketName,
      Delete: { Objects: keys.map((key) => ({ Key: key })) },
    });
    await this._s3Client.send(deleteObjectsCommand);
  }

  async resizeImage(filename: string, buff: Buffer | string, width: number) {
    const output = `${filename}-${width}w.webp`;
    const tempDir = './.tmp';
    const tempFilePath = path.join(tempDir, output);

    if (!existsSync(tempDir)) {
      mkdirSync(tempDir, { recursive: true });
    }

    try {
      console.log(`Starting resizeImage for ${filename} with width ${width}`);

      if (!buff) {
        throw new Error('Buffer or file path is missing');
      }

      await sharp(buff)
        .resize(width)
        .webp({
          minSize: true,
          quality: 70,
        })
        .toFile(tempFilePath);

      console.log(`Image resized and saved at: ${tempFilePath}`);

      return { filename: output, path: tempFilePath };
    } catch (err) {
      console.error('Error resizing image:', err.message);
      throw new Error(`Failed to resize image: ${filename}`);
    }
  }

  async compressImage(
    filename: string,
    buff: Buffer | string,
    config?: WebpOptions,
  ) {
    const output = `${filename}.webp`;
    const tempDir = './.tmp';

    if (!existsSync(tempDir)) {
      mkdirSync(tempDir, { recursive: true });
    }

    try {
      console.log(`Starting compressImage for ${filename}`);

      const img = sharp(buff).webp({
        minSize: true,
        quality: 100,
        ...config,
      });

      const metadata = await img.metadata();
      const buffer = await img.toBuffer();

      console.log(`Image compressed successfully: ${filename}`);

      return { filename: output, buffer, width: metadata.width };
    } catch (err) {
      console.error('Error compressing image:', err.message);
      throw new Error(`Failed to compress image: ${filename}`);
    }
  }

  async resizeImageForSEO(
    filename: string,
    buff: Buffer | string,
    width: number,
  ) {
    const output = `${filename}-${width}x${width}.jpeg`;
    const tempFilePath = path.join('./.tmp', output);

    await sharp(buff)
      .resize({ width, height: width })
      .jpeg({
        quality: 70,
      })
      .toFile(tempFilePath);

    return { filename: output, path: tempFilePath };
  }

  async downloadAndReuploadImage(
    url: string,
    forSEO = false,
    largeSize = false,
  ) {
    try {
      const nonce = Math.floor(Math.random() * 1000000);

      console.log('download image from url', nonce, url);

      const res: AxiosResponse<any, any> = await axios.request({
        url,
        responseType: 'stream',
        timeout: 5000,
        onDownloadProgress: (ev: AxiosProgressEvent) => {
          console.log('download url', ev.progress, url);
        },
      });

      const file = path.parse(url.split('?')[0]);
      const filename = `${nonce}_${_.kebabCase(file.name)}`;
      const tempFilePath = path.join(
        './.tmp',
        file.ext ? `${filename}.${file.ext}` : filename,
      );

      const writable = createWriteStream(tempFilePath);

      await new Promise((resolve, reject) => {
        // Pipe the response data to the file
        (res.data as Stream)
          .pipe(writable)
          .on('finish', resolve)
          .on('close', () => console.log('download done', nonce))
          .on('error', reject);
      });

      console.log('downloaded to file', tempFilePath);

      const body: UploadImageDto = {
        forSEO,
        sizes: largeSize ? [1920, 1366, 992] : [1280, 800, 480],
      };

      const asset = await this.assetRepo.save({
        type: EAssetType.Image,
        forSEO: body.forSEO,
      });

      const urls: AssetUrlsType = [];

      if (!body.sizes?.length) {
        const image = await this.compressImage(filename, tempFilePath);
        const key = `assets/images/${asset.id}/${image.filename}`;
        await this.uploadObjectToBucket({
          body: image.buffer,
          key: key,
        });
        urls.push({
          url: '/' + key,
          width: image.width,
        });
      } else {
        for (const width of body.sizes) {
          const image = await (body.forSEO
            ? this.resizeImageForSEO(filename, tempFilePath, width)
            : this.resizeImage(filename, tempFilePath, width));
          const key = `assets/images/${asset.id}/${image.filename}`;

          await this.uploadLargeFileToS3(image.path, key);
          urls.push({
            width,
            url: '/' + key,
          });
          unlinkSync(image.path);
        }
      }

      asset.urls = urls;
      console.log('upload image done', nonce);
      return await this.assetRepo.save(asset);
    } catch (err) {
      console.error('upload image', err);
    }
  }

  async downloadAndReuploadVideo(url: string) {
    try {
      const nonce = Math.floor(Math.random() * 1000000);
      console.log('process video', url);
      if (url.startsWith('https://www.youtube.com')) {
        // save youtube link
        return await this.assetRepo.save({
          type: EAssetType.Youtube,
          urls: [url],
        });
      }

      const res = await axios
        .request({
          url,
          responseType: 'stream',
          timeout: 5000,
          onDownloadProgress: (ev: AxiosProgressEvent) => {
            console.log('download url', ev.progress, url);
          },
        })
        .catch((err) => {
          console.error('download video error', err);
        });

      if (!res) {
        return;
      }

      const file = path.parse(url.split('?')[0]);
      const filename = `${nonce}_${_.kebabCase(file.name)}`;
      const tempFilePath = path.join(
        './.tmp',
        file.ext ? `${filename}.${file.ext}` : filename,
      );

      const writable = createWriteStream(tempFilePath);

      // Pipe the response data to the file
      try {
        await new Promise((resolve, reject) => {
          // Pipe the response data to the file
          (res.data as Stream)
            .pipe(writable)
            .on('finish', resolve)
            .on('error', reject);
        });

        console.log('downloaded to file', tempFilePath);

        const asset = await this.assetRepo.save({
          type: EAssetType.Video,
        });

        const key = `assets/videos/${asset.id}/${
          file.ext ? `${filename}.${file.ext}` : filename
        }`;

        // console.log('begin upload video', key);
        // Read the file and upload to the bucket
        await this.uploadLargeFileToS3(tempFilePath, key);
        // const stream = createReadStream(tempFilePath);
        // await this.uploadObjectToBucket({
        //   body: stream,
        //   key: key,
        // });
        asset.urls = ['/' + key];
        // Clean up the temporary file

        console.log('remove file', tempFilePath);
        unlinkSync(tempFilePath);

        return await this.assetRepo.save(asset);
      } catch (err) {
        console.error('upload video error', err.message);
        // Clean up the temporary file
        unlinkSync(tempFilePath);
        console.log('remove file', tempFilePath);
      }
    } catch (err) {
      console.error('upload video error', err.message);
      return undefined;
    }
  }

  async uploadLargeFileToS3(filepath: string, key: string) {
    const multipartParams = {
      Bucket: this.bucketName,
      Key: key,
    };
    const fileSize = statSync(filepath).size;

    if (fileSize < CHUNK_SIZE) {
      return this.uploadObjectToBucket({
        body: createReadStream(filepath),
        key,
      });
    }
    const progressBar = new ProgressBar(
      `  uploading ${filepath}  [:bar] :rate/bps :percent :etas`,
      {
        complete: '=',
        incomplete: ' ',
        width: 20,
        total: fileSize,
        stream: process.stdout,
      },
    );

    // Start multipart upload
    const multipartUpload =
      await this._s3.createMultipartUpload(multipartParams);

    // console.log('Start uploading', filepath, key, multipartParams);
    const multipartUploadId = multipartUpload.UploadId;

    const fileStream = createReadStream(filepath, {
      highWaterMark: CHUNK_SIZE,
    });

    const parts: Array<Promise<{ PartNumber: number; ETag: string }>> = [];
    let partNumber = 1;

    progressBar.tick(0);
    for await (const chunk of fileStream) {
      if (!chunk) break;

      const partNum = partNumber++;
      // Upload part
      const uploadPartParams: UploadPartCommandInput = {
        Bucket: this.bucketName,
        Key: key,
        PartNumber: partNum,
        UploadId: multipartUploadId,
        Body: chunk,
      };
      parts.push(
        this._s3.uploadPart(uploadPartParams).then((uploadedPart) => {
          progressBar.tick(chunk.length);

          return {
            ETag: uploadedPart.ETag,
            PartNumber: partNum,
          };
        }),
      );
    }

    if (parts.length === 0) {
      throw new Error('No parts uploaded');
    }

    // Complete multipart upload
    const completeMultipartUploadParams = {
      Bucket: this.bucketName,
      Key: key,
      UploadId: multipartUploadId,
      MultipartUpload: {
        Parts: await Promise.all(parts),
      },
    };

    await this._s3.completeMultipartUpload(completeMultipartUploadParams);
    console.log('Upload complete');
  }

  async createOne(data: any) {
    return await this.assetRepo.save(data);
  }

  async cloneImage(urls: AssetUrlsType) {
    const asset = await this.assetRepo.save({
      type: EAssetType.Image,
      forSEO: false,
    });

    const clonedUrls: AssetUrlsType = [];

    for (const urlObj of urls) {
      const sourceKey =
        typeof urlObj === 'string'
          ? urlObj
          : urlObj.url.startsWith('/')
            ? urlObj.url.substring(1)
            : urlObj.url;

      const destinationKey = `assets/images/${asset.id}/${path.basename(typeof urlObj === 'string' ? urlObj : urlObj.url)}`;

      try {
        let width: number | undefined;
        if (typeof urlObj !== 'string' && 'width' in urlObj) {
          width = urlObj.width;
        }

        await this._s3.copyObject({
          Bucket: this.bucketName,
          CopySource: `${this.bucketName}/${sourceKey}`,
          Key: destinationKey,
          ACL: 'public-read',
        });

        clonedUrls.push({
          width,
          url:
            typeof urlObj === 'string'
              ? '/' + destinationKey
              : '/' + destinationKey,
        });
      } catch (error) {
        throw new BadRequestException(
          `Failed to copy image from URL: ${typeof urlObj === 'string' ? urlObj : urlObj.url}`,
        );
      }
    }

    asset.urls = clonedUrls;
    return await this.assetRepo.save(asset);
  }

  async attachAssetToProject(assetId: string, projectId: string) {
    const asset = await this.assetRepo.findOneBy({ id: assetId });
    if (!asset) {
      throw new NotFoundException(`Asset with id '${assetId}' is not found`);
    }

    await this.assetRepo.update(
      { id: assetId },
      { project: { id: projectId } },
    );

    return asset;
  }
}
