import { Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';
import { DataSource } from 'typeorm';

@Injectable()
export class RedisService {
  constructor(private dataSource: DataSource) {}

  get client() {
    const client = this.dataSource.queryResultCache?.['client'] as
      | Redis
      | undefined;
    if (client?.status !== 'ready') {
      console.log('Redis is not ready');
      return;
    }
    return client;
    // return null;
  }

  getClient(): Redis | undefined {
    return this.client;
  }

  async get<T>(key: string) {
    const result = await this.client?.get(key);
    if (result) {
      return JSON.parse(result) as T;
    }
    return undefined;
  }

  async lock(key: string, durationInSecond = 10) {
    return await this.client?.set(key, 'locked', 'EX', durationInSecond, 'NX');
  }

  async set(key: string, value: any, durationInSecond = 10) {
    await this.client?.set(key, JSON.stringify(value));

    return await this.client?.expire(key, durationInSecond);
  }

  async delete(key: string) {
    return await this.client?.del(key);
  }

  async deletePattern(pattern: string) {
    const keys = await this.listingKey(pattern);

    if (keys) {
      await Promise.all(keys?.map((key) => this.delete(key)));
    }
  }

  async hasKey(key: string) {
    const keys = await this.listingKey(key);
    return !!keys?.length;
  }

  async listingKey(pattern: string) {
    return await this.client?.keys(pattern);
  }

  async incr(key: string) {
    return await this.client?.incr(key);
  }

  async cacheWrapper<T>(
    key: string,
    fetcher: () => T | Promise<T>,
    duration = 10,
  ) {
    const data = await this.get<T>(key);
    if (data) {
      console.log('cacheWrapper', key, data);
      return data;
    }

    const res = await fetcher();

    await this.set(key, data, duration);
    return res;
  }
}
