import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { RedisService } from '../redis.service';
import * as objectHash from 'object-hash';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { REQUEST_USER_KEY } from 'src/iam/iam.constants';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,

    private readonly redisService: RedisService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const [prefix, duration = 1] =
      this.reflector.getAllAndOverride('cache_option', [
        context.getHandler(),
        context.getClass(),
      ]) ?? [];

    const key = this.generateCacheKey(prefix, request);

    return new Observable((observer) => {
      this.redisService
        .get(key)
        .then((cachedData) => {
          if (cachedData !== undefined) {
            observer.next(cachedData);
            observer.complete();
          } else {
            next
              .handle()
              .pipe(
                tap(async (data) => {
                  await this.redisService
                    .set(key, data, duration)
                    .catch((err) => {
                      console.error(err, 'Redis');
                    });
                }),
              )
              .subscribe({
                next: (data) => {
                  observer.next(data);
                },
                error: (error) => {
                  observer.error(error);
                },
                complete: () => {
                  observer.complete();
                },
              });
          }
        })
        .catch((err) => {
          console.error(err, 'Redis');

          next
            .handle()
            .pipe(
              tap(async (data) => {
                await this.redisService
                  .set(key, data, duration)
                  .catch((err) => {
                    console.error(err, 'Redis');
                  });
              }),
            )
            .subscribe({
              next: (data) => {
                observer.next(data);
              },
              error: (error) => {
                observer.error(error);
              },
              complete: () => {
                observer.complete();
              },
            });
        });
    });
  }

  private generateCacheKey(prefix = 'prefix', request: Request): string {
    // Customize the generation of cache key based on request and prefix
    const userId = request[REQUEST_USER_KEY]?.sub;

    const hashed = objectHash({
      method: request.method,
      userId,
      domain: request.headers['user-domain'],
      lang: request.headers['lang'],
      ...request.query,
    });

    return `${prefix}@${request.path}:${hashed}`;
  }
}
