import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { AnalyticService } from './analytic.service';
import { QueryProjectStatsDto } from './dto/query-project-stats.dto';
import { QueryUnitTypeStatsDto } from './dto/query-unit-type-stats.dto';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { QueryPriceRangeStatsDto } from './dto/query-price-range-stats.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';

@Controller('analytic')
@UseInterceptors(CacheInterceptor)
@CacheOption('analytic', 60)
export class AnalyticController {
  constructor(private readonly analyticService: AnalyticService) {}

  @Get('project-stats')
  getProjectStats(@Query() query: QueryProjectStatsDto) {
    return this.analyticService.getProjectStats(query);
  }

  @Get('unit-type-stats')
  getUnitTypeStats(@Query() query: QueryUnitTypeStatsDto) {
    return this.analyticService.getUnitTypeStats(query);
  }

  @Get('price-range-stats')
  getPriceRangeStats(@Query() query: QueryPriceRangeStatsDto) {
    return this.analyticService.getPriceRangeStats(query);
  }

  @Get('/project-stats/contact-sales-submission')
  getProjectStatsByContactSalesSubmission(
    @ActiveUser('sub') userId: string,
    @Query() query: QueryProjectStatsDto,
  ) {
    return this.analyticService.getProjectStatsContactSalesSubmission(
      query,
      userId,
    );
  }

  @Get('/unit-type-stats/contact-sales-submission')
  getUnitTypeStatsByContactSalesSubmission(
    @ActiveUser('sub') userId: string,
    @Query() query: QueryUnitTypeStatsDto,
  ) {
    return this.analyticService.getUnitTypeStatsContactSalesSubmission(
      query,
      userId,
    );
  }
}
