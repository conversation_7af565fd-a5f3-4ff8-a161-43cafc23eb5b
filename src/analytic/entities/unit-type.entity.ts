import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
} from 'typeorm';

@Entity()
@Index(['date', 'unitTypeId', 'contactSaleSubmissionId'], { unique: true })
export class UnitTypeStats extends CrudEntity {
  @Column({ type: 'timestamptz' })
  @Index()
  date: Date;

  @Column({ type: 'uuid' })
  unitTypeId: string;

  @ManyToOne(() => UnitType)
  @JoinColumn({ name: 'unitTypeId' })
  unitType: UnitType;

  @Column({ default: 0 })
  entriesCount: number;

  @Column({ type: 'uuid', nullable: true })
  contactSaleSubmissionId?: string;

  @ManyToOne(
    () => ContactSaleSubmission,
    (contactSaleSubmission) => contactSaleSubmission.unitTypesStats,
    { nullable: true },
  )
  @JoinColumn({
    name: 'contactSaleSubmissionId',
  })
  contactSaleSubmission?: ContactSaleSubmission;
}
