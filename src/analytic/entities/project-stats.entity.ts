import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
} from 'typeorm';

@Entity()
@Index(['date', 'projectId', 'contactSaleSubmissionId'], { unique: true })
export class ProjectStats extends CrudEntity {
  @Column({ type: 'timestamptz' })
  date: Date;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ default: 0 })
  entriesCount: number;

  @Column({ default: 0 })
  viewsCount: number;

  @Column({ default: 0 })
  unitsSoldCount: number;

  @Column({ type: 'uuid', nullable: true })
  contactSaleSubmissionId?: string;

  @OneToOne(
    () => ContactSaleSubmission,
    (contactSaleSubmission) => contactSaleSubmission.projectStats,
    { nullable: true },
  )
  @JoinColumn({
    name: 'contactSaleSubmissionId',
  })
  contactSaleSubmission?: ContactSaleSubmission;
}
