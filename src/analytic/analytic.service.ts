import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProjectStats } from './entities/project-stats.entity';
import { In, IsNull, MoreThan, Repository } from 'typeorm';
import { UnitTypeStats } from './entities/unit-type.entity';
import * as moment from 'moment';
import {
  EProjectStatsSortBy,
  QueryProjectStatsDto,
} from './dto/query-project-stats.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EOrderType } from 'src/core/enums/sort.enum';
import { ProjectService } from 'src/project/project.service';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import {
  EUnitTypeStatsSortBy,
  QueryUnitTypeStatsDto,
} from './dto/query-unit-type-stats.dto';
import { PriceRangeConfigs } from './constants/price-range.constant';
import { PriceRangeStats } from './entities/price-range-stats.entity';
import { QueryPriceRangeStatsDto } from './dto/query-price-range-stats.dto';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';

@Injectable()
export class AnalyticService {
  constructor(
    @InjectRepository(ProjectStats)
    private readonly projectStatsRepo: Repository<ProjectStats>,
    @InjectRepository(UnitTypeStats)
    private readonly unitTypeStatsRepo: Repository<UnitTypeStats>,
    @InjectRepository(PriceRangeStats)
    private readonly priceRangeStatsRepo: Repository<PriceRangeStats>,

    @InjectRepository(ContactSaleSubmission)
    private readonly contactSaleSubmissionRepo: Repository<ContactSaleSubmission>,

    private readonly projectService: ProjectService,
  ) {}

  async incrementProjectViewCount(projectId: string, count = 1) {
    const date = moment().startOf('date').toDate();

    const record = await this.projectStatsRepo.findOneBy({
      projectId,
      contactSaleSubmissionId: IsNull(),
      date,
    });

    if (!record) {
      return await this.projectStatsRepo.save({
        projectId,
        date,
        viewsCount: count,
      });
    }

    return await this.projectStatsRepo.increment(
      { id: record.id },
      'viewsCount',
      count,
    );
  }

  async incrementProjectEntriesCount(
    projectId: string,
    contactSaleSubmissionId: string,
    date: Date,
    count = 1,
  ) {
    const record = await this.projectStatsRepo.findOneBy({
      projectId,
      contactSaleSubmissionId,
      date,
      deletedAt: IsNull(),
    });

    if (!record) {
      return await this.projectStatsRepo.save({
        projectId,
        contactSaleSubmissionId,
        date,
        entriesCount: count,
      });
    }

    return await this.projectStatsRepo.increment(
      { id: record.id },
      'entriesCount',
      count,
    );
  }

  async incrementProjectUnitsSoldCount(
    projectId: string,
    date: Date,
    count = 1,
  ) {
    const record = await this.projectStatsRepo.findOneBy({
      projectId,
      contactSaleSubmissionId: IsNull(),
      date,
    });

    if (!record) {
      return await this.projectStatsRepo.save({
        projectId,
        date,
        unitsSoldCount: Math.max(0, count),
      });
    }
    if (count > 0) {
      return this.projectStatsRepo.increment(
        { id: record.id },
        'unitsSoldCount',
        count,
      );
    }
    return this.projectStatsRepo.increment(
      { id: record.id, unitsSoldCount: MoreThan(0) },
      'unitsSoldCount',
      count,
    );
  }

  async incrementUnitTypeEntriesCount(
    unitTypeId: string,
    contactSaleSubmissionId: string,
    date: Date,
    count = 1,
  ) {
    const record = await this.unitTypeStatsRepo.findOneBy({
      unitTypeId,
      contactSaleSubmissionId,
      date,
      deletedAt: IsNull(),
    });

    if (!record) {
      return await this.unitTypeStatsRepo.save({
        unitTypeId,
        contactSaleSubmissionId,
        date,
        entriesCount: count,
      });
    }

    return await this.unitTypeStatsRepo.increment(
      { id: record.id },
      'entriesCount',
      count,
    );
  }

  async incrementPriceRangeEntriesCount(
    priceRange: {
      minPrice: number;
      maxPrice: number;
    },
    count = 1,
  ) {
    const date = moment().startOf('date').toDate();
    const ranges = PriceRangeConfigs.filter(([min, max]) => {
      return priceRange.minPrice < max && priceRange.maxPrice > min;
    });

    const records = await this.priceRangeStatsRepo.find({
      where: {
        date,
        minPrice: In(ranges.map(([min]) => min)),
      },
    });

    const needCreateNewRecords = ranges
      .filter(([min]) => !records.some((r) => r.minPrice === min))
      .map(([min, max]) => {
        return {
          minPrice: min,
          maxPrice: max,
          date,
        };
      });

    const newRecords = needCreateNewRecords.length
      ? await this.priceRangeStatsRepo.save(needCreateNewRecords)
      : [];

    return await this.priceRangeStatsRepo.increment(
      {
        id: In([
          ...records.map((item) => item.id),
          ...newRecords.map((item) => item.id),
        ]),
      },
      'entriesCount',
      count,
    );
  }

  async deleteProjectEntriesCount(contactSaleSubmissionId: string) {
    return await this.projectStatsRepo.update(
      { contactSaleSubmissionId },
      { deletedAt: new Date() },
    );
  }

  async deleteUnitTypeEntriesCount(contactSaleSubmissionId: string) {
    return await this.unitTypeStatsRepo.update(
      { contactSaleSubmissionId },
      { deletedAt: new Date() },
    );
  }

  async getProjectStats(query: QueryProjectStatsDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException(
          "'startDate' must be less than 'endDate'",
        );
      }
    }

    const lang = query.lang || ELangCode.en;
    const pagination = getPaginationOption(query);

    const orderBy = query.sortBy || EProjectStatsSortBy.EntriesCount;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.projectStatsRepo
      .createQueryBuilder('projectStats')
      .select('SUM(projectStats.viewsCount)', 'viewsCount')
      .addSelect('SUM(projectStats.entriesCount)', 'entriesCount')
      .addSelect('SUM(projectStats.unitsSoldCount)', 'unitsSoldCount')
      .where('projectStats.deletedAt IS NULL');

    if (query.startDate) {
      queryBuilder.andWhere('projectStats.createdAt >= :startDate', {
        startDate: query.startDate.toISOString(),
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('projectStats.createdAt <= :endDate', {
        endDate: query.endDate.toISOString(),
      });
    }

    queryBuilder.innerJoin(
      'projectStats.project',
      'project',
      'project.deletedAt IS NULL',
    );

    const totalCount = await queryBuilder.getRawOne();

    queryBuilder
      .addSelect('project.id', 'id')
      .addSelect(createMultilingualSelect('project', 'name', lang), 'name')
      .groupBy('project.id')
      .limit(pagination.limit)
      .offset(pagination.offset)
      .orderBy(`"${orderBy}"`, sorting);

    const [data, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    return {
      totalCount: {
        viewsCount: +totalCount.viewsCount,
        entriesCount: +totalCount.entriesCount,
        unitsSoldCount: +totalCount.unitsSoldCount,
      },
      ...createPaginationResponse(
        data.map((item) => ({
          ...item,
          viewsCount: +item.viewsCount,
          entriesCount: +item.entriesCount,
          unitsSoldCount: +item.unitsSoldCount,
        })),
        total,
        pagination,
      ),
    };
  }

  async getUnitTypeStats(query: QueryUnitTypeStatsDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException("'startDate' must less than 'endDate'");
      }
    }

    const lang = query.lang || ELangCode.en;
    const pagination = getPaginationOption(query);

    const orderBy = query.sortBy || EUnitTypeStatsSortBy.EntriesCount;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.unitTypeStatsRepo
      .createQueryBuilder('unitTypeStats')
      .select('SUM(unitTypeStats.entriesCount)', 'entriesCount')
      .where('unitTypeStats.deletedAt IS NULL');

    if (query.startDate) {
      queryBuilder.andWhere('unitTypeStats.createdAt >= :startDate', {
        startDate: query.startDate.toISOString(),
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('unitTypeStats.createdAt <= :endDate', {
        endDate: query.endDate.toISOString(),
      });
    }
    const totalCount = await queryBuilder.getRawOne();

    queryBuilder
      .innerJoin('unitTypeStats.unitType', 'unitType')
      .addSelect('unitType.id', 'id')
      .addSelect(createMultilingualSelect('unitType', 'title', lang), 'title')
      .groupBy('unitType.id')
      .limit(pagination.limit)
      .offset(pagination.offset)
      .orderBy(`"${orderBy}"`, sorting);

    const [data, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    return {
      totalCount: {
        entriesCount: +totalCount.entriesCount,
      },
      ...createPaginationResponse(
        data.map((item) => ({
          ...item,
          entriesCount: +item.entriesCount,
        })),
        total,
        pagination,
      ),
    };
  }

  async getPriceRangeStats(query: QueryPriceRangeStatsDto) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException("'startDate' must less than 'endDate'");
      }
    }

    const pagination = getPaginationOption(query);

    const orderBy = query.sortBy || EUnitTypeStatsSortBy.EntriesCount;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.priceRangeStatsRepo
      .createQueryBuilder('priceRangeStats')
      .select('SUM(priceRangeStats.entriesCount)', 'entriesCount');

    if (query.startDate) {
      queryBuilder.andWhere('priceRangeStats.date >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('priceRangeStats.date <= :endDate', {
        endDate: query.endDate,
      });
    }
    const totalCount = await queryBuilder.getRawOne();

    queryBuilder
      .addSelect('priceRangeStats.id', 'id')
      .addSelect('priceRangeStats.minPrice', 'minPrice')
      .addSelect('priceRangeStats.maxPrice', 'maxPrice')
      .groupBy('priceRangeStats.id')
      .limit(pagination.limit)
      .offset(pagination.offset)
      .orderBy(`"${orderBy}"`, sorting);

    const [data, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount(),
    ]);

    return {
      totalCount: {
        entriesCount: +totalCount.entriesCount,
      },
      ...createPaginationResponse(
        data.map((item) => ({
          ...item,
          entriesCount: +item.entriesCount,
        })),
        total,
        pagination,
      ),
    };
  }

  async getProjectStatsContactSalesSubmission(
    query: QueryProjectStatsDto,
    userId: string,
  ) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException("'startDate' must less than 'endDate'");
      }
    }

    const queryBuilder = this.contactSaleSubmissionRepo
      .createQueryBuilder('contactSaleSubmission')
      .leftJoinAndSelect('contactSaleSubmission.project', 'project')
      .leftJoinAndSelect('contactSaleSubmission.user', 'user')
      .where('user.id = :userId', { userId });

    if (query.startDate) {
      queryBuilder.andWhere('contactSaleSubmission.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('contactSaleSubmission.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    const contactSaleSubmissions = await queryBuilder.getMany();

    if (!contactSaleSubmissions.length) {
      return {
        totalCount: {
          entriesCount: 0,
        },
        projectStats: {},
      };
    }

    const projectStats = {};
    const totalCount = {
      entriesCount: 0,
    };

    contactSaleSubmissions.forEach((contactSaleSubmission) => {
      if (!contactSaleSubmission?.project?.id) {
        return;
      }

      if (!projectStats[contactSaleSubmission.project.id]) {
        projectStats[contactSaleSubmission.project.id] = {
          viewsCount: 0,
          entriesCount: 1,
          unitsSoldCount: 0,
          name: contactSaleSubmission.project.name.en,
        };
      } else {
        projectStats[contactSaleSubmission.project.id].entriesCount += 1;
      }

      totalCount.entriesCount += 1;
    });

    const items = [];
    for (const projectId in projectStats) {
      const projectStat = projectStats[projectId];
      items.push({
        projectId,
        ...projectStat,
      });
    }
    return { totalCount, items };
  }

  async getUnitTypeStatsContactSalesSubmission(
    query: QueryUnitTypeStatsDto,
    userId: string,
  ) {
    if (query.startDate && query.endDate) {
      if (query.startDate.getTime() > query.endDate.getTime()) {
        throw new BadRequestException("'startDate' must less than 'endDate'");
      }
    }

    const queryBuilder = this.contactSaleSubmissionRepo
      .createQueryBuilder('contactSaleSubmission')
      .leftJoinAndSelect('contactSaleSubmission.user', 'user')
      .where('user.id = :userId', { userId });

    if (query.startDate) {
      queryBuilder.andWhere('contactSaleSubmission.createdAt >= :startDate', {
        startDate: query.startDate,
      });
    }

    if (query.endDate) {
      queryBuilder.andWhere('contactSaleSubmission.createdAt <= :endDate', {
        endDate: query.endDate,
      });
    }

    const contactSaleSubmissions = await queryBuilder.getMany();

    if (!contactSaleSubmissions.length) {
      return {
        totalCount: {
          entriesCount: 0,
        },
        unitTypeStats: {},
      };
    }

    const unitTypeStats = {};
    const totalCount = {
      entriesCount: 0,
    };

    contactSaleSubmissions.forEach((contactSaleSubmission) => {
      const { unitTypes } = contactSaleSubmission;

      if (unitTypes) {
        let parsedUnitTypes;

        try {
          parsedUnitTypes = JSON.parse(unitTypes);
        } catch (error) {
          console.warn(`Invalid JSON in unitTypes: ${unitTypes}`, error);
          parsedUnitTypes = [];
        }

        if (Array.isArray(parsedUnitTypes)) {
          parsedUnitTypes.forEach((unitType: string) => {
            if (!unitTypeStats[unitType]) {
              unitTypeStats[unitType] = {
                entriesCount: 1,
              };
            } else {
              unitTypeStats[unitType].entriesCount += 1;
            }
            totalCount.entriesCount += 1;
          });
        } else {
          console.warn(`Parsed unitTypes is not an array: ${parsedUnitTypes}`);
        }
      }
    });

    const items = [];
    for (const unitTypeTitle in unitTypeStats) {
      const unitTypeStat = unitTypeStats[unitTypeTitle];
      items.push({
        unitTypeTitle,
        ...unitTypeStat,
      });
    }

    return { totalCount, items };
  }
}
