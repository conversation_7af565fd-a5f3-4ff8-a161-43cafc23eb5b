import { Modu<PERSON> } from '@nestjs/common';
import { AnalyticController } from './analytic.controller';
import { AnalyticService } from './analytic.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectStats } from './entities/project-stats.entity';
import { Project } from 'src/project/entities/project.entity';
import { UnitTypeStats } from './entities/unit-type.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import { ProjectModule } from 'src/project/project.module';
import { RedisModule } from 'src/redis/redis.module';
import { PriceRangeStats } from './entities/price-range-stats.entity';
import { UnitTransaction } from 'src/unit-transaction/entities/unit-transaction.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProjectStats,
      Project,
      UnitTypeStats,
      UnitType,
      PriceRangeStats,
      UnitTransaction,
      ContactSaleSubmission,
    ]),
    ProjectModule,
    RedisModule,
  ],
  controllers: [AnalyticController],
  providers: [AnalyticService],
  exports: [AnalyticService],
})
export class AnalyticModule {}
