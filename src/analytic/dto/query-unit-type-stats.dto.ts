import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EUnitTypeStatsSortBy {
  EntriesCount = 'entriesCount',
}

export class QueryUnitTypeStatsDto extends PaginationQueryDto {
  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EUnitTypeStatsSortBy)
  @IsOptional()
  sortBy?: EUnitTypeStatsSortBy;

  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsDate()
  @Type(() => Date)
  endDate: Date;
}
