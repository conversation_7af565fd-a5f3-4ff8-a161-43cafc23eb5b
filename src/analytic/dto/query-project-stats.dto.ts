import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EProjectStatsSortBy {
  ViewsCount = 'viewCount',
  EntriesCount = 'entriesCount',
  SoldCount = 'unitsSoldCount',
}

export class QueryProjectStatsDto extends PaginationQueryDto {
  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EProjectStatsSortBy)
  @IsOptional()
  sortBy?: EProjectStatsSortBy;

  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsDate()
  @Type(() => Date)
  endDate: Date;
}
