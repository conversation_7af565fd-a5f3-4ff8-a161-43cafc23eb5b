import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EPriceRangeStatsSortBy {
  EntriesCount = 'entriesCount',
}

export class QueryPriceRangeStatsDto extends PaginationQueryDto {
  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EPriceRangeStatsSortBy)
  @IsOptional()
  sortBy?: EPriceRangeStatsSortBy;

  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsDate()
  @Type(() => Date)
  endDate: Date;
}
