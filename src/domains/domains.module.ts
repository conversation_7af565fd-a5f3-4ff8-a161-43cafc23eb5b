import { Modu<PERSON> } from '@nestjs/common';
import { DomainsService } from './domains.service';
import { DomainsController } from './domains.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Domain } from './entities/domain.entity';
import { RedisModule } from 'src/redis/redis.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { UmamiModule } from 'src/umami/umami.module';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { UserProjectLandingPage } from 'src/user-project-landing-page/entities/user-project-landing-page.entity';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Domain,
      UserConfig,
      UmamiMapping,
      UmamiWebsites,
      UserProjectLandingPage,
    ]),
    RedisModule,
    UmamiModule,
    ConfigModule,
  ],
  controllers: [DomainsController],
  providers: [DomainsService],
  exports: [DomainsService],
})
export class DomainsModule {}
