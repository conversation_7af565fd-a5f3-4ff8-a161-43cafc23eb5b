import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  Header,
} from '@nestjs/common';
import { DomainsService } from './domains.service';
import { UpdateDomainDto } from './dto/update-domain.dto';
import { CheckDomainDto } from './dto/check-domain.dto';
import { CreateDomainDto } from './dto/create-domain.dto';
import { GetAllDomainDto } from './dto/get-all-domain.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';

@Controller('domains')
@CacheOption('domain')
export class DomainsController {
  constructor(private readonly domainsService: DomainsService) {}

  @Post()
  create(@Body() createDomainDto: CreateDomainDto) {
    return this.domainsService.create(createDomainDto);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateDomainDto: UpdateDomainDto) {
    return this.domainsService.update(id, updateDomainDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.domainsService.remove(id);
  }

  @Get('check')
  findOne(@Query() query: CheckDomainDto) {
    return this.domainsService.checkDomain(query.domainName, query.configId);
  }

  @Get('check-cname-connection')
  getCname(@Query() query: { domain: string }) {
    return this.domainsService.checkCNAMEFromTerminal(query.domain);
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  listing(@ActiveUser('sub') userId: string, @Query() query: GetAllDomainDto) {
    return this.domainsService.listing(userId, query);
  }
}
