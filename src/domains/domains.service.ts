import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateDomainDto } from './dto/create-domain.dto';
import { UpdateDomainDto } from './dto/update-domain.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Domain } from './entities/domain.entity';
import { Repository } from 'typeorm';
import { GetAllDomainDto } from './dto/get-all-domain.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { RedisService } from 'src/redis/redis.service';
import { ConfigService, ConfigType } from '@nestjs/config';
import domainConfig from 'src/core/configs/domain.config';
import { readFileSync } from 'fs';
import * as path from 'path';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UmamiMapping } from 'src/umami/entities/umami-mapping.entity';
import { UmamiService } from 'src/umami/umami.service';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { UserProjectLandingPage } from 'src/user-project-landing-page/entities/user-project-landing-page.entity';
// Built-in nodejs
import { exec } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';

const execAsync = promisify(exec);

@Injectable()
export class DomainsService {
  get baseDomain() {
    return this.domainConf.baseDomain;
  }

  constructor(
    @InjectRepository(Domain)
    private readonly domainRepository: Repository<Domain>,
    @InjectRepository(UserProjectLandingPage)
    private readonly landingPageRepo: Repository<UserProjectLandingPage>,
    private readonly redisService: RedisService,
    @Inject(domainConfig.KEY)
    private readonly domainConf: ConfigType<typeof domainConfig>,

    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    @InjectRepository(UmamiMapping)
    private readonly umamiMappingRepo: Repository<UmamiMapping>,

    private readonly umamiService: UmamiService,

    @InjectRepository(UmamiWebsites)
    private readonly umamiWebsitesRepo: Repository<UmamiWebsites>,

    private readonly configService: ConfigService,
  ) {}

  async create(createDomainDto: CreateDomainDto) {
    const domain = await this.domainRepository.findOne({
      where: {
        configId: createDomainDto.configId,
        name: createDomainDto.name,
      },
    });

    if (domain) {
      if (createDomainDto.primary === true) {
        const domainsByConfigId = await this.domainRepository.find({
          where: { configId: createDomainDto.configId },
        });

        const domainsIds = domainsByConfigId.map((domain) => domain.id);

        for (const domainId of domainsIds) {
          await this.domainRepository.update(
            { id: domainId },
            { primary: false },
          );
        }

        await this.domainRepository.update(domain.id, {
          ...createDomainDto,
          primary: true,
        });
      } else {
        await this.domainRepository.update(domain.id, {
          ...createDomainDto,
          primary: false,
        });
      }

      return await this.domainRepository.findOne({ where: { id: domain.id } });
    }

    await this.throwIfExist(createDomainDto.name);

    await this.redisService.deletePattern('domain@*');

    if (createDomainDto?.primary === true) {
      const domainsByConfigId = await this.domainRepository.find({
        where: { configId: createDomainDto.configId },
      });

      const domainsIds = domainsByConfigId.map((domain) => domain.id);

      for (const domainId of domainsIds) {
        await this.domainRepository.update(
          { id: domainId },
          { primary: false },
        );
      }
    }

    const createdDomain = await this.domainRepository.save(createDomainDto);

    // try {
    //   const userConfig = await this.userConfigRepo.findOne({
    //     where: { id: createDomainDto.configId },
    //     relations: ['user'],
    //   });

    //   const umamiUserMapping = await this.umamiMappingRepo.findOne({
    //     where: { user: { id: userConfig?.user?.id } },
    //   });

    //   const createdWebsite = await this.umamiService.createWebsiteByUser({
    //     usernname: umamiUserMapping.umamiUserName,
    //     password: umamiUserMapping.umamiPassword,
    //     domain: createDomainDto.name,
    //     name: `${userConfig?.user?.email} ${createDomainDto.name}`,
    //   });

    //   await this.umamiWebsitesRepo.save({
    //     userId: userConfig?.user?.id,
    //     domainId: createdDomain.id,
    //     umamiWebsiteUuid: createdWebsite.id,
    //     umamiWebsiteId: createdWebsite.id,
    //     umamiWebsiteName: createdWebsite.name,
    //     umamiWebsiteDomain: createdWebsite.domain,
    //     umamiWebsiteShareId: createdWebsite.shareId,
    //     umamiWebsiteTrackingCode: `<script defer src="${this.umamiService.umamiHost}/script.js" data-website-id="${createdWebsite.id}"></script>`,
    //   });
    // } catch (error) {
    //   console.log(error);
    // }

    return createdDomain;
  }

  async update(id: string, updateDomainDto: UpdateDomainDto) {
    const domain = await this.domainRepository.findOneBy({ id });

    if (!domain) {
      throw new NotFoundException('domain is not found');
    }

    if (updateDomainDto?.name) {
      await this.throwIfExist(updateDomainDto.name);
      await this.redisService.deletePattern('domain@*');
    }

    if (updateDomainDto?.primary === true) {
      const { configId } = domain;
      const domainsByConfigId = await this.domainRepository.find({
        where: { configId },
      });

      const domainsIds = domainsByConfigId.map((domain) => domain.id);

      for (const domainId of domainsIds) {
        await this.domainRepository.update(
          {
            id: domainId,
          },
          { primary: false },
        );
      }
    }

    await this.domainRepository.update(
      { id },
      {
        ...domain,
        ...updateDomainDto,
      },
    );

    return await this.domainRepository.findOne({ where: { id } });
  }

  async remove(id: string) {
    const domain = await this.domainRepository.findOneBy({ id });

    if (!domain) {
      throw new NotFoundException('domain is not found');
    }

    if (domain.primary) {
      throw new BadRequestException('Cannot remove primary domain');
    }

    await this.redisService.deletePattern('domain@*');
    return await this.domainRepository.remove(domain);
  }

  async throwIfExist(domainName: string) {
    const domain = await this.domainRepository.findOneBy({ name: domainName });

    if (domain) {
      throw new BadRequestException('This domain is not available');
    }

    return;
  }

  async listing(userId: string, query: GetAllDomainDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.domainRepository
      .createQueryBuilder('domain')
      .leftJoin('domain.config', 'config')
      .where('config.userId = :userId', { userId });

    if (query.name) {
      queryBuilder.andWhere('domain.name ILIKE :domainName', {
        domainName: `%${query.name}%`,
      });
    }
    const [data, total] = await queryBuilder
      .take(pagination.limit)
      .skip(pagination.offset)
      .getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async checkDomain(domain: string, configId?: string) {
    // Check in agency domains
    const agencyDomain = await this.domainRepository.findOne({
      where: [
        {
          name: domain,
          configId: configId ? configId : undefined,
        },
        {
          name: domain,
        },
      ],
    });

    // Check in landing page domains
    const ldpDomain = await this.landingPageRepo
      .createQueryBuilder('landingPage')
      .where('landingPage.subDomain = :domain', { domain })
      .orWhere('landingPage.cNameTargetDomain = :domain', { domain })
      .orWhere('landingPage.customDomain = :domain', { domain })
      .orWhere('landingPage.domain = :domain', { domain })
      .getExists();

    // Domain exists in either table
    if (agencyDomain || ldpDomain) {
      if (configId) {
        const domainsByConfigId = await this.domainRepository.find({
          where: { configId },
        });

        if (domainsByConfigId) {
          return 'This domain is already in use';
        }
      } else {
        return 'This domain is already in use';
      }
    }

    if (domain.endsWith(this.baseDomain)) {
      this.validateSubdomain(domain);
    }

    return this.throwIfExist(domain);
  }

  validateSubdomain(domain: string) {
    if (!domain.endsWith(this.baseDomain)) {
      throw new BadRequestException(
        `Domain should be end with '.${this.baseDomain}'`,
      );
    }

    const subdomain = domain.substring(
      0,
      domain.length - this.baseDomain.length - 1,
    );

    if (!subdomain) {
      throw new BadRequestException('This domain is not available');
    }

    if (/[^A-Za-z0-9\-]/.test(subdomain)) {
      throw new BadRequestException(
        'Subdomain should only contain letters, numbers, and hyphens.',
      );
    }

    return this.validateBlacklist(subdomain);
  }

  private validateBlacklist(subdomain: string) {
    const blacklistFilepath = path.join(
      __dirname,
      '..',
      '..',
      'resources',
      'subdomain-blacklist.csv',
    );

    const raw = readFileSync(blacklistFilepath).toString('utf-8');
    const blacklist = raw.split(/\r?\n/);
    for (const item of blacklist) {
      if (subdomain === item) {
        throw new BadRequestException('This subdomain is not available');
      }
    }

    return true;
  }

  async checkCNAMEFromTerminal(domain: string): Promise<string | null> {
    // Return cname connected to domain or null if not found

    const platform = os.platform(); // e.g. 'win32', 'darwin', 'linux'
    let command = `dig ${domain} CNAME +short`;

    // // Use platform-appropriate commands
    // if (platform === 'win32') {
    //   // Windows: use nslookup
    //   command = `nslookup -type=cname ${domain}`;
    // } else if (platform === 'darwin' || platform === 'linux') {
    //   // macOS or Linux: try dig first
    //   command = `dig ${domain} CNAME +short`;
    // } else {
    //   console.log(`Unsupported platform: ${platform}`);
    //   return null;
    // }

    try {
      const { stdout } = await execAsync(command);
      console.log(`Raw output for ${domain}:\n${stdout}`);

      let rawCname: string | null = null;

      if (command.includes('dig')) {
        // dig output usually returns just the CNAME value
        rawCname = stdout.trim();
      }

      if (command.includes('nslookup')) {
        const match = stdout.match(/canonical name = (.+)/i);
        rawCname = match ? match[1].trim() : null;
      }

      // Final cleanup
      if (rawCname) {
        // Remove "www." prefix if exists and trailing dot
        const cleaned = rawCname
          .replace(/^www\./i, '') // remove leading www.
          .replace(/\.$/, ''); // remove trailing dot
        return cleaned;
      }

      return null;
    } catch (error) {
      console.log(`Command failed for ${domain}: ${error.message}`);
      return null;
    }
  }

  public async getDomainsByConfigId(configId: string) {
    return await this.domainRepository.find({
      where: { configId },
    });
  }

  public async getDomainByName(name: string) {
    return await this.domainRepository.findOne({
      where: { name },
    });
  }

  async generateUniqueAgencySubdomain(userId: string, email: string) {
    for (let i = 0; i < 5; i++) {
      try {
        const randomString = Math.random().toString(36).substring(2, 15);
        const emailSubdomain = this.emailToSubdomain(email);
        const domain =
          `${randomString}-${emailSubdomain.toLocaleLowerCase()}-${userId.slice(0, 4)}.${this.configService.get('SUFFIX_DOMAIN')}`.replace(
            /\s/g,
            '-',
          );
        try {
          await this.checkDomain(this.toValidSubdomain(domain));
          return domain;
        } catch (error) {}
      } catch (error) {
        console.log('Error', error);
      }
      throw new Error('Failed to generate a unique subdomain after 5 tries');
    }
  }

  public emailToSubdomain(email: string) {
    const prefix = email.split('@')[0]; // Get the email prefix

    return prefix
      .normalize('NFKD') // Normalize to decomposed form
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritic marks
      .replace(/đ/g, 'd') // Convert Vietnamese-specific characters
      .replace(/[^a-z0-9-]/gi, '') // Remove invalid characters
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
      .toLowerCase()
      .slice(0, 63); // Limit to 63 characters (subdomain max length)
  }

  public toValidSubdomain(str: string) {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '') // Remove invalid characters
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
      .slice(0, 63); // Limit to 63 characters (subdomain max length)
  }
}
