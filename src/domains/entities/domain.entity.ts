import CrudEntity from 'src/core/entities/crud.entity';
import { UmamiWebsites } from 'src/umami/entities/umami-websites.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class Domain extends CrudEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  privateKey?: string;

  @Column({
    nullable: true,
    default: false,
  })
  primary?: boolean;

  @Column({ nullable: true })
  publicKey?: string;

  @Column({ type: 'uuid' })
  configId: string;

  @ManyToOne(() => UserConfig, (config) => config.domains)
  config: UserConfig;

  @OneToOne(() => UmamiWebsites)
  umamiWebsites?: UmamiWebsites;
}
