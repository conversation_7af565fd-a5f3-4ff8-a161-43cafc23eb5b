import { InjectQueue } from '@nestjs/bull';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { Queue } from 'bull';
import * as TelegramBot from 'node-telegram-bot-api';

@Injectable()
export class TelegramService implements OnModuleInit {
  public bot: TelegramBot;

  // Define secondary bot
  public secondaryBot: TelegramBot;

  private tokenIndex: number;
  private readonly tokens: string[] = [
    process.env.TELEGRAM_BOT_TOKEN_1, // First token
    process.env.TELEGRAM_BOT_TOKEN_2, // Second token prod 2
  ];

  constructor(
    @InjectQueue('telegramQueue') private readonly telegramQueue: Queue,
  ) {
    this.tokenIndex = 0;
    if (process.env.NODE_ENV === 'development') {
      this.initializeBotDev();

      // Init secondary bot
      this.initializeSecondaryBot();
    } else {
      this.initializeBot();

      // Init secondary bot
      this.initializeSecondaryBot();
    }
  }

  private async initializeBotDev() {
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, {
      polling: true,
    });

    console.log('Bot initialized with token:', process.env.TELEGRAM_BOT_TOKEN);
  }

  // Init secondary bot
  private async initializeSecondaryBot() {
    this.secondaryBot = new TelegramBot(
      process.env.TELEGRAM_SECONDARY_BOT_TOKEN,
      {
        polling: true,
      },
    );

    console.log(
      'Secondary Bot initialized with token:',
      process.env.TELEGRAM_SECONDARY_BOT_TOKEN,
    );
  }

  private initializeBot() {
    const currentToken = this.tokens[this.tokenIndex];

    this.bot = new TelegramBot(currentToken, { polling: true });

    console.log(`Bot initialized with token: ${currentToken}`);

    console.log('bot 🤖', this.bot);

    this.bot.on('polling_error', (error) => {
      console.log('Polling error:', error.message);
      this.switchToken();
    });
  }

  private getRandomNumber = (): number => {
    return Math.floor(Math.random() * 5) + 1;
  };

  private async switchToken() {
    // Stop the current bot if it's running

    if (this.bot) {
      console.log(`Stopping bot with token ${this.tokens[this.tokenIndex]}`);
      await this.bot.stopPolling(); // Stop polling for updates
      this.bot.removeAllListeners(); // Remove all event listeners
    }

    this.tokenIndex = (this.tokenIndex + 1) % this.tokens.length; // Move to the next token, wrap around if necessary
    // add a small delay time to avoid 2 cluster start at the same time
    const delayTime = this.getRandomNumber() * 100;
    console.log(`Switching token in ${delayTime}ms`);

    setTimeout(() => {
      this.initializeBot(); // Reinitialize the bot after the wait time
    }, delayTime);
  }

  async onModuleInit() {
    this.bot.on('new_chat_members', async (msg) => {
      try {
        const chatId = msg?.chat?.id;
        const groupName = msg?.chat?.title;
        await this.bot.sendMessage(
          chatId,
          `Hello, I have been added to group: ${groupName}. The chat ID is: ${chatId}`,
        );
      } catch (error) {
        console.error(
          `Error sending message on new_chat_members:`,
          error.message,
        );
      }
    });

    // Turn on secondary bot
    this.secondaryBot.on('new_chat_members', async (msg) => {
      try {
        const chatId = msg?.chat?.id;
        const groupName = msg?.chat?.title;
        await this.secondaryBot.sendMessage(
          chatId,
          `Hello, I have been added to group: ${groupName}. The chat ID is: ${chatId}`,
        );
      } catch (error) {
        console.error(
          `Error sending message on new_chat_members:`,
          error.message,
        );
      }
    });
  }

  async sendMessage(
    chatId: string,
    message: string,
    options?: { parse_mode?: TelegramBot.ParseMode; isPin?: boolean },
    ipTrackingId?: string,
  ) {
    try {
      await this.telegramQueue.add(
        'sendMessage',
        { chatId, message, options, ipTrackingId },
        {
          removeOnComplete: true,
          removeOnFail: true,
        },
      );
    } catch (error) {
      console.error('Send message - Error adding job to queue:', error.message);
    }
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
