import { Module } from '@nestjs/common';
import { TelegramService } from './telegram.service';
import { RedisModule } from 'src/redis/redis.module';
import { BullModule } from '@nestjs/bull';
import { TelegramQueueProcessor } from './queue.consumer';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([IpTracking]),
    RedisModule,
    BullModule.registerQueue({
      name: 'telegramQueue',
      limiter: {
        max: 30,
        duration: 1000,
      },
    }),
  ],
  providers: [TelegramService, TelegramQueueProcessor],
  exports: [TelegramService],
})
export class TelegramModule {}
