import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { TelegramService } from './telegram.service';
import { InjectRepository } from '@nestjs/typeorm';
import { IpTracking } from 'src/ip-tracking/entities/ip-tracking.entity';
import { Repository } from 'typeorm';

@Processor('telegramQueue')
export class TelegramQueueProcessor {
  constructor(
    private readonly telegramService: TelegramService,
    @InjectRepository(IpTracking)
    private readonly ipTrackingRepo: Repository<IpTracking>,
  ) {}

  @Process('sendMessage')
  async handleSendMessage(
    job: Job<{
      chatId: string;
      message: string;
      options?: any;
      ipTrackingId?: string;
    }>,
  ) {
    const { chatId, message, options, ipTrackingId } = job.data;

    let primaryBotError = null;
    let secondaryBotError = null;

    try {
      if (options?.isPin) {
        await this.unpinMessages(chatId);
      }

      let sentMessage = null;
      try {
        sentMessage = await this.telegramService.bot.sendMessage(
          chatId,
          message,
          options,
        );
        console.log(`Primary bot sent message to ${chatId}`);
      } catch (error) {
        primaryBotError = error;
        console.warn('Primary bot failed to send message:', error.message);
      }

      let sentMessageSecondaryBot = null;
      if (!sentMessage) {
        try {
          sentMessageSecondaryBot =
            await this.telegramService.secondaryBot.sendMessage(
              chatId,
              message,
              options,
            );
          console.log(`Secondary bot sent message to ${chatId}`);
        } catch (error) {
          secondaryBotError = error;
          console.warn('Secondary bot failed to send message:', error.message);
        }
      }

      if (!sentMessage && !sentMessageSecondaryBot) {
        console.error('Both bots failed to send message:', chatId, message, {
          primaryBotError: primaryBotError?.message,
          secondaryBotError: secondaryBotError?.message,
        });
      } else {
        if (options?.isPin) {
          try {
            if (sentMessage) {
              await this.telegramService.bot.pinChatMessage(
                chatId,
                sentMessage.message_id,
              );
            }
            if (sentMessageSecondaryBot) {
              await this.telegramService.secondaryBot.pinChatMessage(
                chatId,
                sentMessageSecondaryBot.message_id,
              );
            }
          } catch (error) {
            console.error('Error pinning messages:', error.message);
          }
        }

        await this.ipTrackingRepo.update(
          { id: ipTrackingId },
          { isSent: true },
        );
      }
    } catch (generalError) {
      console.error(
        'Unexpected error in handleSendMessage:',
        generalError.message,
      );
    }
  }

  private async unpinMessages(chatId: string) {
    try {
      await this.telegramService.bot.unpinChatMessage(chatId);
      await this.telegramService.secondaryBot.unpinChatMessage(chatId);
    } catch (error) {
      console.error('Error unpinning messages:', error.message);
    }
  }

  private async pinMessages(chatId: string, primaryMessage, secondaryMessage) {
    try {
      await this.telegramService.bot.pinChatMessage(
        chatId,
        primaryMessage.message_id,
      );
      await this.telegramService.secondaryBot.pinChatMessage(
        chatId,
        secondaryMessage.message_id,
      );
    } catch (error) {
      console.error('Error pinning messages:', error.message);
    }
  }
}
