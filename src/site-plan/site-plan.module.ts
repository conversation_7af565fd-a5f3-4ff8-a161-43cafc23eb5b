import { Module } from '@nestjs/common';
import { SitePlanService } from './site-plan.service';
import { SitePlanController } from './site-plan.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SitePlan } from './entities/site-plan.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SitePlan])],
  controllers: [SitePlanController],
  providers: [SitePlanService],
  exports: [SitePlanService],
})
export class SitePlanModule {}
