import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ELangCode } from 'src/core/enums/lang.enum';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { generateSlug } from 'src/core/utils/slug.util';
import { SitePlan } from './entities/site-plan.entity';
import { CreateSitePlanDto } from './dto/create-site-plan.dto';
import { UpdateSitePlanDto } from './dto/update-site-plan.dto';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { ESitePlanSortBy, QuerySitePlanDto } from './dto/query-site-plan.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EOrderType } from 'src/core/enums/sort.enum';

@Injectable()
export class SitePlanService {
  constructor(
    @InjectRepository(SitePlan)
    private readonly sitePlanRepo: Repository<SitePlan>,
  ) {}

  async create(body: CreateSitePlanDto) {
    if (body.slug) {
      const existed = await this.sitePlanRepo.findOneBy({ slug: body.slug });
      if (existed) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }
    const slug =
      body.slug ?? (await generateSlug(body.title, this.sitePlanRepo));

    const data: Partial<SitePlan> = {
      ...body,
      slug,
      title: { [ELangCode.en]: body.title },
    };

    return await this.sitePlanRepo.save(data);
  }

  async getDetail(id: string) {
    const item = await this.sitePlanRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Location is not found');
    }

    return item;
  }

  async listing(query: QuerySitePlanDto) {
    const pagination = getPaginationOption(query);
    const orderBy = query.sortBy || ESitePlanSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.sitePlanRepo
      .createQueryBuilder('sitePlan')
      .where('sitePlan.projectId = :projectId', { projectId: query.projectId })
      .orderBy(`sitePlan.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.title) {
      queryBuilder.andWhere(
        `"sitePlan"."title"->>'${ELangCode.en}' ILIKE :title`,
        { title: `%${query.title}%` },
      );
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async update(id: string, body: UpdateSitePlanDto) {
    const item = await this.sitePlanRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Site plan is not found');
    }

    const langCode = body.lang ?? ELangCode.en;

    if (body.slug && body.slug !== item.slug) {
      const existed = await this.sitePlanRepo.findOneBy({ slug: body.slug });
      if (existed && existed.id !== item.id) {
        throw new UnprocessableEntityException(
          `Slug '${body.slug}' has already used by another`,
        );
      }
    }

    const data: Partial<SitePlan> = {
      ...item,
      ...body,
      title: {
        ...item.title,
        [langCode]: body.title ?? item.title?.[langCode],
      },
    };

    return await this.sitePlanRepo.save(data);
  }

  async delete(id: string) {
    const item = await this.sitePlanRepo.findOneBy({ id });
    if (!item) {
      throw new NotFoundException('Site plan is not found');
    }

    return await this.sitePlanRepo.update(
      { id },
      { deletedAt: new Date(), slug: null },
    );
  }

  formatResponse(item: SitePlan, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      title: getLangValue(lang, item.title, fallback),
    };
  }
}
