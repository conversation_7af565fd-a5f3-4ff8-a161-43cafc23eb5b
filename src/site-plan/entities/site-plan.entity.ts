import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class SitePlan extends CrudEntity {
  @Column({ type: 'jsonb' })
  title?: Record<string, string>;

  @Column({ unique: true, nullable: true })
  slug: string;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project, (project) => project.sitePlans)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid', nullable: true })
  photoId?: string;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'photoId' })
  photo: Asset;
}
