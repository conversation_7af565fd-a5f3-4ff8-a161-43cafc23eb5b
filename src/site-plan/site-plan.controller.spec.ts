import { Test, TestingModule } from '@nestjs/testing';
import { SitePlanController } from './site-plan.controller';
import { SitePlanService } from './site-plan.service';

describe('SitePlanController', () => {
  let controller: SitePlanController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SitePlanController],
      providers: [SitePlanService],
    }).compile();

    controller = module.get<SitePlanController>(SitePlanController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
