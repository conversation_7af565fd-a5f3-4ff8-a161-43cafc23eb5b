import { OmitType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum ESitePlanSortBy {
  title = 'title',
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
}

export class QuerySitePlanDto extends OmitType(PaginationQueryDto, ['lang']) {
  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(ESitePlanSortBy)
  @IsOptional()
  sortBy?: ESitePlanSortBy;

  @IsOptional()
  @IsString()
  title?: string;

  @IsUUID()
  projectId: string;
}
