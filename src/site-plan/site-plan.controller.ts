import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { SitePlanService } from './site-plan.service';
import { CreateSitePlanDto } from './dto/create-site-plan.dto';
import { UpdateSitePlanDto } from './dto/update-site-plan.dto';
import { QuerySitePlanDto } from './dto/query-site-plan.dto';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { Roles } from 'src/users/entities/user.entity';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';

@Controller('site-plan')
@AccessRoles(Roles.ADMIN)
@UseGuards(RolesGuard)
export class SitePlanController {
  constructor(private readonly sitePlanService: SitePlanService) {}

  @Post()
  create(@Body() createSitePlanDto: CreateSitePlanDto) {
    return this.sitePlanService.create(createSitePlanDto);
  }

  @Get()
  listing(@Query() query: QuerySitePlanDto) {
    return this.sitePlanService.listing(query);
  }

  @Get(':id')
  getDetail(@Param('id') id: string) {
    return this.sitePlanService.getDetail(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSitePlanDto: UpdateSitePlanDto,
  ) {
    return this.sitePlanService.update(id, updateSitePlanDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.sitePlanService.delete(id);
  }
}
