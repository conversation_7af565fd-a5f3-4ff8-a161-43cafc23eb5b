import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Headers,
  UseInterceptors,
} from '@nestjs/common';
import { VirtualTourService } from './virtual-tour.service';
import { CreateVirtualTourDto } from './dto/create-virtual-tour.dto';
import { UpdateVirtualTourDto } from './dto/update-virtual-tour.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { ListingVirtualTourDto } from './dto/listing-virtual-tour.dto';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { CacheInterceptor } from 'src/redis/interceptors/cache.interceptor';

@Controller('virtual-tour')
@CacheOption('virtual-tour')
export class VirtualTourController {
  constructor(private readonly virtualTourService: VirtualTourService) {}

  @Post()
  create(@Body() createVirtualTourDto: CreateVirtualTourDto) {
    return this.virtualTourService.create(createVirtualTourDto);
  }

  @Get()
  @UseInterceptors(CacheInterceptor)
  listing(@Query() query: ListingVirtualTourDto) {
    return this.virtualTourService.listing(query);
  }

  @Get('by-domain')
  @Public()
  @RequireHeader('User-Domain')
  @UseInterceptors(CacheInterceptor)
  getByDomain(
    @Headers('User-Domain') domain: string,
    @Query() query: ListingVirtualTourDto,
  ) {
    return this.virtualTourService.getByDomain(domain, query);
  }

  @Get(':id')
  @Public()
  findOne(@Param('id') id: string) {
    return this.virtualTourService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateVirtualTourDto: UpdateVirtualTourDto,
  ) {
    return this.virtualTourService.update(id, updateVirtualTourDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.virtualTourService.remove(id);
  }
}
