import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateVirtualTourDto } from './dto/create-virtual-tour.dto';
import { UpdateVirtualTourDto } from './dto/update-virtual-tour.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { VirtualTour } from './entities/virtual-tour.entity';
import { Repository } from 'typeorm';
import { AssetService } from 'src/asset/asset.service';
import { Project } from 'src/project/entities/project.entity';
import { EAssetRelation } from 'src/asset/enums/asset.enum';
import { ListingVirtualTourDto } from './dto/listing-virtual-tour.dto';
import { Domain } from 'domain';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { createMultilingualSelect } from 'src/core/utils/sql.util';
import { ELangCode } from 'src/core/enums/lang.enum';
import { ProjectService } from 'src/project/project.service';
import { getLangValue } from 'src/core/utils/multi-language.ulti';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { RedisService } from 'src/redis/redis.service';

@Injectable()
export class VirtualTourService {
  constructor(
    @InjectRepository(VirtualTour)
    private readonly virtualTourRepo: Repository<VirtualTour>,
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(Domain)
    private readonly domainRepo: Repository<Domain>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    private readonly assetService: AssetService,
    @Inject(forwardRef(() => ProjectService))
    private readonly projectService: ProjectService,
    private readonly unitTypeService: UnitTypeService,
    private readonly redisService: RedisService,
  ) {}

  async create(createVirtualTourDto: CreateVirtualTourDto) {
    const project = await this.projectRepo.findOneBy({
      id: createVirtualTourDto.projectId,
    });

    if (!project) {
      throw new NotFoundException('Project is not found');
    }

    if (createVirtualTourDto.unitTypeId) {
      await this.unitTypeService.getDetail(createVirtualTourDto.unitTypeId);
    }

    const data: Partial<VirtualTour> = {
      ...createVirtualTourDto,
      name: {
        [ELangCode.en]: createVirtualTourDto.name,
      },
    };

    const item = await this.virtualTourRepo.save(data);
    if (createVirtualTourDto.thumbnailId) {
      await this.assetService
        .addAssetRelation(
          createVirtualTourDto.thumbnailId,
          EAssetRelation.VirtualTour,
          project.id,
        )
        .catch(console.error);
    }

    await this.redisService.deletePattern('virtual-tour@*');

    return item;
  }

  async getByDomain(domain: string, query: ListingVirtualTourDto) {
    const userConfig = await this.userConfigRepo
      .createQueryBuilder('userConfig')
      .leftJoin('userConfig.domains', 'domains')
      .where('domains.name = :domainName', { domainName: domain })
      .getOne();

    if (!userConfig) {
      throw new NotFoundException('Domain is not found');
    }

    const lang = query.lang || ELangCode.en;

    const pagination = getPaginationOption(query);
    const queryBuilder = this.virtualTourRepo
      .createQueryBuilder('virtualTour')
      .leftJoinAndSelect('virtualTour.project', 'project')
      .leftJoinAndSelect('project.location', 'location')
      .leftJoinAndSelect('virtualTour.unitType', 'unitType')
      .leftJoinAndSelect('virtualTour.thumbnail', 'thumbnail')
      .leftJoin('project.userProjects', 'userProjects')
      .where('userProjects.userId = :userId', { userId: userConfig.userId })
      .orderBy('project.id')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.keyword) {
      queryBuilder.andWhere(
        `(regexp_replace(${createMultilingualSelect('project', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :keyword OR
        regexp_replace(${createMultilingualSelect('location', 'name', lang)},'[^a-zA-Z0-9 ]', '', 'g') ILIKE :keyword)`,
        { keyword: `%${query.keyword.trim().replace(/[^a-zA-Z0-9 ]/g, '')}%` },
      );
    }

    if (query.projectSlug) {
      queryBuilder.andWhere('project.slug = :slug', {
        slug: query.projectSlug,
      });
    }

    const virtualTourResult = await queryBuilder.getMany();

    const data = [];

    if (query.distinct === 'project') {
      const projectIds = new Set();

      virtualTourResult.forEach((item) => {
        if (!projectIds.has(item.projectId)) {
          projectIds.add(item.projectId);
          data.push(item);
        }
      });
    } else {
      data.push(...virtualTourResult);
    }

    return createPaginationResponse(
      data.map((item) => this.formatResponse(item, lang)),
      data.length,
      pagination,
    );
  }

  async listing(query: ListingVirtualTourDto) {
    const pagination = getPaginationOption(query);
    const queryBuilder = this.virtualTourRepo
      .createQueryBuilder('virtualTour')
      .leftJoinAndSelect('virtualTour.project', 'project')
      .leftJoinAndSelect('virtualTour.unitType', 'unitType')
      .leftJoinAndSelect('virtualTour.thumbnail', 'thumbnail')
      .orderBy('project.id');

    if (query.projectId) {
      queryBuilder.where('project.id = :projectId', {
        projectId: query.projectId,
      });
    }

    if (query.projectSlug) {
      queryBuilder.andWhere('project.slug = :slug', {
        slug: query.projectSlug,
      });
    }

    const virtualTourResult = await queryBuilder.getMany();

    const data = [];

    if (query.distinct === 'project') {
      const projectIds = new Set();

      virtualTourResult.forEach((item) => {
        if (!projectIds.has(item.projectId)) {
          projectIds.add(item.projectId);
          data.push(item);
        }
      });
    } else {
      data.push(...virtualTourResult);
    }

    return createPaginationResponse(data, data.length, pagination);
  }

  async findOne(id: string) {
    const item = await this.virtualTourRepo.findOne({
      where: { id },
      relations: {
        project: true,
        unitType: true,
        thumbnail: true,
      },
    });

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    return item;
  }

  async update(id: string, updateVirtualTourDto: UpdateVirtualTourDto) {
    const item = await this.findOne(id);
    const lang = updateVirtualTourDto.lang || ELangCode.en;

    const data: Partial<VirtualTour> = {
      ...item,
      ...updateVirtualTourDto,
      name: {
        [lang]: updateVirtualTourDto.name,
      },
    };
    const res = await this.virtualTourRepo.save(data);
    try {
      if (updateVirtualTourDto.thumbnailId !== item.thumbnailId) {
        if (item.thumbnailId) {
          await this.assetService.remove(item.thumbnailId).catch(console.error);
        }
        if (updateVirtualTourDto.thumbnailId) {
          await this.assetService.addAssetRelation(
            updateVirtualTourDto.thumbnailId,
            EAssetRelation.VirtualTour,
            item.id,
          );
        }
      }
    } catch (e) {
      console.log('Update virtual thumbnail error', e);
    }

    await this.redisService.deletePattern('virtual-tour@*');

    return res;
  }

  async remove(id: string) {
    const item = await this.findOne(id);

    if (item.thumbnailId) {
      try {
        await this.assetService.remove(item.thumbnailId);
      } catch {}
    }

    await this.redisService.deletePattern('virtual-tour@*');

    return this.virtualTourRepo.softRemove(item);
  }

  formatResponse(item: VirtualTour, lang: ELangCode, fallback = ELangCode.en) {
    return {
      ...item,
      name: getLangValue(lang, item.name, fallback),
      project: item.project
        ? this.projectService.formatResponse(item.project, lang, fallback)
        : item.project,
      unitType: item.unitType
        ? this.unitTypeService.formatResponse(item.unitType, lang, fallback)
        : item.unitType,
    };
  }

  async findByName(projectId: string, name: string) {
    return await this.redisService.cacheWrapper(
      `virtual-tour@name:${name}`,
      async () => {
        return this.virtualTourRepo.findOneBy({
          projectId,
          name: { en: name },
        });
      },
    );
  }
}
