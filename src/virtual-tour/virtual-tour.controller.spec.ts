import { Test, TestingModule } from '@nestjs/testing';
import { VirtualTourController } from './virtual-tour.controller';
import { VirtualTourService } from './virtual-tour.service';

describe('VirtualTourController', () => {
  let controller: VirtualTourController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VirtualTourController],
      providers: [VirtualTourService],
    }).compile();

    controller = module.get<VirtualTourController>(VirtualTourController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
