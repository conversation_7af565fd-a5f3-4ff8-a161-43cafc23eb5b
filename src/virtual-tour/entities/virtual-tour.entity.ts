import { Asset } from 'src/asset/entities/asset.entity';
import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { UnitType } from 'src/unit-type/entities/unit-type.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

@Entity()
export class VirtualTour extends CrudEntity {
  @Column({ type: 'jsonb' })
  name: Record<string, string>;

  @Column({ nullable: true })
  url?: string;

  @Column({ type: 'uuid', nullable: true })
  thumbnailId?: string;

  @OneToOne(() => Asset)
  @JoinColumn({ name: 'thumbnailId' })
  thumbnail?: Asset;

  @Column({ type: 'uuid' })
  projectId: string;

  @ManyToOne(() => Project, (project) => project.virtualTours)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column({ type: 'uuid', nullable: true })
  unitTypeId?: string;

  @ManyToOne(() => UnitType)
  @JoinColumn({ name: 'unitTypeId' })
  unitType?: UnitType;

  @Column({ type: 'float', nullable: true, default: 0 })
  minSize?: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  maxSize?: number;
}
