import { forwardRef, Module } from '@nestjs/common';
import { VirtualTourService } from './virtual-tour.service';
import { VirtualTourController } from './virtual-tour.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VirtualTour } from './entities/virtual-tour.entity';
import { Project } from 'src/project/entities/project.entity';
import { Domain } from 'domain';
import { AssetModule } from 'src/asset/asset.module';
import { ProjectModule } from 'src/project/project.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([VirtualTour, Project, UserConfig, Domain]),
    AssetModule,
    forwardRef(() => ProjectModule),
    UnitTypeModule,
    RedisModule,
  ],
  controllers: [VirtualTourController],
  providers: [VirtualTourService],
  exports: [VirtualTourService],
})
export class VirtualTourModule {}
