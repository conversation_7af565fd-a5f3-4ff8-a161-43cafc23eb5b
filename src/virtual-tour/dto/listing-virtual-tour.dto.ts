import { IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';

export class ListingVirtualTourDto extends PaginationQueryDto {
  @IsString()
  @IsOptional()
  keyword?: string;

  @IsUUID()
  @IsOptional()
  projectId?: string;

  @IsString()
  @IsOptional()
  projectSlug?: string;

  @IsString()
  @IsOptional()
  distinct?: string;
}
