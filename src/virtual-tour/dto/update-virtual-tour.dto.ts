import { OmitType, PartialType } from '@nestjs/mapped-types';
import { CreateVirtualTourDto } from './create-virtual-tour.dto';
import { ELangCode } from 'src/core/enums/lang.enum';
import { IsEnum, IsOptional } from 'class-validator';

export class UpdateVirtualTourDto extends PartialType(
  OmitType(CreateVirtualTourDto, ['projectId']),
) {
  @IsEnum(ELangCode)
  @IsOptional()
  lang?: ELangCode;
}
