import { forwardRef, Global, Module } from '@nestjs/common';
import { HashingService } from './hashing/hashing.service';
import { BcryptService } from './hashing/bcrypt.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import jwtConfig from './config/jwt.config';
import { User } from '../users/entities/user.entity';
import { APP_GUARD } from '@nestjs/core';
import { AuthenticationGuard } from './authentication/guards/authentication/authentication.guard';
import { AuthenticationService } from './authentication/authentication.service';
import { AccessTokenGuard } from './authentication/guards/access-token/access-token.guard';
import { AuthenticationController } from './authentication/authentication.controller';
import { Session } from './authentication/entities/session.entity';
import { MailerModule } from 'src/mailer/mailer.module';
import { RedisModule } from 'src/redis/redis.module';
import { Url } from 'src/url/entities/url.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { LoginActivityModule } from 'src/login-activity/login-activity.module';
import { LoginActivity } from 'src/login-activity/entities/login-activity.entity';
import { UsersModule } from 'src/users/users.module';
import { UserFeatureModule } from 'src/user-feature/user-feature.module';
import { UserConfigModule } from 'src/user-config/user-config.module';
import { DomainsModule } from 'src/domains/domains.module';
import { ContactSaleSubmissionModule } from 'src/contact-sale-submission/contact-sale-submission.module';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([User, Session, Url, UserConfig, LoginActivity]),
    JwtModule.registerAsync(jwtConfig.asProvider()), // 👈
    MailerModule,
    RedisModule,
    LoginActivityModule,
    forwardRef(() => UsersModule),
    forwardRef(() => UserFeatureModule),
    forwardRef(() => UserConfigModule),
    forwardRef(() => DomainsModule),
    forwardRef(() => ContactSaleSubmissionModule),
  ],
  controllers: [AuthenticationController],
  providers: [
    AccessTokenGuard,
    AuthenticationGuard,
    {
      provide: HashingService,
      useClass: BcryptService,
    },
    {
      provide: APP_GUARD,
      useClass: AuthenticationGuard,
    },
    {
      provide: 'OTP_EXPIRE_IN_SECONDS',
      useValue: 60 * 5,
    },
    AuthenticationService,
  ],
  exports: [
    {
      provide: HashingService,
      useClass: BcryptService,
    },
  ],
})
export class IamModule {}
