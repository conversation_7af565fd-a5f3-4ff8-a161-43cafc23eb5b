import { Injectable } from '@nestjs/common';
import { HashingService } from './hashing.service';
import { genSalt, hash, compare } from 'bcrypt';
import { createCipheriv, createDecipheriv, createHash } from 'crypto';
import { ConfigService } from '@nestjs/config';

const ALGORITHM = 'aes-256-cbc';

@Injectable()
export class BcryptService implements HashingService {
  constructor(private readonly configService: ConfigService) {}
  async hash(data: string | Buffer): Promise<string> {
    const salt = await genSalt();
    return hash(data, salt);
  }

  async compare(data: string | Buffer, encrypted: string): Promise<boolean> {
    return compare(data, encrypted);
  }

  private getKey(): Buffer {
    const rawKey = this.configService.get('SECRET_KEY');
    // Hash to ensure 32 bytes
    return createHash('sha256').update(String(rawKey)).digest(); // 32 bytes
  }

  private getIVKey(): Buffer {
    const rawKey = this.configService.get('SECRET_KEY_IV');
    return Buffer.from(rawKey, 'utf-8');
  }

  encrypt(data: string): string {
    const cipher = createCipheriv(ALGORITHM, this.getKey(), this.getIVKey());
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  decrypt(encrypted: string): string {
    const decipher = createDecipheriv(
      ALGORITHM,
      this.getKey(),
      this.getIVKey(),
    );
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
