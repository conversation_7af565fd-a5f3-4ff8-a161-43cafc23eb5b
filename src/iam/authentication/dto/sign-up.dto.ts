import { OmitType } from '@nestjs/mapped-types';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { Roles } from '../../../../src/users/entities/user.entity';

export class SignUpDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEnum(Roles)
  role: Roles;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  whatsapp?: string;

  @IsUUID()
  @IsOptional()
  photoId?: string;

  @IsString()
  @IsOptional()
  measuringId?: string;

  @IsString()
  @IsOptional()
  company?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  additionalEmail?: string[];
}

export class SignUpAgentByAdminDto extends OmitType(SignUpDto, ['password']) {}
