import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UseFilters,
} from '@nestjs/common';
import { AuthenticationService } from './authentication.service';
import {
  SendOtpSmsDto,
  SignInAgentByAdminDto,
  SignInDto,
} from './dto/sign-in.dto';
import { Auth } from './decorators/auth.decorator';
import { AuthType } from './enums/auth-type.enum';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { UnauthorizedExceptionFilter } from 'src/core/filter/unauthorized-exception.filter';
import { ActiveUser } from './decorators/active-user.decorator';
import { Request } from 'express';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { SignUpDto } from './dto/sign-up.dto';

@Auth(AuthType.None)
@Controller('auth')
export class AuthenticationController {
  constructor(private readonly authenticationService: AuthenticationService) {}

  @HttpCode(HttpStatus.OK)
  @Post('sign-up')
  async signUp(@Body() signUpDto: SignUpDto, @Req() req: Request) {
    return this.authenticationService.signUp(signUpDto, req);
  }

  @HttpCode(HttpStatus.OK)
  @Post('sign-in')
  async signIn(@Body() signInDto: SignInDto, @Req() req: Request) {
    return this.authenticationService.signIn(signInDto, req);
  }

  @HttpCode(HttpStatus.OK)
  @Post('verify-recaptcha')
  async verifyRecaptcha(@Body() { token }: { token: string }) {
    return this.authenticationService.verifyTurnstile(token);
  }

  @Post('send-otp-by-phone')
  @HttpCode(HttpStatus.OK)
  async sendOtpSms(@Body() sendOtpSmsDto: SendOtpSmsDto) {
    return this.authenticationService.sendOtpSms(sendOtpSmsDto.phone);
  }

  @HttpCode(HttpStatus.OK) // changed since the default is 201
  @Post('refresh-tokens')
  refreshTokens(@Req() req: Request, @Body() refreshTokenDto: RefreshTokenDto) {
    return this.authenticationService.refreshTokens(req, refreshTokenDto);
  }

  @UseFilters(UnauthorizedExceptionFilter)
  @HttpCode(HttpStatus.OK)
  @Post('verify-otp')
  async verifyOtp(@Req() req: Request, @Body() verifyOtpDto: VerifyOtpDto) {
    return this.authenticationService.verifyOtp(verifyOtpDto, req);
  }

  @Auth(AuthType.Bearer)
  @HttpCode(HttpStatus.OK)
  @Post('sign-in-agent/by-admin')
  async signInAgentByAdmin(
    @Req() req: Request,
    @Body() signInDto: SignInAgentByAdminDto,
    @ActiveUser('role') role: string,
  ) {
    return this.authenticationService.signInAgentByAdmin(signInDto, role, req);
  }

  @Auth(AuthType.Bearer)
  @HttpCode(HttpStatus.OK)
  @Post('sign-out')
  async signOut(
    @Req() req: Request,
    @ActiveUser('sessionId') sessionId: string,
    @ActiveUser('sub') userId: string,
  ) {
    return this.authenticationService.signOut(req, userId, sessionId);
  }
}
