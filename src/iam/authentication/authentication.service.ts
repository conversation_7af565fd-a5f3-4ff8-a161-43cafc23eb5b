import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService, ConfigType } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Roles, User } from '../../users/entities/user.entity';
import jwtConfig from '../config/jwt.config';
import { HashingService } from '../hashing/hashing.service';
import { SignInAgentByAdminDto, SignInDto } from './dto/sign-in.dto';
import { ActiveUserData } from './interfaces/active-user-data.interface';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { randomUUID } from 'crypto';
import { Session } from './entities/session.entity';
import { RedisService } from 'src/redis/redis.service';
import { MailerService } from 'src/mailer/mailer.service';
import { EMAIL_TEMPLATE } from 'src/mailer/enums/mailer.enum';
import { CustomUnauthorizedException } from 'src/core/exception/custome-unauthorized.exception';
import { EErrorId } from 'src/core/enums/errorId.enum';
import { Url } from 'src/url/entities/url.entity';
import axios from 'axios';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { Request } from 'express';
import * as UAparser from 'ua-parser-js';
import * as geoip from 'geoip-lite';
import { EStatus } from 'src/login-activity/login-activity.enum';
import { LoginActivity } from 'src/login-activity/entities/login-activity.entity';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { SignUpDto } from './dto/sign-up.dto';
import { UsersService } from 'src/users/users.service';
import { UserFeatureService } from 'src/user-feature/user-feature.service';
import { UserConfigService } from 'src/user-config/user-config.service';
import { DomainsService } from 'src/domains/domains.service';
import { CreateUserConfigDto } from 'src/user-config/dto/create-user-config.dto';
import { ContactSaleSubmissionService } from 'src/contact-sale-submission/contact-sale-submission.service';
import { SalesTeamDto } from 'src/user-config/dto/sales-team.dto';

@Injectable()
export class AuthenticationService {
  constructor(
    @InjectRepository(User) private readonly usersRepository: Repository<User>,
    @InjectRepository(Session)
    private readonly sessionRepo: Repository<Session>,
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    @InjectRepository(LoginActivity)
    private readonly loginActivityService: Repository<LoginActivity>,

    private readonly hashingService: HashingService,
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
    private readonly mailerService: MailerService,
    private readonly usersService: UsersService,
    private readonly userFeatureService: UserFeatureService,
    private readonly userConfigService: UserConfigService,

    @Inject(jwtConfig.KEY)
    private readonly jwtConfiguration: ConfigType<typeof jwtConfig>,
    @Inject('OTP_EXPIRE_IN_SECONDS')
    private readonly otpExpireInSeconds: number,

    private readonly configService: ConfigService,
    private readonly domainService: DomainsService,
    private readonly contactSaleSubmissionService: ContactSaleSubmissionService,
  ) {}

  async sendOtpSms(phoneNumber: string) {
    const otp = this.generateOtp();

    const user = await this.usersRepository.findOne({
      where: { phone: phoneNumber },
    });

    if (!user) {
      throw new NotFoundException(
        `Could not find any user with phone number: ${phoneNumber}. Please register first.`,
      );
    }

    try {
      const urls = await this.urlRepository.find({
        where: {
          type: 'sign-in',
        },
      });

      await this.redisService.set(phoneNumber, otp, this.otpExpireInSeconds);

      for (const url of urls) {
        await axios
          .post(url.name, {
            phone: `${phoneNumber}`,
            otp,
          })
          .catch((error) => {
            console.error(`Error posting to ${url.name}:`, error.message);
            return null;
          });
      }

      return { message: 'OTP sent successfully' };
    } catch (error) {
      console.error('Error sending OTP:', error);
      throw new Error('Failed to send OTP');
    }
  }

  async signInAgentByAdmin(
    signInDto: SignInAgentByAdminDto,
    role: string,
    req: Request,
  ) {
    if (role !== Roles.ADMIN) {
      throw new UnauthorizedException();
    }

    const user = await this.usersRepository
      .createQueryBuilder('user')
      .where('user.email = :email', {
        email: signInDto.email,
      })
      .addSelect('user.password')
      .getOne();

    if (!user) {
      throw new UnauthorizedException('User does not exists');
    }

    const tokens = await this.generateTokens(user);
    if (this.jwtConfiguration.monoSession) {
      await this.removeSession(req, user, user.id);
    }
    await this.saveSession(user.id, tokens.sessionId);

    const loginActivity = this.buildLoginActivity(
      req,
      user,
      tokens.sessionId,
      EStatus.ACTIVE,
    );
    console.log('loginActivity', loginActivity);

    try {
      await this.loginActivityService.save(loginActivity);
    } catch (error) {
      console.log('Error when create login activity', error);
    }

    return {
      tokens,
      user,
    };
  }

  async signUp(signUpDto: SignUpDto, req: Request) {
    if (signUpDto.role !== Roles.AGENCY) {
      throw new UnauthorizedException('Role invalid.');
    }

    const userCreated = await this.usersService.create(signUpDto);
    this.userFeatureService.createLandingPageFeature(userCreated.id);

    const tokens = await this.generateTokens(userCreated);
    if (this.jwtConfiguration.monoSession) {
      await this.removeSession(req, userCreated, userCreated.id);
    }
    await this.saveSession(userCreated.id, tokens.sessionId);

    const loginActivity = this.buildLoginActivity(
      req,
      userCreated,
      tokens.sessionId,
      EStatus.ACTIVE,
    );

    try {
      await this.loginActivityService.save(loginActivity);
      await this.userFeatureService.initAutoAddNewProjectFeature(
        userCreated.id,
      );
      const domain = await this.domainService.generateUniqueAgencySubdomain(
        userCreated.id,
        userCreated.email,
      );
      const ceaDetail = await this.contactSaleSubmissionService.getCEADetail(
        userCreated.phone,
      );
      if (!ceaDetail) {
        throw new BadRequestException(
          'CEA Detail not found for the provided phone number.',
        );
      }

      const newSaleTeamDto = new SalesTeamDto();
      newSaleTeamDto.currentEa = ceaDetail.currentEa;
      newSaleTeamDto.licenseNumber = ceaDetail.licenseNumber;
      newSaleTeamDto.name = ceaDetail.name;
      newSaleTeamDto.registrationNumber = ceaDetail.registrationNumber;

      const newUserConfigDto = new CreateUserConfigDto();
      newUserConfigDto.domain = domain;
      newUserConfigDto.phoneNumber = userCreated.phone;
      newUserConfigDto.siteDescription = '';
      newUserConfigDto.siteTitle = '';
      newUserConfigDto.salesTeamInfo = newSaleTeamDto;

      const userConfigCreated = await this.userConfigService.create(
        userCreated.id,
        newUserConfigDto,
      );

      await this.mailerService.sendMail(
        {
          to: userCreated.email,
          subject: 'New Account Registration',
          html: EMAIL_TEMPLATE.NEW_ACCOUNT_REGISTRATION,
        },
        {
          currentEa:
            userConfigCreated?.salesTeamInfo?.currentEa || 'PROJECT SG',
        },
        'mjml',
      );
    } catch (error) {
      console.log('Error when create login activity', error);
    }

    return {
      tokens,
      user: userCreated,
    };
  }

  async signIn(signInDto: SignInDto, req: Request) {
    const user = await this.usersRepository
      .createQueryBuilder('user')
      .where('user.email = :email', {
        email: signInDto.email,
      })
      .addSelect('user.password')
      .getOne();

    if (!user) {
      throw new UnauthorizedException(
        'User does not exists',
        EErrorId.INVALID_EMAIL_OR_PASSWORD,
      );
    }

    const isEqual = await this.hashingService.compare(
      signInDto.password,
      user.password,
    );

    if (!isEqual) {
      throw new UnauthorizedException(
        'Password does not match',
        EErrorId.INVALID_EMAIL_OR_PASSWORD,
      );
    }

    if (user.role === Roles.ADMIN || signInDto.email === '<EMAIL>') {
      const tokens = await this.generateTokens(user);
      await this.saveSession(user.id, tokens.sessionId);

      const loginActivity = this.buildLoginActivity(
        req,
        user,
        tokens.sessionId,
        EStatus.ACTIVE,
      );
      console.log('loginActivity', loginActivity);

      try {
        await this.loginActivityService.save(loginActivity);

        const loginQuery = this.loginActivityService
          .createQueryBuilder('loginActivity')
          .where(
            "loginActivity.createdAt < :expirationTime AT TIME ZONE 'UTC'",
            {
              expirationTime: new Date(
                Date.now() - this.jwtConfiguration.accessTokenTtl * 1000,
              ).toISOString(), // convert seconds to milliseconds
            },
          )
          .andWhere('loginActivity.loggedOutAt IS NULL');

        console.log(
          'expired time ',
          new Date(Date.now() - this.jwtConfiguration.accessTokenTtl * 1000),
        );
        const expiredLogins = await loginQuery.getMany();

        // console.log('expiredLogins', expiredLogins);
        expiredLogins.forEach(async (login) => {
          await this.loginActivityService.update(
            { sessionId: login.sessionId },
            {
              loggedOutAt: new Date(
                login.createdAt.getTime() +
                  this.jwtConfiguration.accessTokenTtl * 1000,
              ), // set logout time to the expiration time
              status: EStatus.INACTIVE,
            },
          );
        });
      } catch (error) {
        console.log('Error when create login activity', error);
      }

      return {
        tokens,
        user,
      };
    }
    const isProduction = this.configService.get<string>('NODE_ENV');

    let otp = '111111';

    if (isProduction == 'production') {
      otp = this.generateOtp();
      await this.mailerService.sendMail(
        {
          to: user.email,
          subject: 'ProjectSG Verification Code',
          html: EMAIL_TEMPLATE.VERIFICATION_CODE,
        },
        {
          otp,
          name: user.firstName + ' ' + user.lastName,
        },
        'mjml',
      );
    }

    await this.redisService.set(user.email, otp, this.otpExpireInSeconds);

    try {
      const urls = await this.urlRepository.find({
        where: {
          type: 'sign-in',
        },
      });

      const userConfig = await this.userConfigRepository.findOneByOrFail({
        userId: user.id,
      });

      for (const url of urls) {
        await axios
          .post(url.name, {
            phone: user.phone || userConfig.phoneNumber,
            email: user.email || userConfig.agencyEmail,
            otp,
          })
          .catch((error) => {
            console.error(`Error posting to ${url.name}:`, error.message);
            return null;
          });
      }
    } catch (error) {
      console.log(error);
    }

    return { message: 'OTP sent to your email' };
  }

  private buildLoginActivity(
    req: Request,
    user: User,
    sessionId: string,
    status: EStatus,
  ) {
    const ua = UAparser(req.headers['user-agent']);
    const ip =
      req.headers['x-forwarded-for'] ||
      req.headers['cf-connecting-ip'] ||
      req.headers['x-real-ip'];

    const ipAddress = Array.isArray(ip)
      ? ip[0]
      : ip || req.connection.remoteAddress;

    const geo = geoip.lookup(ipAddress);
    const referrer =
      req.headers['referer'] || req.headers['referrer'] || 'No referrer';

    return {
      userId: user.id,
      ip: ipAddress,
      country: geo?.country,
      city: geo?.city,
      browser: ua?.browser?.name,
      os: ua?.os?.name,
      device: {
        model: ua?.device?.model,
        type: ua?.device?.type,
        vendor: ua?.device?.vendor,
      },
      referral: Array.isArray(referrer) ? referrer[0] : referrer,
      status,
      sessionId,
      loggedOutAt: status === EStatus.INACTIVE ? new Date() : null,
    };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto, req: Request) {
    if (!verifyOtpDto.email && !verifyOtpDto.phone) {
      throw new CustomUnauthorizedException(
        'Email or phone number is required',
        EErrorId.INVALID_OTP,
      );
    }

    if (verifyOtpDto.email !== '<EMAIL>') {
      const identifier = verifyOtpDto.email || verifyOtpDto.phone;
      const otp = await this.redisService.get(identifier);

      if (!otp) {
        throw new CustomUnauthorizedException(
          'OTP expired',
          EErrorId.EXPIRED_OTP,
        );
      }

      if (otp !== verifyOtpDto.otp) {
        throw new CustomUnauthorizedException(
          'Invalid OTP',
          EErrorId.INVALID_OTP,
        );
      }
    }

    let user: User;
    if (verifyOtpDto.email) {
      user = await this.usersRepository.findOneByOrFail({
        email: verifyOtpDto.email,
      });
    } else {
      user = await this.usersRepository.findOneByOrFail({
        phone: `${verifyOtpDto.phone}`,
      });
    }

    const tokens = await this.generateTokens(user);
    if (this.jwtConfiguration.monoSession) {
      await this.removeSession(req, user, user.id);
    }
    await this.saveSession(user.id, tokens.sessionId);

    const loginActivity = this.buildLoginActivity(
      req,
      user,
      tokens.sessionId,
      EStatus.ACTIVE,
    );
    console.log('loginActivity', loginActivity);

    try {
      await this.loginActivityService.save(loginActivity);
    } catch (error) {
      console.log('Error when create login activity', error);
    }

    return {
      tokens,
      user,
    };
  }

  async generateTokens(user: User, prevSessionId?: string) {
    if (prevSessionId) {
      const section = await this.sessionRepo.findOneBy({ id: prevSessionId });
      if (!section) {
        throw new UnauthorizedException();
      }
    } else {
    }
    const sessionId = randomUUID();

    const [accessToken, refreshToken] = await Promise.all([
      this.signToken<Partial<ActiveUserData>>(
        user.id,
        this.jwtConfiguration.accessTokenTtl,
        { email: user.email, sessionId, role: user.role },
      ),
      this.signToken(user.id, this.jwtConfiguration.refreshTokenTtl, {
        sessionId,
        role: user.role,
      }),
    ]);
    return {
      accessToken,
      refreshToken,
      sessionId,
    };
  }

  async refreshTokens(req: Request, refreshTokenDto: RefreshTokenDto) {
    try {
      const { sub, sessionId } = await this.jwtService.verifyAsync<{
        sessionId: string;
        role: Roles;
        sub: string;
      }>(refreshTokenDto.refreshToken, {
        secret: this.jwtConfiguration.secret,
        audience: this.jwtConfiguration.audience,
        issuer: this.jwtConfiguration.issuer,
      });
      const user = await this.usersRepository.findOneByOrFail({
        id: sub,
      });
      const tokens = await this.generateTokens(user, sessionId);

      if (this.jwtConfiguration.monoSession) {
        await this.removeSession(req, user, user.id);
      } else {
        await this.removeSession(req, user, user.id, sessionId);
      }
      await this.saveSession(user.id, tokens.sessionId);

      return tokens;
    } catch (_err) {
      throw new UnauthorizedException();
    }
  }

  private generateOtp() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private async signToken<T>(userId: string, expiresIn: number, payload?: T) {
    return await this.jwtService.signAsync(
      {
        sub: userId,
        ...payload,
      },
      {
        audience: this.jwtConfiguration.audience,
        issuer: this.jwtConfiguration.issuer,
        secret: this.jwtConfiguration.secret,
        expiresIn,
      },
    );
  }

  private async removeSession(
    req: Request,
    user: User,
    userId: string,
    prevSessionId?: string,
  ) {
    if (prevSessionId) {
      try {
        await this.loginActivityService.update(
          { sessionId: prevSessionId },
          {
            loggedOutAt: new Date(),
            status: EStatus.INACTIVE,
          },
        );
      } catch (error) {
        console.log('Error when create logout activity', error);
      }
      return await this.sessionRepo.delete({ id: prevSessionId });
    }

    const query = this.sessionRepo
      .createQueryBuilder('session')
      .where({
        userId,
      })
      .orderBy('session.createdAt', 'DESC')
      .limit(1);

    const session = await query.getOne();

    try {
      await this.loginActivityService.update(
        { sessionId: session.id },
        {
          loggedOutAt: new Date(),
          status: EStatus.INACTIVE,
        },
      );
    } catch (error) {
      console.log('Error when create logout activity', error);
    }

    return await this.sessionRepo.delete({ userId });
  }

  private async saveSession(userId: string, sessionId: string) {
    return await this.sessionRepo.save({ id: sessionId, userId });
  }

  async signOut(req: Request, userId: string, sessionId: string) {
    console.log('sessionId', sessionId);
    console.log('userId', userId);
    const user = await this.usersRepository.findOneByOrFail({
      id: userId,
    });

    if (user) {
      const updated = await this.loginActivityService.update(
        { sessionId: sessionId },
        {
          loggedOutAt: new Date(),
          status: EStatus.INACTIVE,
        },
      );

      console.log('updatedLogin', updated);
    }

    return await this.sessionRepo.delete({ id: sessionId });
  }

  async verifyTurnstile(token: string) {
    const secret = this.configService.get<string>(
      'CLOUDFLARE_TURNSTILE_SECRET',
    );

    if (!token) {
      throw new BadRequestException('Token is required');
    }

    const response = await axios.post(
      'https://challenges.cloudflare.com/turnstile/v0/siteverify',
      JSON.stringify({
        secret,
        response: token,
      }),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (!response.data.success) {
      const errorMessage = response?.data?.error_codes?.[0];
      throw new BadRequestException(
        errorMessage || "Can't verify recaptcha, please try again later.",
      );
    }

    return response.data;
  }
}
