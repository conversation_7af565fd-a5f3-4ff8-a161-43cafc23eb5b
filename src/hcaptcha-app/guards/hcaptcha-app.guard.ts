import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { USE_HCAPTCHA_APP_KEY } from '../decorators/hcaptcha-app.decorator';
import { HCaptchaAppService } from '../hcaptcha-app.service';
import { VerifyHCaptchaAppDto } from '../dto/verify-hcaptcha-app.dto';

@Injectable()
export class HCaptchaAppGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly hCaptchaAppService: HCaptchaAppService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const useHcaptcha = this.reflector.getAllAndOverride<boolean>(
      USE_HCAPTCHA_APP_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!useHcaptcha) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = request.headers['x-hcaptcha-token'];

    if (!token) {
      throw new UnauthorizedException('Missing hCaptcha token');
    }
    const newHcaptcha = new VerifyHCaptchaAppDto();
    newHcaptcha.token = token;
    const result = await this.hCaptchaAppService.verify(newHcaptcha);

    if (!result.success) {
      const message = (
        result['error-codes'] || ['Invalid hCaptcha token']
      ).join(', ');
      throw new UnauthorizedException(message);
    }
    return true;
  }
}
