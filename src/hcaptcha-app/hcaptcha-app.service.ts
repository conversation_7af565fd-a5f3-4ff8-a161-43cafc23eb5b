import { ConfigService } from '@nestjs/config';
import { VerifyHCaptchaAppDto } from './dto/verify-hcaptcha-app.dto';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { verify } from 'hcaptcha';

@Injectable()
export class HCaptchaAppService {
  constructor(private readonly configService: ConfigService) {}

  async verify(dto: VerifyHCaptchaAppDto): Promise<VerifyResponse> {
    try {
      const { token } = dto;
      const secret = this.configService.get<string>('HCAPTCHA_SECRET');
      const result = await verify(secret, token);
      return result;
    } catch (error) {
      throw new UnauthorizedException(error?.message || 'Error');
    }
  }
}
