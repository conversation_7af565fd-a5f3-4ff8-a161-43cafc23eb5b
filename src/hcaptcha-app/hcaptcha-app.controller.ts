import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { HCaptchaAppService } from './hcaptcha-app.service';
import { VerifyHCaptchaAppDto } from './dto/verify-hcaptcha-app.dto';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { Roles } from 'src/users/entities/user.entity';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';

@Controller('h-captcha-app')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class HCaptchaAppController {
  constructor(private readonly hCaptchaAppService: HCaptchaAppService) {}

  @Post('verify')
  async verify(@Body() dto: VerifyHCaptchaAppDto) {
    return await this.hCaptchaAppService.verify(dto);
  }
}
