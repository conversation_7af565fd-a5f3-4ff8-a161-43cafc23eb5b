import { Module } from '@nestjs/common';
import { LoggerModule, Logger } from 'nestjs-pino';
import { RedisModule } from 'src/redis/redis.module';
import { LoggerAppService } from './logger-app.service';
import { LoggerAppController } from './logger-app.controller';

@Module({
  imports: [
    RedisModule,
    LoggerModule.forRoot({
      pinoHttp: {
        customProps: (req, res) => ({
          context: 'HTTP',
        }),
        transport: {
          targets: [
            {
              target: 'pino-pretty',
              // target: 'pino/file',
              options: {
                colorize: true,
                translateTime: 'SYS:standard',
                destination: 'logs/info.log',
              },
              level: 'info',
            },
            {
              target: 'pino/file',
              options: { destination: 'logs/error.log' },
              level: 'error',
            },
          ],
        },
        enabled: true,
        wrapSerializers: true,
        quietReqLogger: true,
        autoLogging: false,
        serializers: {
          req: (req) => {
            const data = {
              method: req.method,
              url: req.url,
              query: req.query,
              body: req.body,
            };
            console.log('Request: ', data);
            return data;
          },
          res: (res) => {
            return {
              statusCode: res.statusCode,
            };
          },
        },
      },
    }),
  ],
  controllers: [LoggerAppController],
  providers: [LoggerAppService],
  exports: [LoggerAppService],
})
export class LoggerAppModule {}
