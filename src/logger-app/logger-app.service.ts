import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Response } from 'express';
import {
  appendFileSync,
  createReadStream,
  existsSync,
  readdirSync,
  readFileSync,
  truncateSync,
  unlinkSync,
} from 'fs';
import { Lo<PERSON>, PinoLogger } from 'nestjs-pino';
import { join } from 'path';
import * as archiver from 'archiver';

@Injectable()
export class LoggerAppService {
  constructor(public readonly logger: PinoLogger) {}
  private readonly logDir = join(__dirname, '../../../logs');
  private readonly logFile = join(this.logDir, 'info.log');
  private readonly errorFile = join(this.logDir, 'error.log');

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCron() {
    const now = new Date();
    const logDate = new Date(now);
    // Subtract 1 day
    logDate.setDate(logDate.getDate() - 1);
    const formattedDate = `${String(logDate.getDate()).padStart(2, '0')}-${String(
      logDate.getMonth() + 1,
    ).padStart(2, '0')}-${logDate.getFullYear()}`;
    const archiveLogFile = join(this.logDir, `${formattedDate}.info.log`);
    const archiveErrorFile = join(this.logDir, `${formattedDate}.error.log`);

    try {
      if (existsSync(this.logFile)) {
        const content = readFileSync(this.logFile, 'utf-8');
        appendFileSync(archiveLogFile, content);
        truncateSync(this.logFile, 0);
      }

      if (existsSync(this.errorFile)) {
        const content = readFileSync(this.errorFile, 'utf-8');
        appendFileSync(archiveErrorFile, content);
        truncateSync(this.errorFile, 0);
      }
      await this.handleRemoveFiles(logDate);
    } catch (err) {
      this.logger.error('Log rotation failed:', err);
    }
  }

  async handleRemoveFiles(now: Date) {
    // Clean up old logs (older than 7 days)
    const files = readdirSync(this.logDir);

    const nowTime = now.getTime();
    const sevenDaysMs = 7 * 24 * 60 * 60 * 1000;

    files.forEach((file) => {
      const fullPath = join(this.logDir, file);

      const match = file.match(/^(\d{2})-(\d{2})-(\d{4})\.(info|error)\.log$/);

      if (match) {
        const [_, day, month, year] = match;
        const fileDate = new Date(`${year}-${month}-${day}`);
        const fileTime = fileDate.getTime();

        if (nowTime - fileTime > sevenDaysMs) {
          unlinkSync(fullPath);
        }
      }
    });
  }

  async downloadLogs(res: Response) {
    if (!existsSync(this.logDir)) {
      throw new BadRequestException('Logs directory does not exist');
    }

    const files = readdirSync(this.logDir);
    if (files.length === 0) {
      throw new NotFoundException('No log files found');
    }

    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', 'attachment; filename=logs.zip');

    const archive = archiver('zip', { zlib: { level: 9 } });
    archive.on('error', (err) => {
      throw new BadRequestException(`Archive error: ${err.message}`);
    });

    archive.pipe(res);

    files.forEach((file) => {
      const fullPath = join(this.logDir, file);
      archive.append(createReadStream(fullPath), { name: file });
    });

    await archive.finalize();
  }
}
