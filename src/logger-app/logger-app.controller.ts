import { Controller, Get, Res, UseGuards } from '@nestjs/common';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { Roles } from 'src/users/entities/user.entity';
import { LoggerAppService } from './logger-app.service';
import { Response } from 'express';

@Controller('logger-app')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class LoggerAppController {
  constructor(private readonly loggerAppService: LoggerAppService) {}

  @Get('downloads')
  async downloadFile(@Res() res: Response) {
    return await this.loggerAppService.downloadLogs(res);
  }
}
