import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { CacheOption } from 'src/redis/decorators/cache-option.decorator';
import { ScriptService } from './script.service';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { AssignPackageDto } from './dto/assign-package.dto';

@Controller('script')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class ScriptController {
  constructor(private readonly scriptService: ScriptService) {}

  @Post('update-reason-contact-sale-submission-log')
  async updateAllReasonInContactSaleSubmissionLog() {
    return await this.scriptService.updateReason();
  }

  @Post('update-invalid-date-contact-sale-submission')
  async updateAllReasonInContactSaleSubmission() {
    return await this.scriptService.updateReasonContactSaleSubmission();
  }

  @Post('assign-package')
  async assignPackage(@Body() body: AssignPackageDto) {
    return await this.scriptService.assignPackage(body);
  }
}
