import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import {
  ContactSaleSubmissionLog,
  EContactSaleSubmissionType,
} from 'src/contact-sale-submission/entities/contact-sale-submission-log.entity';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { PackageService } from 'src/package/package.service';
import { UserPackageService } from 'src/user-package/user-package.service';
import { Roles, User } from 'src/users/entities/user.entity';
import { IsNull, Repository } from 'typeorm';
import { AssignPackageDto } from './dto/assign-package.dto';

@Injectable()
export class ScriptService {
  constructor(
    @InjectRepository(ContactSaleSubmissionLog)
    private readonly contactSaleSubmissionLogRepository: Repository<ContactSaleSubmissionLog>,
    @InjectRepository(ContactSaleSubmission)
    private readonly contactSaleSubmissionRepository: Repository<ContactSaleSubmission>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,

    private readonly packageService: PackageService,
    private readonly userPackageService: UserPackageService,
  ) {}

  async updateReason() {
    await this.contactSaleSubmissionLogRepository
      .createQueryBuilder('contact_sale_submission_log')
      .update(ContactSaleSubmissionLog)
      .set({
        reason: () => `
          'Submit date: ' ||
          to_char(
            (substring(contact_sale_submission_log.reason from 'Submit date: (.+?) -')::timestamp + interval '8 hours'),
            'Mon DD, YYYY HH12:MI AM'
          ) ||
          ' - Appointment date: ' ||
          substring(contact_sale_submission_log.reason from 'Appointment date: (.+)$')
        `,
      })
      .where(
        `contact_sale_submission_log.type = :type AND contact_sale_submission_log.reason IS NOT NULL AND contact_sale_submission_log.reason ~ :pattern`,
        {
          type: EContactSaleSubmissionType.AppointmentDate,
          pattern: '^Submit date: .+ - Appointment date: .+$',
        },
      )
      .execute();
  }

  async updateReasonContactSaleSubmission() {
    await this.contactSaleSubmissionRepository
      .createQueryBuilder('contact_sale_submission')
      .update(ContactSaleSubmission)
      .set({
        appointment: 'Not Given',
      })
      .where(
        `contact_sale_submission.appointment IS NOT NULL AND contact_sale_submission.appointment = :appointment`,
        {
          appointment: 'Invalid date',
        },
      )
      .execute();

    await this.contactSaleSubmissionLogRepository
      .createQueryBuilder('contact_sale_submission_log')
      .update(ContactSaleSubmissionLog)
      .set({
        reason: 'Not Given',
      })
      .where(
        `contact_sale_submission_log.type = :type AND contact_sale_submission_log.reason IS NOT NULL AND contact_sale_submission_log.reason = :reason`,
        {
          type: EContactSaleSubmissionType.AppointmentDate,
          reason: 'Invalid date',
        },
      )
      .execute();
  }

  async assignPackage(body: AssignPackageDto) {
    const start = Date.now();
    const { code } = body;
    const packageData = await this.packageService.getPackage({
      code,
      duration: IsNull(),
    });
    const usersData = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userPackage', 'user_package')
      .where('user.deletedAt IS NULL')
      .andWhere('user.role = :role', { role: Roles.AGENCY })
      .andWhere('user.statusAccount = :status', { status: 'active' })
      .andWhere('user_package.id IS NULL')
      .orderBy('user.updatedAt', 'DESC')
      .getMany();
    for await (const user of usersData) {
      await this.userPackageService.attachUserPackage({
        userId: user.id,
        packageId: packageData.id,
      });
    }

    const end = Date.now();
    const durationInMs = end - start;

    return {
      startedAt: new Date(start),
      endedAt: new Date(end),
      durationMs: durationInMs / 1000,
      totalUsers: usersData.length,
    };
  }
}
