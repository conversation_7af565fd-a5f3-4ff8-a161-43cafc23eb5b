import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactSaleSubmissionLog } from 'src/contact-sale-submission/entities/contact-sale-submission-log.entity';
import { ScriptService } from './script.service';
import { ScriptController } from './script.controller';
import { ContactSaleSubmission } from 'src/contact-sale-submission/entities/contact-sale-submission.entity';
import { PackageModule } from 'src/package/package.module';
import { UserPackageModule } from 'src/user-package/user-package.module';
import { User } from 'src/users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ContactSaleSubmissionLog,
      ContactSaleSubmission,
      User,
    ]),
    forwardRef(() => UserPackageModule),
    forwardRef(() => PackageModule),
  ],
  controllers: [ScriptController],
  providers: [ScriptService],
  exports: [ScriptService],
})
export class ScriptModule {}
