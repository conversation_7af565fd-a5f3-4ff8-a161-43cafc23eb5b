import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserPreferenceService } from './user-preference.service';
import { UserPreferenceController } from './user-preference.controller';
import { UserPreference } from './entities/user-preference.entity';
import { User } from '../users/entities/user.entity';
import { Location } from '../location/entities/location.entity';
import { UnitType } from '../unit-type/entities/unit-type.entity';
import { MailerModule } from '../mailer/mailer.module';
import { AnalyticModule } from 'src/analytic/analytic.module';
import { WebhookModule } from 'src/core/webhook/webhook.module';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { EmailTemplate } from 'src/email-template/entities/email-template.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserPreference,
      User,
      Location,
      UnitType,
      UserConfig,
      EmailTemplate,
    ]),
    MailerModule,
    AnalyticModule,
    WebhookModule,
  ],
  controllers: [UserPreferenceController],
  providers: [UserPreferenceService],
})
export class UserPreferenceModule {}
