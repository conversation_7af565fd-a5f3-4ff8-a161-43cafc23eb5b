import {
  IsArray,
  IsEmail,
  IsEnum,
  IsString,
  registerDecorator,
  ValidateNested,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { EUserPreferenceType } from '../enums/user-preference.enum';
import { Type } from 'class-transformer';

// Custom validator to check if data is valid
function IsValidData(validationOptions?: ValidationOptions) {
  return function(object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidData',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (Array.isArray(value)) {
            return value.every((item) => typeof item === 'string');
          } else if (typeof value === 'object' && value !== null) {
            return typeof value.from === 'number' && typeof value.to === 'number';
          }
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be an array of strings or an object with 'from' and 'to' numbers`;
        },
      },
    });
  };
}


export class OptionsDto {
  @IsEnum(EUserPreferenceType)
  preferenceType?: EUserPreferenceType;

  @IsValidData({
    message:
      'Data must be either an array of strings or an object with from and to properties',
  })
  data: any;
}

export class CreateUserPreferenceDto {
  @IsString()
  phone: string;

  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OptionsDto)
  options: OptionsDto[];
}
