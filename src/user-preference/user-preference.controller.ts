import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Headers,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UserPreferenceService } from './user-preference.service';
import { CreateUserPreferenceDto } from './dto/create-user-preference.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { QueryUserPreferenceDto } from './dto/query-user-preference.dto';
import { Public } from '../iam/authentication/decorators/auth.decorator';
import { RequireHeader } from '../core/decorators/require-header.decorator';
import { AccessRoles } from '../iam/authentication/decorators/role.decorator';
import { Roles } from '../users/entities/user.entity';
import { RolesGuard } from '../iam/authentication/guards/authentication/role.guard';

@Controller('user-preference')
@UseGuards(RolesGuard)
export class UserPreferenceController {
  constructor(private readonly userPreferenceService: UserPreferenceService) {}

  @Post()
  @Public()
  @RequireHeader('user-domain')
  create(
    @Headers('user-domain') domain: string,
    @Body() body: CreateUserPreferenceDto,
  ) {
    return this.userPreferenceService.create(domain, body);
  }

  @Get()
  @AccessRoles(Roles.ADMIN)
  findAll(@Query() query: QueryUserPreferenceDto) {
    return this.userPreferenceService.findAll(query);
  }

  @Get('by-agency')
  @AccessRoles(Roles.AGENCY)
  findAllByAgency(
    @ActiveUser('sub') userId: string,
    @Query() query: QueryUserPreferenceDto,
  ) {
    return this.userPreferenceService.findAllByAgency(userId, query);
  }

  @Delete(':id')
  delete(
    @Param('id') id: string,
    @ActiveUser('sub') userId: string,
    @ActiveUser('role') role: string,
  ) {
    return this.userPreferenceService.delete(userId, role, id);
  }
}
