import CrudEntity from 'src/core/entities/crud.entity';
import { Project } from 'src/project/entities/project.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity()
export class UserPreference extends CrudEntity {
  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ nullable: true })
  options?: string;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn()
  user: User;
}
