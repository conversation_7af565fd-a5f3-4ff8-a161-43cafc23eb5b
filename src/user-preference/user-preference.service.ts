import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserPreference } from './entities/user-preference.entity';
import { CreateUserPreferenceDto } from './dto/create-user-preference.dto';
import { Roles, User } from '../users/entities/user.entity';
import { Location } from '../location/entities/location.entity';
import { EUserPreferenceType } from './enums/user-preference.enum';
import { UnitType } from '../unit-type/entities/unit-type.entity';
import {
  createPaginationResponse,
  getPaginationOption,
} from '../core/utils/pagination.util';
import { QueryUserPreferenceDto } from './dto/query-user-preference.dto';
import { MailerService } from '../mailer/mailer.service';
import { EMAIL_TEMPLATE } from '../mailer/enums/mailer.enum';
import { AnalyticService } from 'src/analytic/analytic.service';
import * as moment from 'moment';
import { WebhookService } from 'src/core/webhook/webhook.service';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { formatSgPhoneNumber } from 'src/core/utils/common.util';

@Injectable()
export class UserPreferenceService {
  constructor(
    @InjectRepository(UserPreference)
    private readonly repo: Repository<UserPreference>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Location)
    private readonly locationRepo: Repository<Location>,
    @InjectRepository(UnitType)
    private readonly unitTypeRepo: Repository<UnitType>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    private readonly mailerService: MailerService,
    private readonly analyticService: AnalyticService,
    private readonly webhookService: WebhookService,
  ) {}

  async create(
    userDomain: string,
    body: CreateUserPreferenceDto,
  ): Promise<UserPreference> {
    try {
      const user = await this.userRepo.findOne({
        where: {
          config: {
            domains: { name: userDomain },
          },
        },
      });

      if (!user) {
        throw new BadRequestException('User not found');
      }
      const options = this.mergeAndRemoveDuplicates(body?.options);
      if (!options.length) {
        throw new BadRequestException('Options cannot be empty');
      }

      for (const option of options as any) {
        const preferenceType = option?.preferenceType;
        switch (preferenceType) {
          case EUserPreferenceType.Location:
            const locationData = [];
            for (const data of option.data) {
              const location = await this.locationRepo.findOne({
                where: {
                  slug: data,
                },
              });
              if (location) {
                locationData.push({
                  id: location.id,
                  name: location.name,
                  slug: location.slug,
                });
              }
            }
            option.data = locationData;
            break;
          case EUserPreferenceType.UnitType:
            const unitTypeData = [];
            for (const data of option.data) {
              const unitType = await this.unitTypeRepo.findOne({
                where: {
                  slug: data,
                },
              });
              if (unitType) {
                unitTypeData.push({
                  id: unitType.id,
                  name: unitType.title,
                  slug: unitType.slug,
                });
              }
            }
            option.data = unitTypeData;
            break;
          default:
        }
      }

      const userPreference = this.repo.create({
        name: body.name,
        email: body.email,
        phone: body.phone,
        options: JSON.stringify(options),
        user,
      });

      const location = options.find(
        (option: any) => option.preferenceType === EUserPreferenceType.Location,
      ) as any;
      let unitType = options.find(
        (option: any) => option.preferenceType === EUserPreferenceType.UnitType,
      ) as any;
      unitType = unitType ? unitType.data.map((item) => item.name.en) : null;
      const range = options.find(
        (option: any) => option.preferenceType === EUserPreferenceType.Range,
      ) as any;

      const submissionDate = moment()
        .utcOffset(8)
        .format('HH:mm on MMM DD, YYYY');

      await this.mailerService.sendMail(
        {
          to: body.email,
          subject: 'Welcome to Project SG',
          html: EMAIL_TEMPLATE.USER_PREFERENCE_WELCOME,
        },
        {
          name: body.name,
        },
      );

      const agencyEmails = [user.email, ...(user.additionalEmail ?? [])];
      await Promise.all(
        agencyEmails.map((email) => {
          this.mailerService.sendMail(
            {
              to: email,
              subject: `Application information was sent at ${submissionDate}`,
              html: EMAIL_TEMPLATE.USER_PREFERENCE,
            },
            {
              submissionDate,
              fullName: body.name,
              phone: formatSgPhoneNumber(body.phone),
              email: body.email,
              location: location ? location?.data : [],
              unitType: unitType,
              rangeFrom: range
                ? new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(range.data.from)
                : null,
              rangeTo: range
                ? new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(range.data.to)
                : null,
            },
          );
        }),
      );

      if (range?.data) {
        this.analyticService.incrementPriceRangeEntriesCount({
          minPrice: range.data.from,
          maxPrice: range.data.to,
        });
      }

      const userConfig = await this.userConfigRepo.findOne({
        where: { userId: user.id },
      });

      this.webhookService.trigger(user, body, null, userConfig?.phoneNumber);
      return await this.repo.save(userPreference);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async findAll(query: QueryUserPreferenceDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.repo
      .createQueryBuilder('userPreference')
      .leftJoinAndSelect('userPreference.user', 'user')
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        'userPreference.name ILIKE :search OR userPreference.phone ILIKE :search OR userPreference.email ILIKE :search',
        {
          search: `%${query.search}%`,
        },
      );
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`userPreference.${key}`, order);
      });
    }

    const [rawData, total] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(rawData, total, pagination);
  }

  async findAllByAgency(userId: string, query: QueryUserPreferenceDto) {
    const pagination = getPaginationOption(query);

    const queryBuilder = this.repo
      .createQueryBuilder('userPreference')
      .leftJoinAndSelect('userPreference.user', 'user')
      .where('user.id = :userId', { userId })
      .limit(pagination.limit)
      .offset(pagination.offset);

    if (query.search) {
      queryBuilder.andWhere(
        'userPreference.name ILIKE :search OR userPreference.phone ILIKE :search OR userPreference.email ILIKE :search',
        {
          search: `%${query.search}%`,
        },
      );
    }

    if (query.sort) {
      Object.entries(query.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`userPreference.${key}`, order);
      });
    }

    const [rawData, total] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return createPaginationResponse(rawData, total, pagination);
  }

  async delete(userId: string, role: string, id: string) {
    try {
      const queryBuilder = this.repo
        .createQueryBuilder('userPreference')
        .leftJoinAndSelect('userPreference.user', 'user')
        .where('userPreference.id = :id', { id });

      if (role !== Roles.ADMIN) {
        queryBuilder.andWhere('user.id = :userId', { userId });
      }

      const userPreference = await queryBuilder.getOne();
      if (!userPreference) {
        throw new NotFoundException('User preference not found');
      }

      return await this.repo.softDelete(id);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  mergeAndRemoveDuplicates(options) {
    if (options.length) {
      const merged = {};
      options.forEach((option) => {
        const { preferenceType, data } = option;

        if (!Array.isArray(data)) {
          merged[preferenceType] = { ...option };
          return;
        }

        if (!merged[preferenceType]) {
          merged[preferenceType] = { preferenceType, data: [] };
        }

        const seenIds = new Set(
          merged[preferenceType].data.map((item) => item),
        );
        data.forEach((item) => {
          if (!seenIds.has(item)) {
            merged[preferenceType].data.push(item);
            seenIds.add(item);
          }
        });
      });
      return Object.values(merged);
    }
    return [];
  }
}
