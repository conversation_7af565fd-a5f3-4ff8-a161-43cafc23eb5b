import { Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { MailerService } from 'src/mailer/mailer.service';
import { Repository } from 'typeorm';
import { SendEmailHistory } from './entities/send-email-history.entity';

@Processor('email_marketing_queue')
export class MarketingQueue {
  constructor(
    private readonly mailerService: MailerService,

    @InjectRepository(SendEmailHistory)
    private readonly repo: Repository<SendEmailHistory>,
  ) {}

  @Process({
    name: 'sendEmail',
    concurrency: 1,
  })
  async sendEmail(job: Job<unknown>) {
    try {
      await this.mailerService.sendHtmlStringMail(
        {
          to: job.data['email'],
          subject: job.data['subject'],
          text: job.data['subject'],
        },
        job.data['body'],
      );

      await this.repo.save({
        email: job.data['email'],
        subject: job.data['subject'],
        body: job.data['body'],
      });
    } catch (error) {}
  }
}
