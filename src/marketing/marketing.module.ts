import { Modu<PERSON> } from '@nestjs/common';
import { MarketingController } from './marketing.controller';
import { MarketingService } from './marketing.service';
import { BullModule } from '@nestjs/bull';
import { RedisModule } from 'src/redis/redis.module';
import { MarketingQueue } from './marketing.queue';
import { MailerModule } from 'src/mailer/mailer.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SendEmailHistory } from './entities/send-email-history.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SendEmailHistory]),
    RedisModule,
    BullModule.registerQueue({
      name: 'email_marketing_queue',
    }),
    MailerModule,
  ],
  controllers: [MarketingController],
  providers: [MarketingService, MarketingQueue],
  exports: [MarketingService],
})
export class MarketingModule {}
