import { BadRequestException, Injectable } from '@nestjs/common';
import { MarketingSendMailDto } from './dto/send-email.dto';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { SendEmailMarketingEvent } from './events/send-email-marketing.event';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

@Injectable()
export class MarketingService {
  constructor(
    private eventEmitter: EventEmitter2,
    @InjectQueue('email_marketing_queue') private emailMarketingQueue: Queue,
  ) {}

  async sendMail(body: MarketingSendMailDto) {
    try {
      this.eventEmitter.emit(
        'send.email.marketing',
        new SendEmailMarketingEvent(body.subject, body.body, body.emails),
      );
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  @OnEvent('send.email.marketing')
  async handleSendEmailMarketing(payload: SendEmailMarketingEvent) {
    try {
      const delayInterval = 8 * 60 * 1000; // 8 minutes in milliseconds

      payload.emails.forEach((email, index) => {
        const jobDelay = delayInterval * index; // Delay based on position in the array

        this.emailMarketingQueue.add(
          'sendEmail',
          {
            subject: payload.subject,
            body: payload.body,
            email,
          },
          {
            delay: jobDelay,
            removeOnComplete: true,
            removeOnFail: true,
          },
        );
      });
    } catch (error) {
      console.log(error);
    }
  }
}
