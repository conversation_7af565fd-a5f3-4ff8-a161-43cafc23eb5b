import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { MarketingService } from './marketing.service';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { MarketingSendMailDto } from './dto/send-email.dto';

@Controller('marketing')
@UseGuards(RolesGuard)
export class MarketingController {
  constructor(private readonly marketingService: MarketingService) {}

  @Post('send-email')
  @AccessRoles(Roles.ADMIN)
  sendMail(@Body() body: MarketingSendMailDto) {
    return this.marketingService.sendMail(body);
  }
}
