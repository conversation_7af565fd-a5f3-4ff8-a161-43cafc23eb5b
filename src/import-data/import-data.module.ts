import { Module } from '@nestjs/common';
import { ImportDataController } from './import-data.controller';
import { ImportDataService } from './import-data.service';
import { MulterModule } from '@nestjs/platform-express';
import { CategoryModule } from 'src/category/category.module';
import { UnitTypeModule } from 'src/unit-type/unit-type.module';
import { LocationModule } from 'src/location/location.module';
import { DeveloperModule } from 'src/developer/developer.module';
import { AssetModule } from 'src/asset/asset.module';
import { ProjectModule } from 'src/project/project.module';
import { VirtualTourModule } from 'src/virtual-tour/virtual-tour.module';
import { FloorPlanModule } from 'src/floor-plan/floor-plan.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ImportData } from './entities/import-data.entity';
import { UnitTransactionModule } from 'src/unit-transaction/unit-transaction.module';
import { MortgageModule } from 'src/mortgage/mortgage.module';
import { Unit } from 'src/unit/entities/unit.entity';
import { UnitModule } from 'src/unit/unit.module';

@Module({
  imports: [
    MulterModule.register({
      dest: './.tmp',
    }),
    TypeOrmModule.forFeature([ImportData, Unit]),
    CategoryModule,
    UnitTypeModule,
    LocationModule,
    DeveloperModule,
    AssetModule,
    ProjectModule,
    VirtualTourModule,
    FloorPlanModule,
    UnitTransactionModule,
    MortgageModule,
    UnitModule,
  ],
  controllers: [ImportDataController],
  providers: [ImportDataService],
})
export class ImportDataModule {}
