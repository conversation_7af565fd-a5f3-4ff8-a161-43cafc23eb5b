import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { EImportStatus } from '../enums/import-status.enum';
import { EImportType } from '../enums/import-type.enum';

@Entity()
export class ImportData extends CrudEntity {
  @Column({ type: 'enum', enum: EImportType })
  type: EImportType;

  @Column({ type: 'uuid' })
  importedById: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'importedById' })
  importedBy: User;

  @Column()
  filename: string;

  @Column({ nullable: true })
  filepath: string;

  @Column({
    type: 'enum',
    enum: EImportStatus,
    default: EImportStatus.Pending,
  })
  status: EImportStatus;

  @Column({
    nullable: true,
  })
  failureReason?: string;

  @Column({ nullable: true, type: 'uuid' })
  projectId: string;
}
