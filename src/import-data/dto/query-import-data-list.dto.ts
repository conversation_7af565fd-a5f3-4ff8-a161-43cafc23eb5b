import { OmitType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional } from 'class-validator';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { EImportType } from '../enums/import-type.enum';
import { EImportStatus } from '../enums/import-status.enum';
import { EOrderType } from 'src/core/enums/sort.enum';

export enum EImportSortBy {
  name = 'name',
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  type = 'type',
  status = 'status',
}

export class QueryImportDataListDto extends OmitType(PaginationQueryDto, [
  'lang',
]) {
  @IsEnum(EImportType)
  @IsOptional()
  type?: EImportType;

  @IsEnum(EImportStatus)
  @IsOptional()
  status?: EImportStatus;

  @IsEnum(EOrderType)
  @IsOptional()
  sort?: EOrderType;

  @IsEnum(EImportSortBy)
  @IsOptional()
  sortBy?: EImportSortBy;
}
