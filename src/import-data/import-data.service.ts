import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { createReadStream, unlinkSync } from 'fs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { AssetService } from 'src/asset/asset.service';
import { CategoryService } from 'src/category/category.service';
import { CreateCategoryDto } from 'src/category/dto/create-category.dto';
import { ECategoryType } from 'src/category/enums/category-type.enum';
import {
  parseMarketSegment,
  parseTenure,
  parseTOPDate,
} from 'src/core/utils/parser.util';
import { isValidHTML } from 'src/core/utils/validator.util';
import { DeveloperService } from 'src/developer/developer.service';
import { LocationService } from 'src/location/location.service';
import { CreateProjectDto } from 'src/project/dto/create-project.dto';
import { EProjectStatus } from 'src/project/enums/project-status.enum';
import { ProjectService } from 'src/project/project.service';
import { CreateUnitTypeDto } from 'src/unit-type/dto/create-unit-type.dto';
import { UnitTypeService } from 'src/unit-type/unit-type.service';
import { IVirtualTour } from './interfaces/virtual-tour.interface';
import { Project } from 'src/project/entities/project.entity';
import { VirtualTourService } from 'src/virtual-tour/virtual-tour.service';
import { CreateVirtualTourDto } from 'src/virtual-tour/dto/create-virtual-tour.dto';
import * as csvParser from 'csv-parser';
import {
  IRawDirectCommission,
  IRawFloorPlan,
  IRawMortgage,
  IRawProject,
  IRawProjectType,
  IRawUnitTransaction,
  IRawUnitType,
  IRawUpdateShowFlatProject,
} from './interfaces/raw-data.interface';
import { FloorPlanService } from 'src/floor-plan/floor-plan.service';
import { CreateFloorPlanDto } from 'src/floor-plan/dto/create-floor-plan.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ImportData } from './entities/import-data.entity';
import { In, Repository } from 'typeorm';
import { EImportStatus } from './enums/import-status.enum';
import { EImportType } from './enums/import-type.enum';
import {
  EImportSortBy,
  QueryImportDataListDto,
} from './dto/query-import-data-list.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { EOrderType } from 'src/core/enums/sort.enum';
import { Asset } from 'src/asset/entities/asset.entity';
import { ImportProjectUnitTransactionDto } from './dto/import-unit-transaction.dto';
import { isUUID } from 'class-validator';
import { CreateUnitTransactionDto } from 'src/unit-transaction/dto/create-unit-transaction.dto';
import { EUnitStatus } from 'src/unit-transaction/enums/unit-status.enum.dto';
import { UnitTransactionService } from 'src/unit-transaction/unit-transaction.service';
import { UpdateProjectDto } from 'src/project/dto/update-project.dto';
import { JSDOM } from 'jsdom';
import { MortgageService } from 'src/mortgage/mortgage.service';
import { CreateMortgageDto } from 'src/mortgage/dto/create-mortgage.dto';
import { Unit } from 'src/unit/entities/unit.entity';
import { UnitService } from 'src/unit/unit.service';

@Injectable()
export class ImportDataService {
  private readonly importDelay = 10000;
  private isRunning: boolean = false;
  constructor(
    @InjectRepository(ImportData) private readonly repo: Repository<ImportData>,
    @InjectRepository(Unit) private readonly unitRepo: Repository<Unit>,

    private readonly categoryService: CategoryService,
    private readonly unitTypeService: UnitTypeService,
    private readonly locationService: LocationService,
    private readonly developerService: DeveloperService,
    private readonly assetService: AssetService,
    private readonly projectService: ProjectService,
    private readonly virtualTourService: VirtualTourService,
    private readonly floorPlanService: FloorPlanService,
    private readonly unitTransactionService: UnitTransactionService,
    private readonly mortgageService: MortgageService,

    private readonly unitService: UnitService,
  ) {}

  async onModuleInit() {
    setTimeout(() => {
      this.dequeue();
    }, 30000);
  }

  async importProjects(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.Project,
      filepath: file.path,
    });
    this.dequeue(); //
  }

  async importDirectCommission(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.DirectCommission,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importProjectType(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.Category,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importUnitType(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.UnitType,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importUnits(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.Unit,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importFloorPlan(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.FloorPlan,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importUnitTransaction(
    file: Express.Multer.File,
    userId: string,
    body: ImportProjectUnitTransactionDto,
  ) {
    await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.UnitTransaction,
      filepath: file.path,
      projectId: body?.projectId,
    });

    this.dequeue();
  }

  async importProjectUnitTransaction(
    file: Express.Multer.File,
    dto: ImportProjectUnitTransactionDto,
    userId: string,
  ) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.UnitTransaction,
      filepath: file.path,
    });
    this.dequeue();
  }

  async importMortgage(file: Express.Multer.File, userId: string) {
    const task = await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.Mortgage,
      filepath: file.path,
    });
    this.dequeue();
  }

  private async dequeue() {
    if (this.isRunning) {
      return;
    }
    const task = await this.repo.findOne({
      where: {
        status: In([EImportStatus.Pending, EImportStatus.Processing]),
      },
      order: {
        createdAt: 'ASC',
      },
    });

    if (!task) {
      console.log('Import task queue is empty'); //
      return;
    }
    this.isRunning = true;
    try {
      if (!task.filepath) {
        task.status = EImportStatus.Failed;
        task.failureReason = 'Unknown file path';
        await this.repo.save(task);
        return;
      }

      // update task status
      task.status = EImportStatus.Processing;
      await this.repo.save(task);

      const { type } = task;
      if (type === EImportType.Project) {
        await this.processImportProjects(task);
      } else if (type === EImportType.DirectCommission) {
        await this.processImportDirectCommission(task);
      } else if (type === EImportType.Category) {
        await this.processImportProjectType(task);
      } else if (type === EImportType.UnitType) {
        await this.processImportUnitType(task);
      } else if (type === EImportType.FloorPlan) {
        await this.processImportFloorPlan(task);
      } else if (type === EImportType.UnitTransaction) {
        if (task.projectId) {
          await this.processImportProjectUnitTransaction(task, task.projectId);
        } else {
          await this.processImportUnitTransaction(task);
        }
      } else if (type === EImportType.Mortgage) {
        await this.processImportMortgage(task);
      } else if (type === EImportType.Unit) {
        await this.processImportUnits(task);
      } else if (type === EImportType.Update_ShowFlat_Project) {
        await this.processUpdateShowFlatProject(task);
      } else {
        task.status = EImportStatus.Failed;
        task.failureReason = 'Unknown import type';
        await this.repo.save(task);
      }
    } catch (e) {
      console.error(e);
    } finally {
      this.isRunning = false;

      setTimeout(() => {
        this.dequeue();
      }, this.importDelay);
    }
  }

  private async processImportProjects(task: ImportData) {
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, [
            'idx',
            'project_name',
            'project_type',
            'sub_category',
            'detail',
            'location',
            'showflat_location',
            'address',
            'google_map_url',
            'tenure',
            'tenure_effect_from',
            'market_segment',
            'site_area',
            'expected_top',
            'developer',
            'status',
            'description',
            'key_points',
            'site_plan',
            'elevation_chart',
            'cover_image',
            'photos',
            'videos',
            'virtual_tour',
            'facilities',
            'amenities',
          ]);
        },
        async (record: IRawProject) => {
          const raw = this.parseProject(record);
          if (raw) {
            if (!raw.sub_category || !raw.location) {
              return;
            }

            const category = await this.categoryService.findByName(
              raw.sub_category,
            );
            if (!category) {
              return;
            }

            const location = await this.locationService.getOrCreateByName(
              raw.location,
            );

            const developer = raw.developer
              ? await this.developerService.getOrCreateByName(raw.developer)
              : undefined;

            const sitePlanImage = raw.site_plan
              ? await this.assetService.downloadAndReuploadImage(
                  raw.site_plan,
                  false,
                  true,
                )
              : undefined;

            const elevationChart = raw.elevation_chart
              ? await this.assetService.downloadAndReuploadImage(
                  raw.elevation_chart,
                  false,
                  true,
                )
              : undefined;

            console.log('Process project ', raw.project_name);
            const coverImage = raw.cover_image
              ? await this.assetService.downloadAndReuploadImage(
                  raw.cover_image,
                  false,
                  true,
                )
              : undefined;

            const photoUrls = raw.photos
              ? `${raw.photos}`.split('\n').map((i) => i.trim())
              : [];
            const photos: Asset[] = [];

            for await (const url of photoUrls) {
              const photo = await this.assetService.downloadAndReuploadImage(
                url,
                false,
                false,
              );
              photos.push(photo);
            }
            // const photos = await Promise.all(
            //   photoUrls.map((url) =>
            //     this.assetService.downloadAndReuploadImage(url, false, false),
            //   ),
            // );

            const videoUrls = raw.videos
              ? `${raw.videos}`.split('\n').map((i) => i.trim())
              : [];

            const videos: Asset[] = [];

            for await (const url of videoUrls) {
              const video =
                await this.assetService.downloadAndReuploadVideo(url);
              videos.push(video);
            }

            const medias = [
              ...photos.filter(Boolean).map((v) => v.id),
              ...videos.filter(Boolean).map((v) => v.id),
            ];

            const virtualTours = `${raw.virtual_tour}`
              .split('\n')
              .map((i) => i.trim())
              .map((item) => {
                const [name, thumbnail, url] = item
                  .split('||')
                  .map((i) => i.trim());
                const result: IVirtualTour = { name, thumbnail, url };
                return result;
              });

            const amenities = isValidHTML(raw.amenities) ? raw.amenities : null;
            const facilities = isValidHTML(raw.facilities)
              ? raw.facilities
              : null;

            // get google map url
            const dom = new JSDOM(raw.google_map_url);
            const iframeElement =
              dom.window.document?.querySelector('iframe') ?? null;
            const iframeSrc = iframeElement?.getAttribute('src') ?? null;
            const dto: CreateProjectDto = {
              name: raw.project_name,
              area: +(raw.site_area?.replace(/[^\d.+-]/g, '') || 0),
              tenure: parseTenure(raw.tenure),
              categoryId: category.id,
              detail: raw.detail,
              locationId: location.id,
              showflatLocation: raw.showflat_location,
              address: raw.address,
              googleMapUrl: iframeSrc,
              tenureEffectFrom: !!raw.tenure_effect_from
                ? moment(raw.tenure_effect_from, 'DD/MM/YYYY').toDate()
                : undefined,
              marketSegment: parseMarketSegment(raw.market_segment),
              expectedTop: parseTOPDate(raw.expected_top),
              developerId: developer?.id,
              status:
                raw.status === 'Completed'
                  ? EProjectStatus.Completed
                  : EProjectStatus.ByBUC,
              description: raw.description,
              keyPoint: raw.key_points,
              amenityHtml: amenities,
              facilityHtml: facilities,
              photoId: coverImage?.id,
              sitePlanImageId: sitePlanImage?.id,
              elevationChartId: elevationChart?.id,
              medias,
            };

            const existed = await this.projectService.findByName(dto.name);

            const project = existed
              ? await this.projectService.update(existed.id, dto)
              : await this.projectService.create(dto);

            console.log(existed ? 'updated' : 'created', project.name);

            await this.updateProjectVirtualTours(project, virtualTours);
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportProjectType(task: ImportData) {
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, [
            'no',
            'project_type_category',
            'sub_category',
          ]);
        },
        async (record: IRawProjectType) => {
          const raw = this.parseProjectType(record);
          if (raw) {
            const exist = await this.categoryService.findByName(raw.name);
            if (exist) {
              console.log('update', exist.name);
              return await this.categoryService.update(exist.id, raw);
            }
            console.log('create', raw.name);
            return await this.categoryService.create(raw);
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportUnitType(task: ImportData) {
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, [
            'no',
            'title',
            'bedroom',
            'study',
            'deluxe',
            'premium',
            'private_lift',
            'compact',
            'compact_plus',
            'duplex',
            'penthouse',
            'utility',
            'guest',
          ]);
        },
        async (record: IRawUnitType) => {
          const raw = this.parseUnitType(record);
          if (raw) {
            const exist = await this.unitTypeService.findByTitle(raw.title);
            if (exist) {
              console.log('update', exist.title);
              return await this.unitTypeService.update(exist.id, raw);
            }
            console.log('create', raw.title);
            return await this.unitTypeService.create(raw);
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportFloorPlan(task: ImportData) {
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, [
            'entry_no',
            'project_name',
            'unit_type',
            'floor_plan',
            'floor_plan_image',
            'size',
            'available_quantity',
            'total_quantity',
            'min_price',
            'max_price',
          ]);
        },
        async (record: IRawFloorPlan) => {
          const raw = this.parseFloorPlan(record);
          if (raw) {
            const project = await this.projectService.findByName(
              raw.project_name,
            );
            if (!project) {
              console.log('project not found', raw.project_name);
              return;
            }
            const unitType = await this.unitTypeService.findByTitle(
              raw.unit_type,
            );
            if (!unitType) {
              return;
            }

            const photo = raw.floor_plan_image
              ? await this.assetService.downloadAndReuploadImage(
                  raw.floor_plan_image,
                )
              : undefined;

            const exist = await this.floorPlanService.findByName(
              project.id,
              unitType.id,
              raw.floor_plan,
            );
            const dto: CreateFloorPlanDto = {
              area: +(raw.size?.replace(/[^\d.+-]/g, '') || 0),
              name: raw.floor_plan,
              projectId: project.id,
              unitTypeId: unitType.id,
              availableUnits: +raw.available_quantity || 0,
              totalUnits: +raw.total_quantity || 0,
              maxPrice: +(raw.max_price?.replace(/[^\d.+-]/g, '') || 0),
              minPrice: +(raw.min_price?.replace(/[^\d.+-]/g, '') || 0),
              photoId: photo?.id,
            };

            if (exist) {
              console.log('update', exist.name);
              return await this.floorPlanService.update(exist.id, dto);
            }
            console.log('create', dto.name);
            return await this.floorPlanService.create(dto);
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportProjectUnitTransaction(
    task: ImportData,
    projectId: string,
  ) {
    try {
      const project = await this.projectService.findById(projectId);
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, ['unit', 'status', 'timestamp']);
        },
        async (row: IRawUnitTransaction) => {
          if (!row.unit || !row.status || !row.timestamp) {
            return;
          }

          const [name, rawUnitType] = row.unit.split(' - ');

          if (!name) {
            console.log('skip', name, row);
            return;
          }

          const unitType = await this.unitTypeService.findByTitle(rawUnitType);

          const timestamp = moment.parseZone(
            row.timestamp,
            'DD/MM/YYYY HH:mm',
            true,
          );

          const dto: CreateUnitTransactionDto = {
            projectId: project.id,
            status:
              row.status === 'AVAILABLE'
                ? EUnitStatus.Available
                : EUnitStatus.Sold,
            timestamp: timestamp.toDate(),
            unitName: name,
            unitTypeId: unitType?.id,
          };

          const exist = await this.unitTransactionService.findByName(
            name,
            project.id,
          );

          if (exist) {
            console.log(
              'update',
              exist.unitName,
              project.name.en,
              unitType?.title?.en,
            );
            await this.unitService.updateByProjectAndUnitName(
              project.id,
              dto.unitName,
              dto.status,
            );
            await this.unitTransactionService.countUnits(exist.id);
            return await this.unitTransactionService.update(exist.id, dto);
          }
          console.log(
            'create',
            dto.unitName,
            project.name.en,
            unitType?.title?.en,
          );
          const created = await this.unitTransactionService.create(dto);
          await this.unitService.updateByProjectAndUnitName(
            project.id,
            dto.unitName,
            dto.status,
          );
          await this.unitTransactionService.countUnits(created.id);
          return created;
        },
      );

      await this.unitService.syncDuplicates();
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportUnitTransaction(task: ImportData) {
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          console.log(record);
          return (
            _.isEqual(record, ['project', 'unit', 'status', 'timestamp']) ||
            _.isEqual(record, ['project_name', 'unit', 'status', 'date_time'])
          );
        },
        async (row: IRawUnitTransaction) => {
          let projectName = row.project ?? row.project_name;
          let date = row.timestamp ?? row.date_time;

          if (!projectName || !row.unit || !row.status || !date) {
            return;
          }

          let cleanedString = projectName
            .replace(/[\u4e00-\u9fff]/g, '')
            .trim(); // Remove chinese chars

          const project = await this.projectService.findByName(cleanedString);

          if (!project) {
            return;
          }

          const [name] = row.unit.split('\n');

          const timestamp = moment.parseZone(date, 'DD/MM/YYYY HH:mm', true);

          const dto: CreateUnitTransactionDto = {
            projectId: project.id,
            status:
              row.status === 'AVAILABLE'
                ? EUnitStatus.Available
                : EUnitStatus.Sold,
            timestamp: timestamp.toDate(),
            unitName: name,
          };

          const exist = await this.unitTransactionService.findByName(
            name,
            project.id,
          );
          if (exist) {
            console.log('update', exist.unitName, project.name.en);
            await this.unitService.updateByProjectAndUnitName(
              project.id,
              dto.unitName,
              dto.status,
            );
            await this.unitTransactionService.countUnits(exist.id);
            return await this.unitTransactionService.update(exist.id, dto);
          }
          console.log('create', dto.unitName, project.name.en);
          const created = await this.unitTransactionService.create(dto);
          await this.unitService.updateByProjectAndUnitName(
            project.id,
            dto.unitName,
            dto.status,
          );
          await this.unitTransactionService.countUnits(created.id);
          return created;
        },
      );
      await this.unitService.syncDuplicates();
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    await this.repo.save(task);
  }

  async processImportUnits(task: ImportData) {
    let line = 1;
    let failureReasons = '';
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return _.isEqual(record, ['project', 'unit', 'status', 'timestamp']);
        },
        async (row: IRawUnitTransaction) => {
          if (!row.project || !row.unit || !row.status || !row.timestamp) {
            return;
          }

          let failureReason = 'line ' + line + ': ';
          let isFailed = false;
          line++;

          console.log(
            'processImportUnits',
            row.project,
            row.unit,
            row.status,
            row.timestamp,
          );

          const project = await this.projectService.findByName(row.project);

          if (!project) {
            isFailed = true;
            failureReason =
              failureReason + ' project ' + row.project + ' not found, ';
            failureReasons = failureReasons + failureReason;
            return;
          }

          const [name] = row.unit.split('\n');

          const existedUnits = await this.unitRepo.findBy({
            name,
            projectId: project.id,
          });

          if (existedUnits.length > 0) {
            for (const unit of existedUnits) {
              await this.unitRepo.update(
                { id: unit.id },
                {
                  status:
                    row.status === 'AVAILABLE'
                      ? EUnitStatus.Available
                      : EUnitStatus.Sold,
                  updatedAt: moment(row.timestamp, 'DD/MM/YYYY HH:mm').toDate(),
                },
              );
            }
          } else {
            isFailed = true;
            failureReason =
              failureReason +
              ' unit ' +
              name +
              ' not found in project ' +
              row.project +
              ',';
          }

          if (isFailed) {
            failureReasons = failureReasons + failureReason;
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }

    task.failureReason = (task.failureReason || '').concat(failureReasons);
    console.log('processImportUnits - task.failureReason', task.failureReason);
    await this.repo.save(task);
  }

  async processImportDirectCommission(task: ImportData) {
    const requiredHeaders = ['project_name', 'direct_commission'];
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return requiredHeaders.every((header) => record.includes(header));
        },
        async (record: IRawDirectCommission) => {
          const raw = this.parseDirectCommission(record);
          if (raw) {
            const project = await this.projectService.findByName(
              raw.project_name,
            );
            if (!project) {
              console.log('Project not found', raw.project_name);
              return;
            }
            const dto: UpdateProjectDto = {};
            if (raw.direct_commission.includes('UP TO')) {
              const commissionRange = raw.direct_commission.split('UP TO');
              dto.directCommission = parseFloat(commissionRange[1].trim());
              dto.isCommissionUpTo = true;
            } else {
              dto.directCommission = parseFloat(raw.direct_commission.trim());
              dto.isCommissionUpTo = false;
            }
            return await this.projectService.update(project.id, dto);
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }

    await this.repo.save(task);
  }

  async processImportMortgage(task: ImportData) {
    const requiredHeaders = [
      'type',
      'bank',
      'package',
      '1st_year',
      '2nd_year',
      '3rd_year',
      'thereafter',
    ];
    try {
      const ids = await this.mortgageService.getIds();
      await this.processCsvFile(
        task.filepath,
        (record) => {
          return requiredHeaders.every((header) => record.includes(header));
        },
        async (record: IRawMortgage) => {
          const raw = this.parseMortgage(record);
          if (raw) {
            const mortgage = await this.mortgageService.findBankToGetBankLogo(
              raw.bank,
            );
            const dto: CreateMortgageDto = { mortgageType: raw.type };
            dto.firstYear = await this.parseStringToObject(raw['1st_year']);
            dto.secondYear = await this.parseStringToObject(raw['2nd_year']);
            dto.threeYear = await this.parseStringToObject(raw['3rd_year']);
            dto.thereAfter = await this.parseStringToObject(raw['thereafter']);
            dto.bankName = raw.bank;
            dto.package = raw.package;
            const importMortgage = await this.mortgageService.create(dto);
            if (mortgage) {
              const asset = await this.assetService.findOne(
                mortgage.bankLogoId,
              );
              if (asset) {
                const bankLogo = await this.assetService.cloneImage(asset.urls);
                await this.mortgageService.update(importMortgage.id, {
                  bankLogoId: bankLogo.id,
                });
              }
            }
          }
        },
      );
      await this.mortgageService.deleteByArrayId(ids);
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }

    await this.repo.save(task);
  }

  parseProjectType(record: IRawProjectType): CreateCategoryDto | undefined {
    if (+record.no) {
      return {
        name: record.sub_category,
        shortname: record.sub_category,
        type:
          record.project_type_category.toLowerCase() ===
          ECategoryType.Residential
            ? ECategoryType.Residential
            : ECategoryType.Commercial,
      };
    }
  }

  parseUnitType(record: IRawUnitType): CreateUnitTypeDto | undefined {
    if (+record.no) {
      return {
        title: record.title,
        bedRoomCount: +record.bedroom || 0,
        hasStudyRoom: record.study === 'Yes',
        isDeluxe: record.deluxe === 'Yes',
        isPremium: record.premium === 'Yes',
        isPrivateLift: record.private_lift === 'Yes',
        isCompact: record.compact === 'Yes',
        isCompactPlus: record.compact_plus === 'Yes',
        isDuplex: record.duplex === 'Yes',
        isPenthouse: record.penthouse === 'Yes',
        isUtility: record.utility === 'Yes',
        hasGuest: record.guest === 'Yes',
      };
    }
  }

  parseProject(record: IRawProject) {
    if (+record.idx) {
      const raw = {} as IRawProject;
      Object.keys(record).forEach((k) => {
        raw[k] = `${record[k]}`.trim();
      });

      return raw;
    }
  }

  parseFloorPlan(record: IRawFloorPlan) {
    if (+record.entry_no) {
      return record;
    }
  }

  parseDirectCommission(record: IRawDirectCommission) {
    if (record.project_name) {
      return record;
    }
  }

  parseMortgage(record: IRawMortgage) {
    if (record.type && record.bank && record.package) {
      return record;
    }
  }

  async processCsvFile<TRaw, TResult>(
    filepath: string,
    validateHeader: (record: any) => boolean,
    processor: (record: TRaw) => Promise<TResult>,
  ) {
    return new Promise((resolve, reject) => {
      try {
        const fileStream = createReadStream(filepath);
        const maxPipe = 1;
        let currentPipe = 0;
        let headerChecked = false;

        fileStream.on('error', reject);

        const parseStream = fileStream.pipe(
          csvParser({
            mapHeaders: ({ header }) => {
              const trimmed = header.trim().replace('+', '_plus');
              if (!trimmed) {
                return 'idx';
              }
              return _.snakeCase(trimmed.toLowerCase());
            },
          }),
        );
        parseStream
          .on('headers', (headers) => {
            if (headerChecked) {
              return;
            }
            if (!validateHeader(headers)) {
              parseStream.destroy(
                new BadRequestException(
                  'Invalid file format, please check column name',
                ),
              );
            }
            headerChecked = true;
          })
          .on('end', resolve)
          .on('error', reject)
          .on('data', (data) => {
            currentPipe++;
            if (currentPipe >= maxPipe) {
              if (!parseStream.isPaused()) {
                parseStream.pause();
              }
            }
            processor(data)
              .catch(console.error)
              .finally(() => {
                currentPipe--;
                if (currentPipe < maxPipe && parseStream.isPaused) {
                  parseStream.resume();
                }
              });
          });
      } catch (err) {
        reject(err);
      }
    }).then(() => {
      unlinkSync(filepath);
    });
  }

  async updateProjectVirtualTours(
    project: Project,
    virtualTours: IVirtualTour[],
  ) {
    for await (const item of virtualTours) {
      if (!item.url) {
        continue;
      }
      try {
        const existed = await this.virtualTourService.findByName(
          project.id,
          item.name,
        );

        const dto: CreateVirtualTourDto = {
          projectId: project.id,
          name: item.name,
          thumbnailId: (
            await this.assetService.downloadAndReuploadImage(item.thumbnail)
          )?.id,
          unitTypeId: (await this.unitTypeService.findByTitle(item.name))?.id,
          url: item.url,
        };

        if (existed) {
          await this.virtualTourService.update(existed.id, dto);
          console.log('Update virtual tour', existed.id);
        } else {
          const res = await this.virtualTourService.create(dto);
          console.log('Create virtual tour', res.id);
        }
      } catch (err) {
        console.error(err);
      }
    }
  }

  async listing(query: QueryImportDataListDto) {
    const pagination = getPaginationOption(query);

    const orderBy = query.sortBy || EImportSortBy.createdAt;
    const sorting = query.sort || EOrderType.ASC;

    const queryBuilder = this.repo
      .createQueryBuilder('importData')
      .leftJoinAndSelect('importData.importedBy', 'importedBy')
      .orderBy(`importData.${orderBy}`, sorting)
      .offset(pagination.offset)
      .limit(pagination.limit);

    if (query.type) {
      queryBuilder.andWhere('importData.type = :type', {
        type: query.type,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('importData.status = :status', {
        status: query.status,
      });
    }

    const [data, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(data, total, pagination);
  }

  async detail(id: string) {
    if (!isUUID(id)) {
      throw new BadRequestException('taskId must be a UUID format');
    }

    const item = await this.repo.findOne({
      where: { id },
      relations: { importedBy: true },
    });

    if (!item) {
      throw new NotFoundException('Task is not found');
    }

    return item;
  }

  async parseStringToObject(input: string) {
    const [valueStr, text] = input.split('% ');
    const value = parseFloat(valueStr);
    return {
      text: text ? text.trim() : '',
      value: value,
    };
  }

  async updateShowFlatProject(file: Express.Multer.File, userId: string) {
    await this.repo.save({
      filename: file.originalname,
      importedById: userId,
      type: EImportType.Update_ShowFlat_Project,
      filepath: file.path,
    });
    this.dequeue();
  }

  async processUpdateShowFlatProject(task: ImportData) {
    let line = 1;
    let failureReasons = '';
    try {
      await this.processCsvFile(
        task.filepath,
        (record) => {
          const requiredFields = [
            'project_name',
            'showflat_available',
            'showflat_address',
          ];

          return requiredFields.every((field) =>
            (record || []).includes(field),
          );
        },
        async (row: IRawUpdateShowFlatProject) => {
          if (!row['project_name']) {
            return;
          }
          let failureReason = 'line ' + line + ': ';
          let isFailed = false;
          line++;
          console.log(
            'processUpdateShowFlatProject',
            row['project_name'],
            row['showflat_available'],
            row['showflat_address'],
          );
          const projectData = await this.projectService.findByName(
            row['project_name'],
          );
          if (!projectData) {
            isFailed = true;
            failureReason =
              failureReason +
              ' project ' +
              row['project_name'] +
              ' not found, ';
            failureReasons = failureReasons + failureReason;
            return;
          }
          await this.projectService.update(projectData.id, {
            showflatLocation: row['showflat_address'],
            isShowflatLocation:
              row['showflat_available'] === 'Yes' ? true : false,
          });

          if (isFailed) {
            failureReasons = failureReasons + failureReason;
          }
        },
      );
      task.status = EImportStatus.Success;
    } catch (err) {
      task.status = EImportStatus.Failed;
      task.failureReason = err.message || err.toString();
    }
    task.failureReason = (task.failureReason || '').concat(failureReasons);
    console.log(
      'processUpdateShowFlatProject - task.failureReason',
      task.failureReason,
    );
    await this.repo.save(task);
  }
}
