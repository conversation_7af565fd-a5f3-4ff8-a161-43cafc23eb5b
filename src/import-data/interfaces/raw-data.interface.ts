import { EMortgageType } from 'src/mortgage/enums/mortgage.enum';

export interface IRawUnitType {
  no: string;
  title: string;
  bedroom: string;
  study: string;
  deluxe: string;
  premium: string;
  private_lift: string;
  compact: string;
  compact_plus: string;
  duplex: string;
  penthouse: string;
  utility: string;
  guest: string;
}

export interface IRawProjectType {
  no: string;
  project_type_category: string;
  sub_category: string;
}

export interface IRawProject {
  idx: string;
  project_name: string;
  project_type: string;
  sub_category: string;
  detail: string;
  location: string;
  showflat_location: string;
  address: string;
  google_map_url: string;
  tenure: string;
  tenure_effect_from: string;
  market_segment: string;
  site_area: string;
  expected_top: string;
  developer: string;
  status: string;
  description: string;
  key_points: string;
  site_plan: string;
  elevation_chart: string;
  cover_image: string;
  photos: string;
  videos: string;
  virtual_tour: string;
  facilities: string;
  amenities: string;
}

export interface IRawFloorPlan {
  entry_no: string;
  project_name: string;
  unit_type: string;
  floor_plan: string;
  floor_plan_image: string;
  size: string;
  available_quantity: string;
  total_quantity: string;
  min_price: string;
  max_price: string;
}

export interface IRawUnitTransaction {
  unit: string;
  status: string;
  timestamp?: string;
  date_time?: string;
  project?: string;
  project_name?: string;
}

export interface IRawDirectCommission {
  project_name: string;
  direct_commission: string;
}

export interface IRawMortgage {
  type: EMortgageType;
  bank: string;
  package: string;
  '1st_year': string;
  '2nd_year': string;
  '3rd_year': string;
  thereafter: string;
}

export interface IRawUpdateShowFlatProject {
  project_name: string;
  showflat_available: 'Yes' | 'No';
  showflat_address: string;
}
