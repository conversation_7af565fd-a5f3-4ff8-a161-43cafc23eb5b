import {
  Body,
  Controller,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ImportDataService } from './import-data.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';
import { QueryImportDataListDto } from './dto/query-import-data-list.dto';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { ImportProjectUnitTransactionDto } from './dto/import-unit-transaction.dto';

@Controller('import-data')
@UseGuards(RolesGuard)
@AccessRoles(Roles.ADMIN)
export class ImportDataController {
  constructor(private readonly importDataService: ImportDataService) {}

  @Post('project')
  @UseInterceptors(FileInterceptor('file'))
  importProjects(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10485760 })], // 10MB
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importProjects(file, userId);
  }

  @Post('project-type')
  @UseInterceptors(FileInterceptor('file'))
  importProjectTypes(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importProjectType(file, userId);
  }

  @Post('unit-type')
  @UseInterceptors(FileInterceptor('file'))
  importUnitTypes(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importUnitType(file, userId);
  }

  @Post('floor-plan')
  @UseInterceptors(FileInterceptor('file'))
  importFloorPlan(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importFloorPlan(file, userId);
  }

  @Post('unit')
  @UseInterceptors(FileInterceptor('file'))
  importUnits(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importUnits(file, userId);
  }

  @Post('unit-transaction/project')
  @UseInterceptors(FileInterceptor('file'))
  importProjectUnitTransaction(
    @ActiveUser('sub') userId: string,
    @Body() body: ImportProjectUnitTransactionDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importProjectUnitTransaction(
      file,
      body,
      userId,
    );
  }

  @Post('unit-transaction')
  @UseInterceptors(FileInterceptor('file'))
  importUnitTransaction(
    @ActiveUser('sub') userId: string,
    @Body() body: ImportProjectUnitTransactionDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importUnitTransaction(file, userId, body);
  }

  @Get()
  listing(@Query() query: QueryImportDataListDto) {
    return this.importDataService.listing(query);
  }

  @Get(':id')
  detail(@Param('id') id: string) {
    return this.importDataService.detail(id);
  }

  @Post('direct-commission')
  @UseInterceptors(FileInterceptor('file'))
  importDirectCommission(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importDirectCommission(file, userId);
  }

  @Post('mortgage')
  @UseInterceptors(FileInterceptor('file'))
  importMortgage(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.importMortgage(file, userId);
  }

  @Put('update-showflat-project')
  @UseInterceptors(FileInterceptor('file'))
  updateShowFlatProject(
    @ActiveUser('sub') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10485760 }), // 10MB
          new FileTypeValidator({
            fileType: '.csv',
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file?: Express.Multer.File,
  ) {
    return this.importDataService.updateShowFlatProject(file, userId);
  }
}
