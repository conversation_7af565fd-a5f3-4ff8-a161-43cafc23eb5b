/**
 * Make object pretty
 * Ex:
 * `{ photo_id: "random-id" }` -> `{ photo : { id: "random-id" }}`
 * @param raw `{ photo_id: "random-id" }`
 * @param paths `['photo']`
 * @returns `{ photo : { id: "random-id" }}`
 */
export function beautifyObject(raw: Record<string, any>, paths: string[]) {
  const keys = Object.keys(raw);
  const prefixes = paths.map((p) => [p, `${p}_`]);
  const rootKeys = keys.filter(
    (k) => !prefixes.some(([_, prefix]) => k.startsWith(prefix)),
  );

  const res: Record<string, any> = {};

  // assign root keys
  rootKeys.forEach((k) => {
    res[k] = raw[k];
  });

  prefixes.forEach(([path, prefix]) => {
    const keyOfPaths = keys.filter((k) => k.startsWith(prefix));
    const obj: Record<string, any> = {};
    let isNullObj = true;
    keyOfPaths.forEach((originalKey) => {
      const value = raw[originalKey];
      const key = originalKey.substring(prefix.length);
      obj[key] = value ?? null;
      if (isNullObj && value !== undefined && value !== null) {
        isNullObj = false;
      }
    });

    res[path] = isNullObj ? null : obj;
  });

  return res;
}

export function diffArrays<T>(oldArr: T[], newArr: T[]) {
  const newItems: T[] = newArr.filter((item) => !oldArr.includes(item));
  const deletedItems: T[] = oldArr.filter((item) => !newArr.includes(item));
  return { newItems, deletedItems };
}

export function formatSgPhoneNumber(phoneNumber: string) {
  if (!phoneNumber) {
    return null;
  }

  // Remove any existing spaces for consistency
  const cleaned = phoneNumber.replace(/\s+/g, '');

  // Match the number format with flexible grouping based on length
  const match = cleaned.match(/(\+\d{2})(\d{1,4})(\d{0,4})/);

  if (!match) {
    return phoneNumber; // Return the original number if it doesn't match the expected pattern
  }

  // Build the formatted number with spaces
  const formatted = [match[1], match[2], match[3]].filter(Boolean).join(' ');

  return formatted;
}
