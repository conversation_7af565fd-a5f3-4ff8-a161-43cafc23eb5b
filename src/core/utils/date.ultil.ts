import * as moment from 'moment';

export const isLessThanCurrentTime = (date: Date): boolean => {
  const currentTimeInUtc = moment.utc();
  return moment.utc(date).isBefore(currentTimeInUtc);
};

export const isGreaterThanCurrentTime = (date: Date): boolean => {
  const currentTimeInUtc = moment.utc();
  return moment.utc(date).isAfter(currentTimeInUtc);
};

export const getUtcTimePlusMinutes = (minutes: number) => {
  return moment.utc().add(minutes, 'minutes').toDate();
};

export const getUtcTime = () => {
  return moment.utc().toDate();
};
