import { <PERSON><PERSON><PERSON> } from 'jsdom';
import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function isValidHTML(htmlString: string): boolean {
  try {
    // Create a new JSDOM instance with the HTML string
    const dom = new JSDOM(htmlString);
    // Access the window.document property of the JSDOM instance
    const doc = dom.window.document;

    // Check if there are parsing errors
    const parseErrors = doc.getElementsByTagName('parsererror').length > 0;

    // Return true if no parsing errors and the document has a body
    return !parseErrors && doc.body !== null;
  } catch (_error: any) {
    // Handle any exceptions (e.g., invalid HTML string)
    return false;
  }
}

export function IsJsonArrayString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isJsonArrayString',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed);
          } catch {
            return false;
          }
        },
        defaultMessage(_args: ValidationArguments) {
          return `$property must be a JSON array string`;
        },
      },
    });
  };
}

export function IsJsonParsableAsObject(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isJsonParsableAsObject',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (typeof value !== 'string') return false;
          try {
            const parsed = JSON.parse(value);
            return (
              typeof parsed === 'object' &&
              !Array.isArray(parsed) &&
              parsed !== null
            );
          } catch {
            return false;
          }
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a JSON string representing an object`;
        },
      },
    });
  };
}
