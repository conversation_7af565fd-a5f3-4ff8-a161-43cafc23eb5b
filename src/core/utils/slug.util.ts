import { kebabCase } from 'lodash';
import * as moment from 'moment';
import { Repository } from 'typeorm';

export async function generateSlug(
  title: string,
  repository: Repository<{ slug: string }>,
  hasChecked = false,
) {
  const slug = kebabCase(title.replace(/\+/, '_plus')).substring(0, 240);
  const existed = await repository.findOneBy({ slug });
  if (!hasChecked) {
    if (existed) {
      return `${slug}-${moment().unix()}`;
    }
  }
  return slug;
}
