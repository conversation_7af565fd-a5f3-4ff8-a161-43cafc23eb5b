import { BadRequestException } from '@nestjs/common';
import { JSDOM } from 'jsdom';
import { EMarketSegment } from 'src/project/enums/market-segment.enum';
import { ETenure } from 'src/project/enums/tenure.enum';

export interface Section {
  title: string;
  items: string[];
}

export function parseTOPDate(input: string): Date | null {
  const monthNames = [
    'January',
    'Jan',
    'February',
    'Feb',
    'March',
    'Mar',
    'April',
    'Apr',
    'May',
    'June',
    'Jun',
    'July',
    'Jul',
    'August',
    'Aug',
    'September',
    'Sep',
    'Sept',
    'October',
    'Oct',
    'November',
    'Nov',
    'December',
    'Dec',
  ];

  // Function to check if a string is a valid month name
  function isMonthName(month: string): boolean {
    return monthNames.some((m) => m.toLowerCase() === month.toLowerCase());
  }

  // Function to convert month name to month index (0-11)
  function monthNameToIndex(month: string): number {
    for (let i = 0; i < monthNames.length; i += 2) {
      if (
        monthNames[i].toLowerCase() === month.toLowerCase() ||
        monthNames[i + 1]?.toLowerCase() === month.toLowerCase()
      ) {
        return i / 2;
      }
    }
    return -1;
  }

  // Remove leading and trailing whitespace
  input = input.trim();

  // Regular expressions for different date formats
  const regexMMYYYY = /^(\d{2})\/(\d{4})$/;
  const regexMonthYYYY = /^([A-Za-z]+) (\d{4})$/;
  const regexTOPYYYY = /^TOP (\d{4})$/;
  const regexDayMonthYYYY = /^(\d{1,2}) ([A-Za-z]+) (\d{4})$/;
  const regexDDMMYYYY = /^(\d{2})\/(\d{2})\/(\d{4})$/;

  let match;

  // MM/YYYY format
  if ((match = input.match(regexMMYYYY))) {
    const month = parseInt(match[1], 10) - 1; // Month is 0-indexed
    const year = parseInt(match[2], 10);
    return new Date(year, month, 1);
  }

  // Month YYYY format
  if ((match = input.match(regexMonthYYYY))) {
    const month = match[1];
    const year = parseInt(match[2], 10);
    if (isMonthName(month)) {
      return new Date(year, monthNameToIndex(month), 1);
    }
  }

  // TOP YYYY format
  if ((match = input.match(regexTOPYYYY))) {
    const year = parseInt(match[1], 10);
    return new Date(year, 0, 1);
  }

  // Day Month YYYY format
  if ((match = input.match(regexDayMonthYYYY))) {
    const day = parseInt(match[1], 10);
    const month = match[2];
    const year = parseInt(match[3], 10);
    if (isMonthName(month)) {
      return new Date(year, monthNameToIndex(month), day);
    }
  }

  // DD/MM/YYYY format
  if ((match = input.match(regexDDMMYYYY))) {
    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10) - 1; // Month is 0-indexed
    const year = parseInt(match[3], 10);
    return new Date(year, month, day);
  }

  // If no format matched, return null
  return null;
}

export function parseTenure(input: string) {
  input = input.toLowerCase();

  switch (input) {
    case '99-yr':
      return ETenure.Yr99;
    case '999-yr':
      return ETenure.Yr999;
    case '101-yr':
      return ETenure.Yr101;
    case 'freehold':
      return ETenure.Freehold;
  }
}

export function parseMarketSegment(input?: string) {
  switch (input) {
    case EMarketSegment.CCR:
      return EMarketSegment.CCR;
    case EMarketSegment.OCR:
      return EMarketSegment.OCR;
    case EMarketSegment.RCR:
      return EMarketSegment.RCR;
  }
}
