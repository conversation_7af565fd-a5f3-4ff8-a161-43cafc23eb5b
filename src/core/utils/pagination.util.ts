import { PaginationQueryDto } from '../dto/pagination-query.dto';

export type PaginationOption = ReturnType<typeof getPaginationOption>;

export type AdditionalData = {
  availableUnitCount?: number;
  totalPage?: number;
  currentPage?: number;
  limit?: number;
};

export function getPaginationOption(value: PaginationQueryDto) {
  const limit = value.limit;
  const page = value.page;
  const offset = limit && page ? (page - 1) * limit : 0;

  return {
    limit,
    page,
    offset,
  };
}

export function createPaginationResponse<T>(
  data: Array<T>,
  total: number,
  option: PaginationOption,
  additionalData?: AdditionalData,
) {
  const totalPage = Math.ceil(total / option.limit);
  return {
    items: data,
    total,
    totalPage,
    currentPage: option.page,
    limit: option.limit,
    ...additionalData,
  };
}

export function createCombinedUnitTransactionResponse<T>(
  data: Array<T>,
  additionalData?: AdditionalData,
) {
  const total = data.length;

  return {
    items: data,
    total,
    totalPage: 1,
    currentPage: 1,
    limit: total,
    ...additionalData,
  };
}
