import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { HashingService } from '../../iam/hashing/hashing.service';

export function HashPasswordFactory(moduleRef: ModuleRef) {
  return createParamDecorator(async (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    if (request.body && request.body.password) {
      const hashService = moduleRef.get(HashingService, { strict: false });
      request.body.password = await hashService.hash(request.body.password);
    }
  });
}
