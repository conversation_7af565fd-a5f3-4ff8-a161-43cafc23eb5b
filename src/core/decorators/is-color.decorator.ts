import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  IsString,
} from 'class-validator';

// Custom decorator to validate color codes
export function IsColor(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isColor',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          const hexColorRegex = /^#([0-9A-F]{3}){1,2}$/i;
          const rgbaColorRegex =
            /^rgba\(\d{1,3}, \d{1,3}, \d{1,3}, (0|0?\.\d+|1(\.0)?)\)$/i;
          return hexColorRegex.test(value) || rgbaColorRegex.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid hex or rgba color code`;
        },
      },
    });
  };
}
