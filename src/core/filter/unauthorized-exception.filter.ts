import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  UnauthorizedException,
} from '@nestjs/common';
import { Response } from 'express';
import { CustomUnauthorizedException } from '../exception/custome-unauthorized.exception';
@Catch(UnauthorizedException)
export class UnauthorizedExceptionFilter implements ExceptionFilter {
  catch(
    exception: UnauthorizedException | CustomUnauthorizedException,
    host: ArgumentsHost,
  ) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // If the exception is of type CustomUnauthorizedException, get the errorId, otherwise use a default value
    const errorId =
      exception instanceof CustomUnauthorizedException
        ? exception.errorId
        : 'PL000';

    response.status(401).json({
      statusCode: 401,
      success: false,
      message: exception.message || 'Unauthorized',
      code: 'UnauthorizedException',
      errorId, // Dynamic errorId
    });
  }
}
