import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: Error, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const isHttpException = exception instanceof HttpException;

    let status = HttpStatus.INTERNAL_SERVER_ERROR;

    const errorCode =
      exception?.['code'] ??
      exception?.['response']?.['code'] ??
      exception.name;
    if (isHttpException) {
      status = exception.getStatus();
    } else if (exception?.['statusCode']) {
      status = exception?.['statusCode'];
    } else if (exception?.['response']?.['status']) {
      status = exception?.['response']?.['status'];
    }

    if (!isHttpException) {
      console.error(exception?.['code'] ?? 'UnhandledException', exception);
    }

    return response.status(status).json({
      statusCode: status,
      success: false,
      message: exception?.['response']?.message ?? exception.message,
      code: errorCode,
    });
  }
}
