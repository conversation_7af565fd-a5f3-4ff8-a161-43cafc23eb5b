import { Url } from 'src/url/entities/url.entity';
import * as crypto from 'crypto';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from 'src/users/entities/user.entity';
import { CreateContactSaleSubmissionDto } from 'src/contact-sale-submission/dto/create-contact-sale-submission.dto';
import axios from 'axios';
import { Injectable } from '@nestjs/common';
import { CreateUserPreferenceDto } from 'src/user-preference/dto/create-user-preference.dto';
import { Project } from 'src/project/entities/project.entity';

@Injectable()
export class WebhookService {
  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
  ) {}

  async trigger(
    user: User,
    body: CreateContactSaleSubmissionDto | CreateUserPreferenceDto,
    clientLink: string,
    agencyPhone: string,
    project?: Project,
  ): Promise<void> {
    const urls = await this.urlRepository.find({
      where: {
        type: 'submit-form',
      },
    });

    const webhooks = urls.map((url) => {
      const nonce = Math.random().toString(36).substring(7);
      const timestamp = Math.floor(Date.now() / 1000);
      const combineString = [nonce, timestamp, url.secretKey].sort().join('');

      const signature = crypto
        .createHash('sha1')
        .update(combineString)
        .digest('hex');

      return {
        nonce,
        timestamp,
        signature,
        url: url.name,
      };
    });

    const data = {
      agencyName: user.firstName + ' ' + user.lastName,
      agencyPhone,
      clientEmail: body.email,
      clientName: body.name,
      clientPhone: body.phone,
      clientDetailsLink: clientLink,
    };

    Promise.allSettled(
      webhooks.map((webhook) => {
        console.info(`calling to ${webhook.url}`);

        axios
          .post(webhook.url, {
            ...data,
            nonce: webhook.nonce,
            timestamp: webhook.timestamp,
            signature: webhook.signature,
            projectName: project?.name?.en ?? '-',
          })
          .catch((error) => {
            console.error(`Error posting to ${webhook.url}:`, error.message);
            return null;
          });
      }),
    );
  }
}
