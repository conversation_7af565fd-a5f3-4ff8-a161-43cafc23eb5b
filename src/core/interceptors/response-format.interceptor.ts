import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  statusCode: number;
  message: string;
  data: T;
}

@Injectable()
export class ResponseFormatInterceptor<T>
  implements NestInterceptor<T, Response<T>>
{
  constructor(private reflector: Reflector) {}
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    const skipResponseFormat = this.reflector.get<boolean>(
      'skipResponseFormat',
      context.getHandler(),
    );

    if (skipResponseFormat) {
      return next.handle();
    }

    return next.handle().pipe(
      map((data) => {
        const statusCode = context.switchToHttp().getResponse().statusCode;
        const success = statusCode < 400;
        return {
          statusCode,
          success,
          message:
            this.reflector.get<string>(
              'response_message',
              context.getH<PERSON><PERSON>(),
            ) || (success ? 'Success' : 'Error'),
          data: data ?? null,
        };
      }),
    );
  }
}
