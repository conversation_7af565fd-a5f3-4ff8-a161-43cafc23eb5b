import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

@Injectable()
export class RequireHeaderGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const headerName = this.reflector.getAllAndOverride<string[]>(
      'require-header',
      [context.getHandler(), context.getClass()],
    );

    if (headerName) {
      const request = context.switchToHttp().getRequest();
      const value = request.headers['user-domain'];
      if (value) {
        return true;
      }
      throw new BadRequestException(`Header '${headerName}' is required`);
    }
    return true;
  }
}
