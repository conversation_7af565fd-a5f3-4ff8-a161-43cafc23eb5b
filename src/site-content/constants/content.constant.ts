import { readFileSync } from 'fs';
import * as path from 'path';

const resourcePath = path.join(__dirname, '..', '..', '..', 'resources');

function getContent(filename: string) {
  try {
    return readFileSync(path.join(resourcePath, 'site-contents', filename))
      .toString()
      .replace(/\n/g, '');
  } catch (e) {
    console.error(e);
    return '';
  }
}

export const SiteContents = Object.freeze({
  'privacy-policy': getContent('privacy-policy.html'),
});
