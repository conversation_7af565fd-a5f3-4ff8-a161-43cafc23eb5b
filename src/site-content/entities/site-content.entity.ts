import CrudEntity from 'src/core/entities/crud.entity';
import { User } from 'src/users/entities/user.entity';
import { Column, Entity, ManyToOne } from 'typeorm';

@Entity()
export class SiteContent extends CrudEntity {
  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User)
  user: User;

  @Column()
  contentType: string;

  @Column()
  title?: string;

  @Column({ type: 'text' })
  content: string;
}
