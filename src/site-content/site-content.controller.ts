import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Headers,
} from '@nestjs/common';
import { SiteContentService } from './site-content.service';
import { CreateSiteContentDto } from './dto/create-site-content.dto';
import { UpdateSiteContentDto } from './dto/update-site-content.dto';
import { ActiveUser } from 'src/iam/authentication/decorators/active-user.decorator';
import { RequireHeader } from 'src/core/decorators/require-header.decorator';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { CreateSiteContentAdminDto } from './dto/create-site-content-admin.dto';
import { UpdateSiteContentAdminDto } from './dto/update-site-content-admin.dto';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';

@Controller('site-content')
export class SiteContentController {
  constructor(private readonly siteContentService: SiteContentService) {}

  @AccessRoles(Roles.ADMIN)
  @Patch('admin/trigger-system-page')
  async triggerSystemPage(@Body() body: { pageName: string }) {
    return this.siteContentService.triggerSystemPage(body.pageName);
  }

  @Post()
  async create(
    @ActiveUser('sub') userId: string,
    @Body() createSiteContentDto: CreateSiteContentDto,
  ) {
    return this.siteContentService.create(userId, createSiteContentDto);
  }

  @Post('admin')
  async createByAdmin(@Body() createSiteContentDto: CreateSiteContentAdminDto) {
    return this.siteContentService.createByAdmin(createSiteContentDto);
  }

  @Patch('admin/:id')
  async updateByAdmin(
    @Param('id') id: string,
    @Body() updateSiteContentDto: UpdateSiteContentAdminDto,
  ) {
    return this.siteContentService.updateByAdmin(id, updateSiteContentDto);
  }

  @Get('admin')
  async findAllByAdmin() {
    return this.siteContentService.findAllByAdmin();
  }

  @Get('admin/:contentType')
  async findOneByContentTypeAdmin(@Param('contentType') contentType: string) {
    return this.siteContentService.findOneByContentTypeAdmin(contentType);
  }

  @Get()
  findAll(@ActiveUser('sub') userId: string) {
    return this.siteContentService.findAll(userId);
  }

  @Get('by-domain/:contentType')
  @RequireHeader('User-Domain')
  @Public()
  findOneByContentType(
    @Headers('User-Domain') domain: string,
    @Param('contentType') contentType: string,
  ) {
    return this.siteContentService.getOneByContentTypeAndDomain(
      domain,
      contentType,
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.siteContentService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSiteContentDto: UpdateSiteContentDto,
  ) {
    return this.siteContentService.update(id, updateSiteContentDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.siteContentService.remove(id);
  }
}
