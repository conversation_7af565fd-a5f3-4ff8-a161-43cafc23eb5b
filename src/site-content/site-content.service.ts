import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { CreateSiteContentDto } from './dto/create-site-content.dto';
import { UpdateSiteContentDto } from './dto/update-site-content.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { SiteContent } from './entities/site-content.entity';
import { Repository } from 'typeorm';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { RedisService } from 'src/redis/redis.service';
import * as objectHash from 'object-hash';
import { Roles, User } from 'src/users/entities/user.entity';
import { SiteContents } from './constants/content.constant';
import { SiteContentAdmin } from './entities/site-content-admin.entity';
import { UpdateSiteContentAdminDto } from './dto/update-site-content-admin.dto';
import { CreateSiteContentAdminDto } from './dto/create-site-content-admin.dto';
import { formatSgPhoneNumber } from 'src/core/utils/common.util';

@Injectable()
export class SiteContentService {
  constructor(
    @InjectRepository(UserConfig)
    private readonly userConfigRepo: Repository<UserConfig>,

    @InjectRepository(SiteContent)
    private readonly siteContentRepo: Repository<SiteContent>,
    private readonly redisService: RedisService,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    @InjectRepository(SiteContentAdmin)
    private readonly siteContentAdminRepo: Repository<SiteContentAdmin>,
  ) {}

  async create(userId: string, createSiteContentDto: CreateSiteContentDto) {
    const item = await this.siteContentRepo.findOneBy({
      userId,
      contentType: createSiteContentDto.contentType,
    });

    if (item) {
      throw new UnprocessableEntityException(
        'A content with this type has been existed',
      );
    }

    await this.redisService.deletePattern('site-content@*');

    return await this.siteContentRepo.save({
      ...createSiteContentDto,
      userId,
    });
  }

  async createByAdmin(createSiteContentDto: CreateSiteContentAdminDto) {
    const item = await this.siteContentAdminRepo.findOneBy({
      contentType: createSiteContentDto.contentType,
    });

    if (item) {
      throw new UnprocessableEntityException(
        'A content with this type has been existed',
      );
    }

    await this.redisService.deletePattern('site-content@*');

    return await this.siteContentAdminRepo.save(createSiteContentDto);
  }

  async updateByAdmin(
    id: string,
    updateSiteContentDto: UpdateSiteContentAdminDto,
  ) {
    const item = await this.siteContentAdminRepo.findOneBy({ id });

    if (!item) {
      throw new NotFoundException('Site content not found');
    }

    await this.redisService.deletePattern('site-content@*');

    await this.siteContentRepo.update(
      { contentType: item.contentType },
      updateSiteContentDto,
    );

    return await this.siteContentAdminRepo.save({
      ...item,
      ...updateSiteContentDto,
    });
  }

  async findAllByAdmin() {
    return await this.siteContentAdminRepo.find();
  }

  async findAll(userId: string) {
    return await this.siteContentRepo.findBy({ userId });
  }

  async findOne(id: string) {
    const item = await this.siteContentRepo.findOneBy({ id });
    const userConfig = await this.userConfigRepo.findOneBy({
      userId: item.userId,
    });
    const user = await this.userRepo.findOneBy({ id: item.userId });

    const params = {
      name: user?.firstName + ' ' + user?.lastName,
      email: user?.email || userConfig?.agencyEmail,
      phone: formatSgPhoneNumber(user?.phone || userConfig?.phoneNumber || ''),
      company: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
    };

    if (!item) {
      throw new NotFoundException('Site content not found');
    }

    // const resolvedContent = item.content.replace(

    if (item?.content) {
      const renderTemplate = (
        template: string,
        params: Record<string, string>,
      ): string => {
        return template.replace(/\{\{(.*?)\}\}/g, (_, key) => {
          return params[key.trim()] || '';
        });
      };

      item.content = renderTemplate(item.content, params);
    }

    return item;
  }

  async getOneByContentTypeAndDomain(domain: string, contentType: string) {
    const userConfig = await this.userConfigRepo
      .createQueryBuilder('userConfig')
      .leftJoin('userConfig.domains', 'domains')
      .where('domains.name = :domain', { domain })
      .getOne();

    if (!userConfig) {
      throw new BadRequestException('Domain not found');
    }

    const user = await this.userRepo.findOneBy({ id: userConfig.userId });

    const params = {
      name: user?.firstName + ' ' + user?.lastName,
      email: user?.email || userConfig?.agencyEmail,
      phone: formatSgPhoneNumber(user?.phone || userConfig?.phoneNumber || ''),
      company: userConfig?.salesTeamInfo?.currentEa || 'PROJECT SG',
    };

    const hashKey = objectHash({ configId: userConfig.id, contentType });

    return await this.redisService.cacheWrapper(
      `site-content@:${hashKey}`,
      async () => {
        const item = await this.siteContentRepo.findOne({
          where: {
            userId: userConfig.userId,
            contentType,
          },
        });

        if (item?.content) {
          const renderTemplate = (
            template: string,
            params: Record<string, string>,
          ): string => {
            return template.replace(/\{\{(.*?)\}\}/g, (_, key) => {
              return params[key.trim()] || '';
            });
          };

          item.content = renderTemplate(item.content, params);
        }

        return item;
      },
    );
  }

  async findOneByContentTypeAdmin(contentType: string) {
    return await this.siteContentAdminRepo.findOneBy({
      contentType,
    });
  }

  async update(id: string, updateSiteContentDto: UpdateSiteContentDto) {
    const item = await this.findOne(id);

    if (!item) {
      throw new NotFoundException('Site content not found');
    }

    await this.redisService.deletePattern('site-content@*');

    return await this.siteContentRepo.save({
      ...item,
      ...updateSiteContentDto,
    });
  }

  async remove(id: string) {
    const item = await this.findOne(id);

    await this.redisService.deletePattern('site-content@*');

    return await this.siteContentRepo.softRemove(item);
  }

  async generateDefaultSiteContent(userId: string) {
    const item = await this.userRepo.findOne({
      where: {
        id: userId,
        role: Roles.AGENCY,
      },
    });

    if (!item) {
      throw new NotFoundException('User not a agency');
    }

    const siteContents = await this.siteContentAdminRepo.find();

    const userSiteContents = siteContents.map((content) => ({
      userId,
      contentType: content.contentType,
      content: content.content,
      title: content.title,
    }));

    await this.siteContentRepo.save(userSiteContents);
  }

  async triggerSystemPage(pageName: string) {
    const agencies = await this.userRepo.find({
      where: {
        role: Roles.AGENCY,
      },
    });

    if (agencies.length <= 0) return;

    const page = await this.siteContentAdminRepo.findOne({
      where: { contentType: pageName },
    });

    if (!page) return;

    try {
      await Promise.all(
        agencies.map(async (agency) => {
          const existPage = await this.siteContentRepo.findOne({
            where: { userId: agency.id, contentType: page.contentType },
          });

          if (existPage) {
            await this.siteContentRepo.update(
              { id: existPage.id },
              { content: page.content },
            );
          } else {
            await this.siteContentRepo.save({
              userId: agency.id,
              contentType: page.contentType,
              content: page.content,
              title: page.title,
            });
          }
        }),
      );
    } catch (error) {
      throw new BadRequestException(error);
    }
  }
}
