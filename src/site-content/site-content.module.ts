import { Modu<PERSON> } from '@nestjs/common';
import { SiteContentService } from './site-content.service';
import { SiteContentController } from './site-content.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SiteContent } from './entities/site-content.entity';
import { UserConfig } from 'src/user-config/entities/user-config.entity';
import { RedisModule } from 'src/redis/redis.module';
import { User } from 'src/users/entities/user.entity';
import { SiteContentAdmin } from './entities/site-content-admin.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SiteContent, UserConfig, User, SiteContentAdmin]),
    RedisModule,
  ],
  controllers: [SiteContentController],
  providers: [SiteContentService],
  exports: [SiteContentService],
})
export class SiteContentModule {}
