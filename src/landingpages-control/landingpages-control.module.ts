import { Module } from '@nestjs/common';
import { LandingpagesControlService } from './landingpages-control.service';
import { LandingpagesControlController } from './landingpages-control.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LandingpagesControl } from './entities/landingpages-control.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LandingpagesControl])],
  controllers: [LandingpagesControlController],
  providers: [LandingpagesControlService],
})
export class LandingpagesControlModule {}
