import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { LandingpagesControlService } from './landingpages-control.service';
import { CreateLandingpagesControlDto } from './dto/create-landingpages-control.dto';
import { UpdateLandingpagesControlDto } from './dto/update-landingpages-control.dto';

@Controller('landingpages-control')
export class LandingpagesControlController {
  constructor(
    private readonly landingpagesControlService: LandingpagesControlService,
  ) {}

  @Post()
  create(@Body() createLandingpagesControlDto: CreateLandingpagesControlDto) {
    return this.landingpagesControlService.create(createLandingpagesControlDto);
  }

  @Get()
  findAll() {
    return this.landingpagesControlService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.landingpagesControlService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateLandingpagesControlDto: UpdateLandingpagesControlDto,
  ) {
    return this.landingpagesControlService.update(
      +id,
      updateLandingpagesControlDto,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.landingpagesControlService.remove(+id);
  }
}
