import { Injectable } from '@nestjs/common';
import { CreateLandingpagesControlDto } from './dto/create-landingpages-control.dto';
import { UpdateLandingpagesControlDto } from './dto/update-landingpages-control.dto';

@Injectable()
export class LandingpagesControlService {
  create(createLandingpagesControlDto: CreateLandingpagesControlDto) {
    return 'This action adds a new landingpagesControl';
  }

  findAll() {
    return `This action returns all landingpagesControl`;
  }

  findOne(id: number) {
    return `This action returns a #${id} landingpagesControl`;
  }

  update(id: number, updateLandingpagesControlDto: UpdateLandingpagesControlDto) {
    return `This action updates a #${id} landingpagesControl`;
  }

  remove(id: number) {
    return `This action removes a #${id} landingpagesControl`;
  }
}
