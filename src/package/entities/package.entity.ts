import CrudEntity from 'src/core/entities/crud.entity';
import { XeroPayment } from 'src/xero-payment/entities/xero-payment.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import { EPackageStatus } from '../enums/package-status.enum';
import { EPackageDiscountType } from '../enums/package-discount-type.enum';

@Entity()
export class Package extends CrudEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ nullable: true })
  numberOfCredit?: number;

  /**
   * unit day
   */
  @Column({ nullable: true })
  duration?: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  discount?: number;

  @Column({
    type: 'enum',
    enum: EPackageDiscountType,
    nullable: true,
  })
  discountType?: EPackageDiscountType;

  @Column({
    type: 'enum',
    enum: EPackageStatus,
    nullable: true,
  })
  status: EPackageStatus;

  @OneToMany(() => XeroPayment, (transaction) => transaction.package)
  transactions: XeroPayment[];

  @Column({ nullable: true })
  isPopular?: boolean;

  @Column({ nullable: true, default: false })
  isHidden?: boolean;

  @Column({ nullable: true, unique: true })
  code?: string;

  @Column({ nullable: true, default: 0 })
  position?: number;
}
