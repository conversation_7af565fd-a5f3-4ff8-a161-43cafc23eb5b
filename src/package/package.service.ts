import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOneOptions, FindOptionsWhere, IsNull, Repository } from 'typeorm';
import { Package } from './entities/package.entity';
import { CreatePackageDto } from './dto/create-package.dto';
import { QueryPackageDto } from './dto/query-package.dto';
import {
  createPaginationResponse,
  getPaginationOption,
} from 'src/core/utils/pagination.util';
import { UpdatePackageDto } from './dto/update-package.dto';
import { EPackageStatus } from './enums/package-status.enum';

@Injectable()
export class PackageService {
  constructor(
    @InjectRepository(Package)
    private readonly packageRepository: Repository<Package>,
  ) {}

  async getActivePackages(): Promise<Package[]> {
    return await this.packageRepository.find({
      where: { status: EPackageStatus.ACTIVE },
    });
  }

  async findPackageById(packageId: string): Promise<Package> {
    return await this.packageRepository.findOne({
      where: { id: packageId, deletedAt: IsNull() },
    });
  }

  async create(dto: CreatePackageDto): Promise<Package> {
    if (dto.code) {
      const isExists = await this.packageRepository.existsBy({
        code: dto.code,
      });
      if (isExists) {
        throw new ConflictException('Package code already exists');
      }
    }
    return await this.packageRepository.save(dto);
  }

  async list(queryPackageDto: QueryPackageDto) {
    const { filter } = queryPackageDto;
    const pagination = getPaginationOption(queryPackageDto);

    const queryBuilder = this.packageRepository
      .createQueryBuilder('package')
      .skip(pagination.offset)
      .take(pagination.limit);

    if (filter) {
      if (filter.name)
        queryBuilder.andWhere('package.name = :name', { name: filter.name });

      if (filter.description)
        queryBuilder.andWhere('package.description = :description', {
          description: filter.description,
        });

      if (filter.price)
        queryBuilder.andWhere('package.price = :price', {
          price: filter.price,
        });

      if (filter.numberOfCredit)
        queryBuilder.andWhere('package.numberOfCredit = :numberOfCredit', {
          numberOfCredit: filter.numberOfCredit,
        });

      if (filter.duration)
        queryBuilder.andWhere('package.duration = :duration', {
          duration: filter.duration,
        });

      if (filter.discount)
        queryBuilder.andWhere('package.discount = :discount', {
          discount: filter.discount,
        });

      if (filter.discountType)
        queryBuilder.andWhere('package.discountType = :discountType', {
          discount: filter.discountType,
        });

      if (filter.status)
        queryBuilder.andWhere('package.status = :status', {
          status: filter.status,
        });

      if (typeof filter.isPopular === 'boolean')
        queryBuilder.andWhere('package.isPopular = :isPopular', {
          isPopular: filter.isPopular,
        });

      if (typeof filter.isHidden === 'boolean')
        queryBuilder.andWhere('package.isHidden = :isHidden', {
          isHidden: filter.isHidden,
        });

      if (filter.code)
        queryBuilder.andWhere('package.code = :code', {
          code: filter.code,
        });
    }

    if (queryPackageDto.search) {
      queryBuilder.andWhere(
        `package.name LIKE :search 
         OR package.description LIKE :search 
         OR CAST(package.price AS CHAR) LIKE :search 
         OR CAST(package.numberOfCredit AS CHAR) LIKE :search 
         OR CAST(package.duration AS CHAR) LIKE :search 
         OR CAST(package.discount AS CHAR) LIKE :search 
         OR CAST(package.status AS CHAR) LIKE :search`,
        { search: `%${queryPackageDto.search}%` },
      );
    }

    if (queryPackageDto.sort) {
      Object.entries(queryPackageDto.sort).forEach(([key, order]) => {
        queryBuilder.addOrderBy(`package.${key}`, order);
      });
    }

    const [packages, total] = await queryBuilder.getManyAndCount();

    return createPaginationResponse(packages, total, pagination);
  }

  async getPackage(query: FindOptionsWhere<Package>) {
    const { code, id, duration } = query;
    const where: FindOptionsWhere<Package> = { deletedAt: IsNull() };
    if (id) {
      where.id = id;
    }
    if (code) {
      where.code = code;
    }
    if (duration) {
      where.duration = duration;
    }
    const pkg = await this.packageRepository.findOne({ where });

    if (!pkg) {
      throw new NotFoundException('Package not found');
    }

    return pkg;
  }

  async updatePackage(id: string, updatePackageDto: UpdatePackageDto) {
    const pkg = await this.packageRepository.findOne({ where: { id } });

    if (!pkg) {
      throw new NotFoundException('Package not found');
    }

    if (updatePackageDto.code) {
      const isExists = await this.packageRepository.existsBy({
        code: updatePackageDto.code,
      });
      if (isExists) {
        throw new ConflictException('Package code already exists');
      }
    }

    return await this.packageRepository.save({ ...pkg, ...updatePackageDto });
  }

  async deletePackage(id: string) {
    const pkg = await this.packageRepository.findOne({ where: { id } });

    if (!pkg) {
      throw new NotFoundException('Package not found');
    }

    return await this.packageRepository.softDelete(id);
  }
}
