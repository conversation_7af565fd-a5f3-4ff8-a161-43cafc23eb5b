import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { EPackageStatus } from '../enums/package-status.enum';
import { EPackageDiscountType } from '../enums/package-discount-type.enum';

export class CreatePackageDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber()
  @IsNotEmpty()
  price: number;

  @IsOptional()
  @IsNumber()
  numberOfCredit?: number;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @IsOptional()
  @IsNumber()
  discount: number;

  @IsOptional()
  @IsEnum(EPackageDiscountType)
  discountType: EPackageDiscountType;

  @IsEnum(EPackageStatus)
  @IsNotEmpty()
  status: EPackageStatus;

  @IsOptional()
  @IsBoolean()
  isPopular?: boolean;

  @IsOptional()
  @IsBoolean()
  isHidden?: boolean;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsNumber()
  position?: number;
}
