import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationQueryDto } from 'src/core/dto/pagination-query.dto';
import { IsSortObject } from 'src/common/decorators/is-sort-object.decorator';
import { EPackageStatus } from '../enums/package-status.enum';
import { Package } from '../entities/package.entity';
import { EPackageDiscountType } from '../enums/package-discount-type.enum';

class PackageFilters
  implements
    Partial<
      Pick<
        Package,
        | 'name'
        | 'description'
        | 'price'
        | 'numberOfCredit'
        | 'duration'
        | 'discount'
        | 'status'
        | 'discountType'
        | 'isPopular'
        | 'isHidden'
        | 'code'
      >
    >
{
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsNumber()
  price?: number;

  @IsOptional()
  @IsNumber()
  numberOfCredit?: number;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsNumber()
  discount?: number;

  @IsOptional()
  @IsEnum(EPackageDiscountType)
  discountType?: EPackageDiscountType;

  @IsOptional()
  @IsEnum(EPackageStatus)
  status?: EPackageStatus;

  @IsOptional()
  @Transform((val) => {
    const value = val.obj['isPopular'];
    if (value == 'true') {
      return true;
    } else if (value == 'false') {
      return false;
    }
    return value;
  })
  isPopular?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform((val) => {
    const value = val.obj['isHidden'];
    if (value == 'true') {
      return true;
    } else if (value == 'false') {
      return false;
    }
    return value;
  })
  isHidden?: boolean;

  @IsOptional()
  @IsString()
  code?: string;
}

export class QueryPackageDto extends PaginationQueryDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PackageFilters)
  filter?: PackageFilters;

  @IsOptional()
  @Validate(IsSortObject)
  sort: { [key: string]: 'ASC' | 'DESC' };

  @IsString()
  @IsOptional()
  search?: string;
}
