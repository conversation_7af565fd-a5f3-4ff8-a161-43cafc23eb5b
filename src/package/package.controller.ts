import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Public } from 'src/iam/authentication/decorators/auth.decorator';
import { PackageService } from './package.service';
import { CreatePackageDto } from './dto/create-package.dto';
import { Package } from './entities/package.entity';
import { QueryPackageDto } from './dto/query-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { RolesGuard } from 'src/iam/authentication/guards/authentication/role.guard';
import { AccessRoles } from 'src/iam/authentication/decorators/role.decorator';
import { Roles } from 'src/users/entities/user.entity';

@Controller('package')
@UseGuards(RolesGuard)
export class PackageController {
  constructor(private readonly packageService: PackageService) {}

  @Post()
  @AccessRoles(Roles.ADMIN)
  async create(@Body() createPackageDto: CreatePackageDto): Promise<Package> {
    return this.packageService.create(createPackageDto);
  }

  @Get()
  @Public()
  async list(@Query() queryPackageDto: QueryPackageDto) {
    return this.packageService.list(queryPackageDto);
  }

  @Get('code/:code')
  @Public()
  async getPackageByCode(@Param('code') code: string) {
    return this.packageService.getPackage({ code });
  }

  @Get(':id')
  @Public()
  async getPackage(@Param('id') id: string) {
    return this.packageService.getPackage({ id });
  }

  @Put(':id')
  @AccessRoles(Roles.ADMIN)
  async updatePackage(
    @Param('id') id: string,
    @Body() updatePackageDto: UpdatePackageDto,
  ) {
    return this.packageService.updatePackage(id, updatePackageDto);
  }

  @Delete(':id')
  @AccessRoles(Roles.ADMIN)
  async deletePackage(@Param('id') id: string) {
    return this.packageService.deletePackage(id);
  }
}
